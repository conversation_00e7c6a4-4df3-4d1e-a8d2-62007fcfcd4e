
import { useCallback, useEffect, useRef, useState } from "react";
import { io } from "socket.io-client";

export const useSocket = (url, opts) => {
  const [connected, setConnected] = useState(false);
  const [reconnecting, setReconnecting] = useState(false);
  const socketRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const baseReconnectDelay = 1000;

  useEffect(() => {
    const { auth, autoConnect = true, onConnect, onDisconnect, onError } = opts || {};

    if (!auth?.token) {
      console.error("Socket: Missing auth token");
      onError?.(new Error("Missing auth token"));
      return;
    }

    if (!socketRef.current) {
      const socket = io(url, {
        auth,
        autoConnect,
        transports: ["websocket"],
        reconnection: false, // Disable built-in reconnection to manage manually
      });

      socketRef.current = socket;

      socket.on("connect", () => {
        console.log("Socket connected:", socket.id);
        setConnected(true);
        setReconnecting(false);
        reconnectAttempts.current = 0;
        onConnect?.(socket);
      });

      socket.on("disconnect", (reason) => {
        console.log("Socket disconnected:", reason);
        setConnected(false);
        if (reason !== "io client disconnect") {
          setReconnecting(true);
          handleReconnect();
        }
        onDisconnect?.(reason);
      });

      socket.on("connect_error", (error) => {
        console.error("Socket connect error:", error.message);
        setReconnecting(true);
        onError?.(error);
        if (error.message.includes("Too many connection attempts")) {
          setTimeout(() => handleReconnect(), baseReconnectDelay * 2 ** reconnectAttempts.current);
        }
      });

      if (autoConnect) {
        socket.connect();
      }
    }

    const handleReconnect = () => {
      if (reconnectAttempts.current < maxReconnectAttempts) {
        reconnectAttempts.current += 1;
        console.log(`Reconnect attempt ${reconnectAttempts.current}/${maxReconnectAttempts}`);
        socketRef.current?.connect();
      } else {
        console.error("Max reconnect attempts reached");
        setReconnecting(false);
        onError?.(new Error("Max reconnect attempts reached"));
      }
    };

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current.off();
        socketRef.current = null;
        setConnected(false);
        setReconnecting(false);
        reconnectAttempts.current = 0;
      }
    };
  }, [url, opts?.auth?.token, opts?.onConnect, opts?.onDisconnect, opts?.onError]);

  const reconnect = useCallback(() => {
    if (socketRef.current && !connected) {
      reconnectAttempts.current = 0;
      setReconnecting(true);
      socketRef.current.connect();
    }
  }, [connected]);

  return {
    socket: socketRef.current,
    connected,
    reconnecting,
    reconnect,
  };
};