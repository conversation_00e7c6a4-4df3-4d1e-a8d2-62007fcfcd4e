import { useState, Suspense } from "react";
import { Outlet } from "react-router-dom";
import posthog from "posthog-js";
import GoogleTranslate from "./components/navbar/components/GoogleTranslate";
import LoadingSpinner from "./components/ui/LoadingSpinner";

function App() {
  posthog.init("phc_3fQmBanpGrx91Ik4ZFcZQXGUmU5Pi58vfdrwG91Ybzi", {
    api_host: "https://app.posthog.com"
  });

  return (
    <div>
      <Suspense fallback={<LoadingSpinner size="lg" />}>
        <Outlet />
      </Suspense>
      {/* <GoogleTranslate /> */}
    </div>
  );
}

export default App;
