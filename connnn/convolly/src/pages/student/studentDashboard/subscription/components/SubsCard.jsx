import React, { useState } from "react";
import img from "@/assets/images/tutor1.png";
import tick from "@/assets/images/studentDashboard/tickIcon.png";
import CancelSubscriptionModal from "./CancelSubscriptionModal";
import RefundRequestModal from "./RefundRequestModal";
import PauseSubscriptionModal from "./PauseSubscriptionModal";

const SubsCard = ({ subscription }) => {
	const [showCancelModal, setShowCancelModal] = useState(false);
	const [showRefundModal, setShowRefundModal] = useState(false);
	const [showPauseModal, setShowPauseModal] = useState(false);

	const handleCloseModal = () => {
		setShowCancelModal(false);
		setShowRefundModal(false);
		setShowPauseModal(false);
	};

	// Return early if no subscription data
	if (!subscription) {
		return (
			<div className="border rounded-md p-4 flex flex-col h-full">
				<div className="animate-pulse">
					<div className="border-b pb-4">
						<div className="flex justify-between">
							<div className="w-8 h-8 bg-gray-200 rounded-full"></div>
							<div className="w-16 h-8 bg-gray-200 rounded"></div>
						</div>
						<div className="flex justify-between mt-2">
							<div className="w-20 h-4 bg-gray-200 rounded"></div>
							<div className="w-24 h-4 bg-gray-200 rounded"></div>
						</div>
					</div>
					<div className="mt-4 space-y-4">
						<div className="w-32 h-6 bg-gray-200 rounded"></div>
						<div className="space-y-2">
							<div className="w-full h-4 bg-gray-200 rounded"></div>
							<div className="w-full h-4 bg-gray-200 rounded"></div>
							<div className="w-3/4 h-4 bg-gray-200 rounded"></div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Extract data from subscription object with fallbacks
	const tutorName = subscription.tutorId
		? `${subscription.tutorId.firstname || ''} ${subscription.tutorId.lastname || ''}`.trim() || 'Tutor'
		: 'Tutor';
	const tutorAvatar = subscription.tutorId?.avatar || img;
	const lessonsPerWeek = subscription.lessonsPerWeek || 1;
	const remainingLessons = subscription.remainingLessons || 0;
	const monthlyPrice = subscription.monthlyPrice || 0;
	const lessonsPerMonth = lessonsPerWeek * 4;
	const canRefund = subscription.billing?.canRequestRefund || false;
	const canCancel = subscription.billing?.canCancel || false;
	const status = subscription.status || 'unknown';

	// Handle edge cases
	if (lessonsPerWeek < 1 || lessonsPerWeek > 5) {
		console.warn('Invalid lessonsPerWeek value:', lessonsPerWeek);
	}

	// Status styling
	const getStatusColor = (status) => {
		switch (status) {
			case 'active': return 'text-green-600 bg-green-100';
			case 'paused': return 'text-yellow-600 bg-yellow-100';
			case 'cancelled': return 'text-red-600 bg-red-100';
			case 'incomplete': return 'text-gray-600 bg-gray-100';
			default: return 'text-gray-600 bg-gray-100';
		}
	};

	console.log(subscription);

	return (
		<>
			<div className="border rounded-md p-4 flex flex-col h-full">
				<div className="border-b pb-4">
					<div className="flex justify-between">
						<img
							src={tutorAvatar}
							className="w-8 h-8 object-cover rounded-full"
							alt={tutorName}
						/>
						<button
							onClick={() => setShowRefundModal(true)}
							disabled={!canRefund}
							className={`px-2 py-1 sm:px-4 sm:py-1 border rounded-lg transition-colors ${
								canRefund
									? 'border-primary hover:bg-primary hover:text-white'
									: 'border-gray-300 text-gray-400 cursor-not-allowed'
							}`}
						>
							Refund
						</button>
					</div>
					<div className="flex text-[#4B5563] text-sm mt-2 justify-between">
						<div className="flex items-center space-x-2">
							<p>{tutorName}</p>
							<span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
								{status.charAt(0).toUpperCase() + status.slice(1)}
							</span>
						</div>
						<p>{remainingLessons} lessons left</p>
					</div>
				</div>

				<div className="mt-4 flex-grow">
					<div className="text-[#4B5563] space-y-4 text-md">
						<p className="text-[#1A1A40] text-2xl mb-2">
							{lessonsPerWeek} lesson{lessonsPerWeek > 1 ? 's' : ''} per week
						</p>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>That's {lessonsPerMonth} lessons every 4 weeks at ${monthlyPrice.toFixed(2)}</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p className="text-md">
								Schedule your {lessonsPerMonth} lessons for anytime during the 4 weeks period
							</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>Change your tutor for free at anytime</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>Cancel or pause subscription at anytime</p>
						</div>
						<div className="flex">
							<img src={tick} className="h-4 w-4 mr-2 pt-1" alt="tick icon" />
							<p>Change the duration of your classes at anytime</p>
						</div>
					</div>
				</div>

				<div className="mt-6 pt-4 flex justify-end space-x-2 text-md">
					<button
						onClick={() => setShowPauseModal(true)}
						disabled={status !== 'active'}
						className={`border px-2 py-1 sm:px-4 sm:py-1 rounded-md transition-colors ${
							status === 'active'
								? 'hover:bg-gray-50'
								: 'text-gray-400 cursor-not-allowed'
						}`}
					>
						Pause
					</button>
					<button
						onClick={() => setShowCancelModal(true)}
						disabled={!canCancel}
						className={`border text-white px-2 py-1 sm:px-4 sm:py-2 rounded-md transition-colors ${
							canCancel
								? 'bg-primary hover:bg-primary-dark'
								: 'bg-gray-400 cursor-not-allowed'
						}`}
					>
						Cancel
					</button>
				</div>
			</div>

			{/* Modals */}
			{showCancelModal && (
				<div className="fixed inset-0 p-4 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<CancelSubscriptionModal onClose={handleCloseModal} />
				</div>
			)}

			{showRefundModal && (
				<div className="fixed p-4 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<RefundRequestModal onClose={handleCloseModal} />
				</div>
			)}

			{showPauseModal && (
				<div className="fixed p-4 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<PauseSubscriptionModal onClose={handleCloseModal} />
				</div>
			)}
		</>
	);
};

export default SubsCard;
