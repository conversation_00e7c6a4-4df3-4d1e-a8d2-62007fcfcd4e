import React, { useState } from "react";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { useDeleteStudentAccountMutation } from "@/redux/slices/accountApiSlice";

const SettingsDeleteAccount = () => {
	const [emailInput, setEmailInput] = useState("");
	const [deleteStudentAccount, { isLoading }] =
		useDeleteStudentAccountMutation();
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const navigate = useNavigate();

	const handleDelete = async (e) => {
		e.preventDefault();

		if (emailInput.trim().toLowerCase() !== user?.email?.toLowerCase()) {
			toast.error("Email does not match your account.");
			return;
		}

		try {
			await deleteStudentAccount(user?.id).unwrap();
			toast.success("Account deleted successfully.");
			// Clear session / logout and redirect
			localStorage.removeItem("userInfo");
			navigate("/");
		} catch (err) {
			toast.error("Failed to delete account.");
		}
	};

	return (
		<div className="flex flex-col w-auto md:max-w-[516px]">
			<p className="text-md sm:text-xl text-[#4B5563]">
				Deleting your account is permanent and all your account information will
				be deleted along with it. If you're sure you want to proceed, enter your
				email address below.
			</p>
			<form onSubmit={handleDelete} className="mb-2 py-4">
				<label htmlFor="email" className="text-[#1A1A40] font-semibold">
					Email Address
				</label>
				<input
					id="email"
					type="email"
					value={emailInput}
					onChange={(e) => setEmailInput(e.target.value)}
					className="w-full mt-4 p-2 border rounded-md"
					placeholder="<EMAIL>"
					required
				/>
				<button
					type="submit"
					disabled={isLoading}
					className="bg-red-600 hover:bg-red-700 transition w-full p-2 sm:p-4 mt-4 rounded-md text-white text-xl font-semibold"
				>
					{isLoading ? "Deleting..." : "Delete Account"}
				</button>
			</form>
		</div>
	);
};

export default SettingsDeleteAccount;
