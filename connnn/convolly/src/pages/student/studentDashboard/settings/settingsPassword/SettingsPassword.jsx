import { Button } from "@/components/button/button";
import Input<PERSON>ield from "@/components/inputs";
import usePost from "@/hooks/usePost";
import { useUpdateAccountPasswordMutation } from "@/redux/slices/accountApiSlice";
import React from "react";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";

const SettingsPassword = () => {
	const {
		register,
		handleSubmit,
		formState: { errors },
		watch,
		reset,
	} = useForm();

	const user = useSelector((state) => state?.app?.userInfo?.user);
	const { handlePost, isLoading } = usePost(useUpdateAccountPasswordMutation);

	const onSubmit = async (data) => {
		if (data.newPassword !== data.confirmPassword) {
			toast.error("New passwords don't match");
			return;
		}

		const response = await handlePost({
			userId: user?.id,
			currentPassword: data.currentPassword,
			newPassword: data.newPassword,
		});

		if (response?.success) {
			reset();
		}
	};

	return (
		<form
			onSubmit={handleSubmit(onSubmit)}
			className="flex flex-col w-auto md:max-w-[516px]"
		>
			<h2 className="text-xl font-semibold mb-6">Change Password</h2>

			<div className="space-y-4">
				<div>
					<label className="block mb-1 text-[18px] text-[#1A1A40] font-medium">
						Current Password
					</label>
					<InputField
						register={register}
						fieldName="currentPassword"
						fieldType="password"
						placeHolder="Enter your current password"
						isRequired={true}
						error={errors?.currentPassword?.message}
					/>
				</div>

				<div>
					<label className="block mb-1 text-[18px] text-[#1A1A40] font-medium">
						New Password
					</label>
					<InputField
						register={register}
						fieldName="newPassword"
						fieldType="password"
						placeHolder="Enter your new password"
						isRequired={true}
						error={errors?.newPassword?.message}
					/>
				</div>

				<div>
					<label className="block mb-1 text-[18px] text-[#1A1A40] font-medium">
						Confirm New Password
					</label>
					<InputField
						register={register}
						fieldName="confirmPassword"
						fieldType="password"
						placeHolder="Enter your password again"
						isRequired={true}
						validate={true}
						registerOptions={{
							pattern: {
								value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{8,}$/,
								message:
									"Password must be at least 8 characters, include uppercase, lowercase, and a special character",
							},
						}}
						error={errors?.confirmPassword?.message}
					/>
				</div>
			</div>

			<Button
				className="w-full h-[50px] mb-3"
				//   disabled={forgotPasswordLoading}
			>
				Save Changes
			</Button>
		</form>
	);
};

export default SettingsPassword;
