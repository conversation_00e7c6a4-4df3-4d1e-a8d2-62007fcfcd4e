import React from "react";
import masterCardIcon from "../../../../../assets/svgs/payments/mastercard.svg";
import visaIcon from "../../../../../assets/svgs/payments/Visa-logo.svg";
import lockIcon from "../../../../../assets/svgs/payments/square-lock-password.png";
import AddBankCardModal from "./AddBankCardModal";

const SettingsPaymentMethod = () => {
	// const [showAddCardModal, setshowLogoutModal] = React.useState(false);
	const [isModalOpen, setIsModalOpen] = React.useState(false);

	const openModal = () => {
		setIsModalOpen(true);
		console.log("button clicked");
	};
	const closeModal = () => setIsModalOpen(false);

	return (
		<div className="">
			<div className="flex p-2 flex-col w-sm w-auto md:max-w-[516px]">
				<div className="flex shadow-md p-4 rounded-md justify-between">
					<div>
						<p className="text-[#1A1A40] mb-4 font-bold text-lg sm:text-2xl">
							Credit or debit card
						</p>
						<div className="flex gap-6">
							<img src={visaIcon} alt="icons" className="w-12 h-12" />
							<img src={masterCardIcon} alt="icons" className="w-12 h-12" />
						</div>
					</div>
					<div>
						<button
							onClick={openModal}
							className="bg-primary text-md font-semibold text-sm sm:text-xl rounded-md text-white py-3 px-6"
						>
							Add Card
						</button>
					</div>
				</div>
				<div className="flex flex-row mt-4">
					<img src={lockIcon} alt="lock icon" className="w-18 h-8 pr-3" />
					<div>
						<p className="text-sm sm:text-md text-[#4B5563]">
							Convolly uses industry-standard encryption to protect your
							information
						</p>
					</div>
				</div>
			</div>

			{isModalOpen && (
				<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
					<AddBankCardModal onClose={closeModal} />
				</div>
			)}
		</div>
	);
};

export default SettingsPaymentMethod;
