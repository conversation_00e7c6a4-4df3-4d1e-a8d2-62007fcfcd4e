import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import SettingsProfile from "./settingsProfile/SettingsProfile";
import SettingsPassword from "./settingsPassword/SettingsPassword";
import SettingsPaymentMethod from "./settingsPaymentMethod/SettingsPaymentMethod";
import SettingsNotifications from "./settingNotification/SettingsNotification";
import SettingsDeleteAccount from "./deleteAccount/SettingsDeleteAccount";
import { ChevronUp, Menu } from "lucide-react";

const navItems = [
	{ id: "profile", name: "Profile" },
	{ id: "password", name: "Password" },
	{ id: "payment-method", name: "Payment Method" },
	{ id: "notification", name: "Notification" },
	{ id: "delete-account", name: "Delete Account" },
];

const StudentSettings = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const searchParams = new URLSearchParams(location.search);
	const activeTab = searchParams.get("tab") || "profile";
	const [isNavOpen, setIsNavOpen] = useState(false);
	const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);

	React.useEffect(() => {
		const handleResize = () => {
			setIsMobileView(window.innerWidth < 768);
			if (window.innerWidth >= 768) {
				setIsNavOpen(false);
			}
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const handleTabChange = (tabId) => {
		navigate(`?tab=${tabId}`, { replace: true });
		if (isMobileView) {
			setIsNavOpen(false);
		}
	};

	const toggleNav = () => {
		setIsNavOpen(!isNavOpen);
	};

	const tabComponents = {
		profile: <SettingsProfile />,
		password: <SettingsPassword />,
		"payment-method": <SettingsPaymentMethod />,
		notification: <SettingsNotifications />,
		"delete-account": <SettingsDeleteAccount />,
	};

	return (
		<div className="w-full p-4 bg-white rounded-lg flex flex-col md:flex-row gap-6">
			{/* Mobile nav header */}
			{isMobileView && (
				<div className="flex items-center bg-gray-300 p-2 rounded-md justify-between lg:hidden">
					<h2 className="text-xl font-semibold">Account Settings</h2>
					<button
						onClick={toggleNav}
						className="p-2 rounded-md hover:bg-gray-100"
					>
						{isNavOpen ? (
							<ChevronUp className="w-5 h-5" />
						) : (
							<Menu className="w-5 h-5" />
						)}
					</button>
				</div>
			)}

			{/* Navigation - hidden on mobile when collapsed */}
			{(isNavOpen || !isMobileView) && (
				<nav className="flex flex-col space-y-2 max-w-[256px]">
					{navItems.map((item) => (
						<button
							key={item.id}
							onClick={() => handleTabChange(item.id)}
							className={`w-full text-[16px] md:text-[18px] text-left px-4 py-3 rounded-md transition-colors font-semibold ${
								activeTab === item.id
									? "bg-[#EBEDF0] text-black font-medium"
									: "text-gray-700 hover:bg-gray-100"
							}`}
						>
							{item.name}
						</button>
					))}
				</nav>
			)}

			{/* Content area */}
			<div className="flex-1 sm:p-4 p-2 bg-white rounded-lg">
				{tabComponents[activeTab] || <SettingsProfile />}
			</div>
		</div>
	);
};

export default StudentSettings;
