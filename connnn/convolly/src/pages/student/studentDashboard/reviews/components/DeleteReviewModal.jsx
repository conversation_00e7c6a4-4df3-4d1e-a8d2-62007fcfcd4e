import { But<PERSON> } from "@/components/button/button";
import { X } from "lucide-react";
import React, { useState } from "react";
import { useDeleteReviewMutation } from "@/redux/slices/student/reviewApiSlice";
import { useToast } from "@/context/toastContext/toastContext";

const DeleteReviewModal = ({ review, onClose, onUpdate }) => {
	const [isDeleting, setIsDeleting] = useState(false);
	const [deleteReview] = useDeleteReviewMutation();
	const { showToast } = useToast();

	const handleDelete = async () => {
		setIsDeleting(true);

		try {
			await deleteReview({
				reviewId: review._id,
			}).unwrap();

			showToast("Review deleted successfully!", "success");
			onUpdate(); // Refresh the reviews list
			onClose();
		} catch (error) {
			console.error("Error deleting review:", error);
			showToast(
				error?.data?.message || "Failed to delete review. Please try again.",
				"error"
			);
		} finally {
			setIsDeleting(false);
		}
	};
	return (
		<div>
			<div className="sm:w-[600px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
				<div className="flex justify-between items-center pb-4 border-b border-gray-200">
					<h2 className="text-xl font-bold text-[#1A1A40]">Delete Review</h2>
					<button
						onClick={onClose}
						className="text-gray-500 hover:text-gray-700"
						disabled={isDeleting}
					>
						<X />
					</button>
				</div>

				<div className="mt-6">
					<div className="mb-8">
						<h1 className="text-xl sm:text-[22px] text-[#1A1A40] font-semibold text-center">
							Are you sure you want to delete this review?
						</h1>
						<p className="text-lg text-gray-600 text-center mt-2">
							This action cannot be undone.
						</p>
					</div>
					<div className="flex items-center justify-center gap-4">
						<Button
							onClick={onClose}
							className="flex-1 h-[50px] w-40 bg-white border border-gray-300 text-black hover:bg-gray-50 rounded-md"
							disabled={isDeleting}
						>
							Cancel
						</Button>
						<Button
							onClick={handleDelete}
							className="flex-1 h-[50px] w-40 bg-red-600 hover:bg-red-700 text-white rounded-md"
							disabled={isDeleting}
						>
							{isDeleting ? "Deleting..." : "Yes, Delete"}
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default DeleteReviewModal;
