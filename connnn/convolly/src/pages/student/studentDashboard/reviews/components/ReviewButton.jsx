import React, { useState, useRef, useEffect } from "react";
import EditOrDeleteReviewModal from "./EditOrDeleteReviewModal";

const ReviewButton = ({ review, onUpdate }) => {
	const modalRef = useRef(null);
	const buttonRef = useRef(null);
	const [showOptions, setShowOptions] = useState(false);

	const handleShowOptions = (e) => {
		e.stopPropagation();
		setShowOptions(!showOptions);
	};

	const hideOption = () => {
		setShowOptions(false);
	};

	useEffect(() => {
		const handleClickOutside = (event) => {
			// Close if clicked outside of both modal and button
			if (
				modalRef.current &&
				!modalRef.current.contains(event.target) &&
				!buttonRef.current.contains(event.target)
			) {
				hideOption();
			}
		};

		if (showOptions) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showOptions]);

	return (
		<div className="absolute right-0 top-0 z-50">
			<div>
				<button
					ref={buttonRef}
					onClick={handleShowOptions}
					className="text-2xl px-2 py-1 rounded"
					aria-expanded={showOptions}
					aria-label="Review options"
				>
					...
				</button>

				{showOptions && (
					<div ref={modalRef} className="absolute right-0 z-50 mt-1">
						<EditOrDeleteReviewModal
							review={review}
							onOptionClose={hideOption}
							onUpdate={onUpdate}
						/>
					</div>
				)}
			</div>
		</div>
	);
};

export default ReviewButton;
