import { Button } from "@/components/button/button";
import { X, Star } from "lucide-react";
import React, { useState, useEffect } from "react";
import { useUpdateReviewMutation } from "@/redux/slices/student/reviewApiSlice";
import defaultAvatar from "../../../../../assets/images/tutor1.png";
import { useToast } from "@/context/toastContext/toastContext";

const EditReviewModal = ({ review, onClose, onUpdate }) => {
	const [rating, setRating] = useState(review?.rating || 0);
	const [comment, setComment] = useState(review?.comment || "");
	const [hoveredRating, setHoveredRating] = useState(0);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const [updateReview] = useUpdateReviewMutation();
	const { showToast } = useToast();

	const targetUser = review?.targetUser || {};

	useEffect(() => {
		if (review) {
			setRating(review.rating || 0);
			setComment(review.comment || "");
		}
	}, [review]);

	const handleRatingClick = (newRating) => {
		setRating(newRating);
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (rating === 0) {
			showToast("Please select a rating", "error");
			return;
		}

		if (!comment.trim()) {
			showToast("Please provide a comment", "error");
			return;
		}

		setIsSubmitting(true);

		try {
			await updateReview({
				reviewId: review._id,
				rating,
				comment: comment.trim(),
			}).unwrap();

			showToast("Review updated successfully!", "success");
			onUpdate();
			onClose();
		} catch (error) {
			console.error("Error updating review:", error);
			showToast(
				error?.data?.message || "Failed to update review. Please try again.",
				"error"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderStars = () => {
		return Array.from({ length: 5 }, (_, index) => {
			const starValue = index + 1;
			const isActive = starValue <= (hoveredRating || rating);

			return (
				<Star
					key={index}
					size={24}
					className={`cursor-pointer transition-colors ${
						isActive
							? "fill-yellow-400 text-yellow-400"
							: "fill-gray-200 text-gray-200 hover:fill-yellow-300 hover:text-yellow-300"
					}`}
					onClick={() => handleRatingClick(starValue)}
					onMouseEnter={() => setHoveredRating(starValue)}
					onMouseLeave={() => setHoveredRating(0)}
				/>
			);
		});
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-40 z-50 flex items-center justify-center px-4">
			<div className="w-full max-w-2xl bg-white rounded-xl shadow-lg p-6 relative">
				<div className="flex justify-between items-center mb-6">
					<h2 className="text-xl font-bold text-[#1A1A40]">Edit Review</h2>
					<button
						onClick={onClose}
						className="text-gray-500 hover:text-gray-700"
						disabled={isSubmitting}
					>
						<X size={24} />
					</button>
				</div>

				<form onSubmit={handleSubmit}>
					<div className="flex flex-col sm:flex-row justify-between gap-6">
						<div className="flex items-center gap-4">
							<img
								src={targetUser.profilePicture || defaultAvatar}
								alt={targetUser.fullname || "Tutor"}
								className="w-16 h-16 rounded-md object-cover"
							/>
							<div>
								<p className="text-lg font-semibold text-[#1A1A40]">
									{targetUser.fullname || "Unknown Tutor"}
								</p>
								<p className="text-sm text-gray-500">
									Total Reviews: {targetUser.reviewStats?.totalReview || 0}
								</p>
							</div>
						</div>

						<div>
							<label className="block text-sm font-medium text-[#1A1A40] mb-2">
								Rating
							</label>
							<div className="flex gap-1">{renderStars()}</div>
						</div>
					</div>

					<div className="mt-6">
						<label className="block text-sm font-medium text-[#1A1A40] mb-2">
							Comment
						</label>
						<textarea
							value={comment}
							onChange={(e) => setComment(e.target.value)}
							className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							rows={5}
							placeholder="Write your review here..."
							disabled={isSubmitting}
						/>
					</div>

					<Button
						type="submit"
						className="mt-6 w-full h-[48px]"
						disabled={isSubmitting}
					>
						{isSubmitting ? "Updating..." : "Update Review"}
					</Button>
				</form>
			</div>
		</div>
	);
};

export default EditReviewModal;
