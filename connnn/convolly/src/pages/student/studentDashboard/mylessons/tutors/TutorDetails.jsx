import React, { useState } from "react";
import {
	format,
	addDays,
	startOfWeek,
	addWeeks,
	setHours,
	setMinutes,
	parse,
	isSameDay,
	isWithinInterval,
	parseISO,
} from "date-fns";
import rightArrow from "@/assets/svgs/rightArrow.svg";
import leftArrow from "@/assets/svgs/leftArrow.svg";
import { X } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useScheduleClassMutation } from "@/redux/slices/student/scheduleApiSlice";
import Loader from "@/components/loader/loader";
import { capitalizeWords } from "@/utils/utils";
import { useGetTutorDetailsQuery } from "@/redux/slices/student/findTutorApiSlice";
import useGet from "@/hooks/useGet";
import { useGetTutorCalendarQuery } from "@/redux/slices/student/scheduleApiSlice";

// Time slots from 00:00 to 23:30 (30 min intervals)
const timeSlots = Array.from({ length: 48 }, (_, i) => {
	const hour = Math.floor(i / 2);
	const minutes = i % 2 === 0 ? 0 : 30;
	return { hour, minutes };
});

const TutorDetails = () => {
	const navigate = useNavigate();
	const [weekOffset, setWeekOffset] = useState(0);
	const { id } = useParams();
	const [selectedSlot, setSelectedSlot] = useState(null);
	const [numberOfLessonMinute, setNumberOfLessonMinutes] = useState("50mins");

	const hasUsedFreeTrial = useSelector(
		(state) => state?.app?.userInfo?.user?.hasUsedFreeTrial
	);

	const { handlePost: handleSchedule, isLoading: schedulingClass } = usePost(
		useScheduleClassMutation
	);

	const { data: user, isLoading: gettingTutor } = useGet(
		useGetTutorDetailsQuery,
		id,
		!!id
	);

	const { data: tutorCalendar, isLoading: gettingTutorCalendar } = useGet(
		useGetTutorCalendarQuery,
		id,
		!!id
	);

	// Convert user.timeAvailable to availableTimes format
	const dayMap = {
		Sunday: 0,
		Monday: 1,
		Tuesday: 2,
		Wednesday: 3,
		Thursday: 4,
		Friday: 5,
		Saturday: 6,
	};

	const availableTimes = user?.timeAvailable.reduce((acc, slot) => {
		const dayIndex = dayMap[slot.label];
		if (!dayIndex && dayIndex !== 0) return acc;

		const start = parse(slot.from, "HH:mm", new Date());
		const end = parse(slot.to, "HH:mm", new Date());
		let current = start;

		while (current <= end) {
			acc.push({
				day: dayIndex,
				hour: current.getHours(),
				minutes: current.getMinutes(),
			});
			current = new Date(current.getTime() + 30 * 60 * 1000); // Add 30 minutes
		}
		return acc;
	}, []);

	const startDate = addWeeks(
		startOfWeek(new Date(), { weekStartsOn: 0 }),
		weekOffset
	);
	const endDate = addDays(startDate, 6);
	const formatRange = `${format(startDate, "MMM d")} – ${format(
		endDate,
		"d, yyyy"
	)}`;

	// Check if a time slot is booked (improved version)
	const isBooked = (dayIndex, time) => {
		if (!tutorCalendar?.data?.bookedSlots?.length) return false;

		const slotDate = addDays(startDate, dayIndex);
		const slotStart = new Date(slotDate);
		slotStart.setHours(time.hour, time.minutes, 0, 0);
		const slotEnd = new Date(slotStart.getTime() + 30 * 60 * 1000); // 30 min slot

		return tutorCalendar.data.bookedSlots.some((booked) => {
			const bookedStart = parseISO(booked.startDateTime);
			const bookedEnd = parseISO(booked.endDateTime);

			// Convert to UTC for comparison to avoid timezone issues
			const slotStartUTC = new Date(slotStart.toISOString());
			const slotEndUTC = new Date(slotEnd.toISOString());
			const bookedStartUTC = new Date(bookedStart.toISOString());
			const bookedEndUTC = new Date(bookedEnd.toISOString());

			return (
				(slotStartUTC >= bookedStartUTC && slotStartUTC < bookedEndUTC) ||
				(slotEndUTC > bookedStartUTC && slotEndUTC <= bookedEndUTC) ||
				(slotStartUTC <= bookedStartUTC && slotEndUTC >= bookedEndUTC)
			);
		});
	};

	const handleClickSlot = (dayIndex, time) => {
		const date = setHours(
			setMinutes(addDays(startDate, dayIndex), time.minutes),
			time.hour
		);
		setSelectedSlot(date);
	};

	const isAvailable = (dayIndex, time) => {
		const isTimeAvailable = availableTimes?.some(
			(slot) =>
				slot.day === dayIndex &&
				slot.hour === time.hour &&
				slot.minutes === time.minutes
		);

		return isTimeAvailable && !isBooked(dayIndex, time);
	};

	const scheduleClass = async () => {
		if (!selectedSlot || !user || !numberOfLessonMinute) return;

		const lessonMinutes = parseInt(numberOfLessonMinute);
		const endTime = new Date(
			selectedSlot.getTime() + lessonMinutes * 60 * 1000
		);

		const data = {
			tutorId: id,
			calendarId: user.id,
			title: `${user.teachingSubjects[0]?.title || "Lesson"} Tutoring Session`,
			description: "",
			startDateTime: selectedSlot.toISOString(),
			endDateTime: endTime.toISOString(),
			subject: user.teachingSubjects[0]?.title || "",
		};

		try {
			const response = await handleSchedule(data);
			if (response?.success) {
				setSelectedSlot(null);
				navigate("/student/my-lessons?tab=calendar");
			} else {
				console.error("Error scheduling class:", response?.message);
			}
		} catch (error) {
			console.error("Error scheduling class:", error);
		}
	};

	return (
		<div className="flex flex-col md:flex-row gap-5 items-start">
			{schedulingClass && <Loader />}

			<div className="w-full max-w-auto md:max-w-[300px] mx-auto border p-2 sm:p-3 rounded-lg flex flex-col text-center">
				<div className="w-full aspect-square max-w-[196px] mb-4 mx-auto">
					<img
						src={user?.image || "/default-avatar.png"}
						alt="tutor image"
						className="w-full h-full object-cover rounded-full"
					/>
				</div>

				<p className="text-[#4B5563] text-sm my-2">
					Your subscription with {capitalizeWords(user?.fullname)}
				</p>

				<select
					value={numberOfLessonMinute}
					onChange={(e) => setNumberOfLessonMinutes(e.target.value)}
					className="border rounded-lg text-sm outline-none p-2"
				>
					<option value="50mins">50mins (Standard lesson)</option>
					<option value="25mins">25mins</option>
				</select>

				<div className="bg-[#1FC16B1A] text-secondary p-3 text-left pb-5 mt-4">
					<p className="font-medium sm:text-lg mb-1">Cancellation policy</p>
					<p className="text-sm">
						You can cancel or reschedule lessons for free up to 12 hours before
						they start
					</p>
				</div>
			</div>

			<div className="w-full">
				{/* Header Navigation */}
				<div className="flex items-center gap-2 mb-7">
					<p
						className="border border-[#E8E8E8] p-[6px] rounded-lg px-5 cursor-pointer"
						onClick={() => setWeekOffset(0)}
					>
						Today
					</p>
					<button
						onClick={() => setWeekOffset((w) => w - 1)}
						className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
					>
						<img src={leftArrow} alt="prev" />
					</button>
					<button
						onClick={() => setWeekOffset((w) => w + 1)}
						className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
					>
						<img src={rightArrow} alt="next" />
					</button>
					<p className="md:text-xl text-md font-bold">{formatRange}</p>
				</div>

				{/* Calendar */}
				<div className="rounded-lg border overflow-x-auto">
					{/* Weekdays Header */}
					<div className="grid text-sm md:text-md grid-cols-7">
						{[...Array(7)].map((_, i) => {
							const date = addDays(startDate, i);
							const isToday =
								format(date, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");
							return (
								<div
									key={i}
									className={`text-center py-3 font-semibold md:font-bold ${
										isToday ? "bg-green-500 text-white rounded-t" : ""
									}`}
								>
									{format(date, "EEE")} <br /> {format(date, "d")}
								</div>
							);
						})}
					</div>

					{/* Time Slots */}
					{timeSlots.map((time, i) => (
						<div className="grid text-xs md:text-md grid-cols-7" key={i}>
							{[...Array(7)].map((_, dayIdx) => {
								const available = isAvailable(dayIdx, time);
								const booked = isBooked(dayIdx, time);
								const timeLabel = `${time.hour
									.toString()
									.padStart(2, "0")}:${time.minutes
									.toString()
									.padStart(2, "0")}`;

								return (
									<div
										key={dayIdx}
										className={`text-center py-4 text-xs sm:text-sm md:text-md lg:text-lg font-semibold ${
											available
												? "text-black cursor-pointer hover:bg-blue-50"
												: booked
												? "text-gray-400 bg-gray-100 cursor-not-allowed line-through"
												: "text-gray-400 cursor-not-allowed"
										}`}
										onClick={() => available && handleClickSlot(dayIdx, time)}
									>
										{timeLabel}
										{booked && (
											<div className="absolute inset-0 bg-red-100 opacity-30"></div>
										)}
									</div>
								);
							})}
						</div>
					))}
				</div>

				{selectedSlot && (
					<>
						{hasUsedFreeTrial ? (
							<div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-40 flex items-center justify-center z-50">
								<div className="bg-white p-5 sm:p-8 rounded-lg shadow-xl w-full max-w-[500px] text-center relative">
									<button
										className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
										onClick={() => setSelectedSlot(null)}
									>
										<X className="h-5 w-5" />
									</button>
									<div className="h-[150px] w-[150px] mx-auto mb-3">
										<img
											src={user?.image || "/default-avatar.png"}
											alt="tutor"
											className="w-full h-full object-cover rounded-full"
										/>
									</div>
									<p className="text-lg font-semibold mb-3">
										Continue learning with {capitalizeWords(user?.fullname)}?
									</p>
									<p className="text-sm mb-4">
										Start a monthly subscription and set up your schedule
									</p>
									<button
										className="w-full bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition mb-2"
										onClick={() =>
											navigate(
												`/student/my-lessons/tutors/${user?.id}/subscribe`,
												{
													state: user,
												}
											)
										}
									>
										Yes, subscribe
									</button>
								</div>
							</div>
						) : (
							<div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-40 flex items-center justify-center z-50">
								<div className="bg-white text-xs p-5 border rounded-lg shadow w-full max-w-[350px] relative">
									<button
										className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
										onClick={() => setSelectedSlot(null)}
									>
										<X className="h-5 w-5" />
									</button>
									<div className="flex gap-2 items-center mb-3">
										<img
											src={user?.image || "/default-avatar.png"}
											alt="tutor"
											className="w-[55px] h-[55px] rounded-full object-cover"
										/>
										<h4 className="sm:text-xl text-secondary font-bold">
											{capitalizeWords(user.fullname)}
										</h4>
									</div>
									<p className="text-secondary sm:text-lg mb-1">
										Date: {format(selectedSlot, "MMM d, yyyy")}
									</p>
									<p className="text-secondary sm:text-lg mb-2">
										Time: {format(selectedSlot, "HH:mm")}
									</p>
									<p className="text-secondary sm:text-lg mb-5">Price: $0</p>
									<Button
										className="h-[50px] w-full"
										onClick={scheduleClass}
										disabled={schedulingClass}
									>
										{schedulingClass ? "Scheduling..." : "Confirm Schedule"}
									</Button>
								</div>
							</div>
						)}
					</>
				)}
			</div>
		</div>
	);
};

export default TutorDetails;
