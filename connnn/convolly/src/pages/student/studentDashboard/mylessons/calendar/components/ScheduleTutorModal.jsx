import { X } from "lucide-react";
import React from "react";
import useGet from "@/hooks/useGet";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import img from "@/assets/svgs/userVector.svg";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";

const ScheduleTutorModal = ({ selectedSlot, onClose, onScheduleTutor }) => {
	const { data: tutors, isLoading } = useGet(useGetTutorsQuery, "");

	const navigate = useNavigate();

	return (
		<div className="" onClick={onClose}>
			{isLoading && <Loader />}

			<div
				className="bg-white rounded-xl sm:w-[959px] p-6 w-full max-w-full"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="flex justify-between items-center p-6 mb-4">
					<h3 className="text-[#1A1A40] text-[22px] font-bold">
						Select a tutor you want to schedule a class with
					</h3>
					<button
						className="text-gray-500 hover:text-gray-700"
						onClick={onClose}
					>
						<X />
					</button>
				</div>

				<div className="max-h-[60vh] px-6 overflow-y-auto">
					{tutors?.map((tutor) => (
						<div
							key={tutor.id}
							className="flex justify-between items-center py-3 border-b last:border-b-0 cursor-pointer"
							onClick={() => navigate(`/student/my-lessons/tutors/${tutor.id}`)}
						>
							<div className="flex items-center space-x-4">
								<img
									src={tutor.image || img}
									alt={tutor.fullname}
									className="w-12 h-12 rounded-full object-cover"
								/>
								<div>
									<p className="text-[#1A1A40]font-medium">{tutor.fullname}</p>
									<p className="text-gray-600 text-sm">English</p>
								</div>
							</div>

							<div className="text-right">
								<p className="text-[#1A1A40] text-sm">
									{tutor.totalLessons} lessons
								</p>
							</div>

							<div>
								<p className="text-gray-800 font-medium">
									{tutor.basePrice || "-"}
								</p>

								<p className="text-gray-600 text-xs">Per lesson</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default ScheduleTutorModal;
