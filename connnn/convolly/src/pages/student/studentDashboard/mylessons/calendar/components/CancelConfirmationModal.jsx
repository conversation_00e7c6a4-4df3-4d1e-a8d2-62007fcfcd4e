import React, { useState } from "react";
import { toast } from "react-toastify";

const predefinedReasons = [
	"Student is sick and cannot attend the lesson",
	"Unexpected emergency",
	"<PERSON><PERSON> didn’t show up",
	"Time conflict",
	"Other",
];

const CancelConfirmationModal = ({ onCancelBookingConfirm, onCancel }) => {
	const [selectedReason, setSelectedReason] = useState("");
	const [customReason, setCustomReason] = useState("");

	const handleConfirm = () => {
		const reason =
			selectedReason === "Other" ? customReason.trim() : selectedReason;

		if (!reason) {
			alert("Please select or enter a reason");
			return;
		}

		onCancelBookingConfirm({ reason });
		toast.success("Lesson cancelled successfully.");
	};

	return (
		<div
			onClick={onCancel}
			className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
		>
			<div
				onClick={(e) => e.stopPropagation()}
				className="bg-white rounded-xl p-6 w-full max-w-xl text-center"
			>
				<h2 className="text-2xl font-bold mb-2">
					Are you sure you want to cancel this lesson?
				</h2>
				<p className="mb-6 text-gray-600">
					Cancelling may affect your scheduling priority.
				</p>

				<div className="mb-4 text-left">
					<label className="block mb-2 font-medium">Select a reason:</label>
					<select
						value={selectedReason}
						onChange={(e) => setSelectedReason(e.target.value)}
						className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200"
					>
						<option value="">-- Select --</option>
						{predefinedReasons.map((reason, idx) => (
							<option key={idx} value={reason}>
								{reason}
							</option>
						))}
					</select>
				</div>

				{selectedReason === "Other" && (
					<div className="mb-4 text-left">
						<label className="block mb-2 font-medium">Please specify:</label>
						<textarea
							value={customReason}
							onChange={(e) => setCustomReason(e.target.value)}
							rows={3}
							className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200"
						></textarea>
					</div>
				)}

				<div className="flex justify-center gap-4 mt-6">
					<button
						className="border border-gray-300 py-2 px-6 rounded-md hover:bg-gray-100"
						onClick={onCancel}
					>
						Cancel
					</button>
					<button
						className="bg-[#D00416] text-white py-2 px-6 rounded-md hover:bg-red-600"
						onClick={handleConfirm}
					>
						Yes, Cancel
					</button>
				</div>
			</div>
		</div>
	);
};

export default CancelConfirmationModal;
