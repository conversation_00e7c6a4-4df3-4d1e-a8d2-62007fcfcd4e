import React, { useState } from "react";
import Upcomming<PERSON>esson from "./UpcomingLesson";
import PastLessons from "./PastLessons";
import useGet from "@/hooks/useGet";
import { useGetClassesQuery } from "@/redux/slices/student/classesApiSlice";
import Loader from "@/components/loader/loader";

const LessonsContainer = () => {
	const [activeOption, setActiveOption] = useState("UpcomingLessons");

	const { data: bookings, isLoading } = useGet(useGetClassesQuery, "");

	// Filter bookings into upcoming and past lessons based on the date
	const now = new Date();

	const upcomingLessons =
		bookings?.bookings?.filter(
			(lesson) => new Date(lesson.scheduledTime) > now
		) || [];
	const pastLessons =
		bookings?.bookings?.filter(
			(lesson) => new Date(lesson.scheduledTime) <= now
		) || [];

	return (
		<div className="w-full">
			{isLoading && <Loader />}

			{/* Toggler */}
			<div className="w-sm sm:max-w-[427px]">
				<div className="flex items-start text-[#1A1A40] h-[55px] bg-gray-100 rounded-md p-1 mx-auto">
					<button
						className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px] rounded-md text-center cursor-pointer transition-all duration-100 ${
							activeOption === "UpcomingLessons"
								? "bg-white shadow-sm"
								: "bg-transparent"
						}`}
						onClick={() => setActiveOption("UpcomingLessons")}
					>
						<div className="flex text-sm sm:text-lg font-bold text-[#1A1A40]">
							<p>Upcoming Lessons</p>
						</div>
					</button>

					<button
						className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px] rounded-md text-center cursor-pointer transition-all duration-100 ${
							activeOption === "PastLessons"
								? "bg-white shadow-sm"
								: "bg-transparent"
						}`}
						onClick={() => setActiveOption("PastLessons")}
					>
						<div className="flex text-sm sm:text-lg font-bold text-[#1A1A40]">
							<p>Past Lessons</p>
						</div>
					</button>
				</div>
			</div>

			{/* Conditional rendering based on active option */}
			<div className="pt-6">
				{activeOption === "UpcomingLessons" ? (
					<div className="space-y-4">
						{upcomingLessons.length > 0 ? (
							upcomingLessons.map((lesson) => (
								<UpcommingLesson key={lesson.id} lesson={lesson} />
							))
						) : (
							<p>No upcoming lessons scheduled</p>
						)}
					</div>
				) : (
					<div className="space-y-4">
						{pastLessons.length > 0 ? (
							pastLessons.map((lesson) => (
								<PastLessons key={lesson?.id} lesson={lesson} />
							))
						) : (
							<p>No past lessons found</p>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

export default LessonsContainer;
