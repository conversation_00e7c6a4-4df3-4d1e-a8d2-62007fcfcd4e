import React, { useState } from "react";

const LessonTogler = () => {
	const [activeOption, setActiveOption] = useState("UpcommingLessons");

	return (
		<div className="flex items-start text-[#1A1A40] h-[55px] bg-gray-100 rounded-md p-1 mx-auto">
			<button
				className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px]  rounded-md text-center cursor-pointer transition-all duration-100 ${
					activeOption === "UpcommingLessons"
						? "bg-[#FFFFFF] shadow-sm"
						: "bg-transparent"
				}`}
				onClick={() => setActiveOption("UpcommingLessons")}
			>
				<div className="flex text-lg font-bold text-[#1A1A40]">
					<p>Upcomming Lessons</p>
				</div>
			</button>

			<button
				className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px]rounded-md text-center cursor-pointer transition-all duration-100 ${
					activeOption === "PastLessons"
						? "bg-white shadow-sm"
						: "bg-transparent"
				}`}
				onClick={() => setActiveOption("PastLessons")}
			>
				<div className="flex text-lg font-bold text-[#1A1A40]">
					<p>Past Lessons</p>
				</div>
			</button>
		</div>
	);
};

export default LessonTogler;
