import React, { useEffect, useRef } from "react";
import Links from "./Links";
import { useNavigate } from "react-router-dom";
import img from "@/assets/svgs/userVector.svg";

import { useSelector } from "react-redux";

const Sidebar = ({ isOpen, toggleSidebar, setShowLogoutModal }) => {
	const sidebarRef = useRef(null);

	useEffect(() => {
		const handleClickOutside = (event) => {
			// Check if the click is on the navbar toggle button
			const navbarToggle = document.querySelector(
				".lg\\:hidden.p-1.rounded-md"
			); // Match your navbar toggle button class

			if (
				sidebarRef.current &&
				!sidebarRef.current.contains(event.target) &&
				isOpen &&
				event.target !== navbarToggle &&
				!navbarToggle?.contains(event.target)
			) {
				toggleSidebar();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isOpen, toggleSidebar]);

	const navigate = useNavigate();

	const handleRoute = () => {
		navigate("/student/my-lessons?tab=tutors");
	};

	const user = useSelector((state) => state?.app?.userInfo?.user);

	return (
		<div
			ref={sidebarRef}
			className={`fixed lg:relative z-50 lg:z-auto lg:block w-[256px] shadow-lg p-3 overflow-auto h-full bg-white transition-all duration-300 ${
				isOpen ? "left-0" : "-left-full"
			} lg:left-0`}
		>
			<div className="my-5">
				<div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
					<div className="flex items-center p-4 gap-3">
						<div className="relative">
							<img
								src={user?.img || img}
								alt={user?.fullname}
								className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-md"
							/>
						</div>

						<div>
							<h3 className="font-semibold text-gray-900">{user?.fullname}</h3>
							<p className="text-sm text-gray-600 flex items-center gap-1">
								{user?.role}
							</p>
						</div>
					</div>
				</div>
			</div>

			<div>
				<Links
					toggleSidebar={toggleSidebar}
					setShowLogoutModal={setShowLogoutModal}
				/>
			</div>

			<div className="bg-[#EBEDF0] p-2 mt-6 rounded-md mb-4">
				<div className="p-3 b-6">
					<p className="text-[18px] text-[#1A1A40]">
						Subscribe to your favourite tutor
					</p>
					<p className="text-[14px] mt-4 text-[#4B5563]">
						Love the way a tutor teaches? Stay consistent and build real
						progress by subscribing to them
					</p>
				</div>
				<button
					onClick={handleRoute}
					className="w-full mt-4 text-[18px] text-white rounded-md text-center bg-primary px-2 py-4"
				>
					Subscribe
				</button>
			</div>
		</div>
	);
};

export default Sidebar;
