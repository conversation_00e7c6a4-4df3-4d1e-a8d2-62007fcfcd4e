import TotalClasses from "@/assets/svgs/studentDashboard/totalClasses";
import TotalNoOfTutorsIcon from "@/assets/svgs/studentDashboard/totalNoOfTutorsIcon";

const MainCards = ({ dashbaordStats = {} }) => {
	// Destructure with defaults
	const {
		totalClasses = 0,
		totalTutors = 0,
		subscribedTutors = 0,
	} = dashbaordStats;

	const cards = [
		{
			Icon: TotalClasses,
			value: totalClasses,
			label: "Total classes",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: totalTutors,
			label: "Total no of tutors",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: subscribedTutors,
			label: "Total no of subscribed tutors",
		},
	];

	return (
		<div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
			{cards.map(({ label, Icon, value }, index) => (
				<div
					key={index}
					className="h-[90px] sm:h-[130px] rounded-lg border border-[#E8E8E8] bg-white shadow-xs hover:shadow-sm transition-shadow flex flex-col justify-between p-3 sm:p-4"
				>
					<div className="flex justify-between items-start">
						<Icon className="w-6 h-6 sm:w-16 sm:h-16 text-primary" />
					</div>
					<div>
						<p className="text-lg my-1 sm:text-2xl lg:text-[26px] text-[#1A1A40] font-bold leading-tight">
							{value}
						</p>
						<p className="text-xs sm:text-sm text-[#4B5563] truncate">
							{label}
						</p>
					</div>
				</div>
			))}
		</div>
	);
};

export default MainCards;
