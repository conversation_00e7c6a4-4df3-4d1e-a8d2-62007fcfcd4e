import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useMemo
} from "react";
import { useSearchParams } from "react-router-dom";
import { decryptText, encryptMessage, encryptFile } from "../utils/crypto";
import { getMessages, getUsers } from "../utils/api";
import { useSocket } from "@/hooks/useSocket";
import { debounce, removeDuplicate, safelyBind } from "../utils";
import { useSelector } from "react-redux";
import dayjs from "dayjs";
import { FiSend, FiPaperclip, FiX, FiFile } from "react-icons/fi";

const ChatRoom = ({ selected, userId, otherUser }) => {
  // State management
  const [chatMessage, setChatMessage] = useState("");
  const [chatMessages, setChatMessages] = useState([]);
  const [filePreviews, setFilePreviews] = useState([]);
  const [typing, setTyping] = useState(null);
  const [searchParams, setSearchParams] = useSearchParams();
  const [fetchingMessages, setFetchingMessages] = useState(true);
  const [isComposing, setIsComposing] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState(false);

  // Refs
  const stateRef = useRef({ isTyping: false });
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const textareaRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Get conversation ID from URL params
  const conversationId = searchParams.get("chat_cid") || "";

  // Get current user data from Redux store
  const currentUser = useSelector((state) => state?.app?.userInfo?.user);
  const accessToken = useSelector((state) => state?.app?.userInfo?.accessToken);

  // Socket configuration
  const socketOpts = useMemo(
    () => ({
      auth: {
        token: accessToken
      }
    }),
    [accessToken]
  );

  const socketApi = useSocket(
    "https://convolly-backend.onrender.com",
    currentUser ? socketOpts : undefined
  );

  const { socket, connected } = socketApi;

  // Group messages by date
  const groupedMessages = useMemo(() => {
    const groups = {};
    chatMessages.forEach((message) => {
      const date = dayjs(message.createdAt || new Date()).format("YYYY-MM-DD");
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });
    return groups;
  }, [chatMessages]);

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        120
      )}px`;
    }
  }, [chatMessage]);

  // Clean up object URLs and timeouts when component unmounts
  useEffect(() => {
    return () => {
      filePreviews.forEach((preview) => {
        URL.revokeObjectURL(preview.preview);
      });
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [filePreviews]);

  // Fetch messages when conversation changes
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        if (!currentUser) return;

        setFetchingMessages(true);
        const messages = [];

        const data = conversationId
          ? await getMessages(currentUser.id, conversationId, accessToken)
          : [];

        for (const msg of data) {
          const text = await decryptText(currentUser, msg);
          messages.push({ ...msg, text });
        }

        setChatMessages(messages);
      } catch (err) {
        console.error("Failed to fetch messages:", err);
        setChatMessages([]);
      } finally {
        setFetchingMessages(false);
      }
    };

    fetchMessages();
  }, [currentUser, conversationId, accessToken]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages]);

  // Update conversation ID in URL
  const updateConversationId = useCallback(
    (msg) => {
      setSearchParams(
        (params) => {
          params.set("chat_cid", msg.conversation.id);
          return params;
        },
        { replace: true }
      );
    },
    [setSearchParams]
  );

  // Socket event handlers
  useEffect(() => {
    if (!socket || !currentUser) return;

    console.log("Emitting join-chat-room");
    socket.emit("join-chat-room");

    // New message handler
    safelyBind(socket, "new-chat-message", async (msg) => {
      if (
        msg.conversation.id === conversationId ||
        msg.conversation.participants[otherUser?.id]
      ) {
        updateConversationId(msg);
        try {
          const text = await decryptText(currentUser, msg);
          setChatMessages((messages = []) => {
            let newMessages = [];
            const newMessage = { ...msg, text };
            if (msg.sender.id === currentUser.id) {
              newMessages = messages.map((message) =>
                message.tempId === msg.tempId ? newMessage : message
              );
            } else {
              newMessages = [...messages, newMessage];
            }
            return removeDuplicate(newMessages);
          });

          if (msg.sender.id !== currentUser.id) {
            socket.emit("chat-message-delivered", msg.id);
            const id = setTimeout(() => {
              socket.emit("chat-message-read", msg.id);
              clearTimeout(id);
            }, 4000);
          }
        } catch (err) {
          console.error("Error processing new-chat-message:", err);
        }
      }
    });

    // Message saved handler
    safelyBind(socket, "chat-message-saved", ({ tempId, message }) => {
      updateConversationId(message);
      setChatMessages((messages) =>
        messages.map((msg) =>
          msg.tempId === tempId ? { ...msg, ...message } : msg
        )
      );
    });

    // Message read handler
    safelyBind(socket, "chat-message-read", (message) => {
      setChatMessages((messages) =>
        messages.map((msg) =>
          msg.id === message.id ? { ...msg, ...message } : msg
        )
      );
    });

    // Message delivered handler
    safelyBind(socket, "chat-message-delivered", (message) => {
      setChatMessages((messages) =>
        messages.map((msg) =>
          msg.id === message.id ? { ...msg, ...message } : msg
        )
      );
    });

    // Typing indicators
    safelyBind(socket, "chat-user-typing", (from) => {
      setTyping(from);
      // Clear typing indicator after 3 seconds of inactivity
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      typingTimeoutRef.current = setTimeout(() => {
        setTyping(null);
      }, 3000);
    });

    safelyBind(socket, "chat-user-stopped-typing", () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      setTyping(null);
    });

    // Cleanup
    return () => {
      socket.off("new-chat-message");
      socket.off("chat-message-saved");
      socket.off("chat-message-read");
      socket.off("chat-message-delivered");
      socket.off("chat-user-typing");
      socket.off("chat-user-stopped-typing");
    };
  }, [socket, currentUser, otherUser, conversationId, updateConversationId]);

  // Handle file selection and preview
  const handleFileSelect = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    const newPreviews = files.map((file) => {
      if (file.type.startsWith("image/")) {
        return {
          file,
          preview: URL.createObjectURL(file),
          type: "image",
          name: file.name,
          size: file.size
        };
      } else {
        return {
          file,
          preview: null,
          type: "file",
          name: file.name,
          size: file.size
        };
      }
    });

    setFilePreviews((prev) => [...prev, ...newPreviews]);
    e.target.value = ""; // Reset file input
  };

  const removeFilePreview = (index) => {
    URL.revokeObjectURL(filePreviews[index].preview);
    setFilePreviews((prev) => prev.filter((_, i) => i !== index));
  };

  // Encrypt files before sending
  const encryptFiles = async () => {
    if (filePreviews.length === 0) return [];

    setUploadingFiles(true);
    try {
      const encryptedFiles = await Promise.all(
        filePreviews.map(async (preview) => {
          const recipients = {};

          // Encrypt for recipient
          recipients[otherUser.id] = await encryptFile(
            preview.file,
            otherUser.encryptedData.publicKey
          );

          // Encrypt for sender
          recipients[currentUser.id] = await encryptFile(
            preview.file,
            currentUser.encryptedData.publicKey
          );

          return {
            recipients,
            name: preview.name,
            extension: preview.name.split(".").pop(),
            mimetype: preview.file.type,
            size: preview.size,
            type: preview.type
          };
        })
      );
      return encryptedFiles;
    } catch (err) {
      console.error("Error encrypting files:", err);
      return [];
    } finally {
      setUploadingFiles(false);
    }
  };

  // Send message handler
  const sendChatMessage = async () => {
    if (fetchingMessages || (!chatMessage && filePreviews.length === 0)) return;

    // Encrypt files if we have any
    const filesToSend = filePreviews.length > 0 ? await encryptFiles() : [];
    if (filePreviews.length > 0 && filesToSend.length === 0) return;

    const tempId = new Date().getTime();

    try {
      const receiverEncryptedMessage = chatMessage
        ? await encryptMessage(otherUser?.encryptedData?.publicKey, chatMessage)
        : null;

      const senderEncryptedMessage = chatMessage
        ? await encryptMessage(
            currentUser?.encryptedData?.publicKey,
            chatMessage
          )
        : null;

      const message = {
        conversationId: conversationId || "",
        tempId,
        sender: {
          role: currentUser?.role,
          id: currentUser?.id,
          firstname: currentUser?.firstname,
          lastname: currentUser?.lastname,
          image: currentUser?.image
        },
        receiver: {
          role: otherUser?.role,
          id: otherUser?.id
        },
        files: filesToSend,
        recipients: {
          [currentUser?.id]: senderEncryptedMessage,
          [otherUser?.id]: receiverEncryptedMessage
        },
        text: chatMessage || (filesToSend.length > 0 ? "[File attached]" : "")
      };

      // Add to local messages immediately
      setChatMessages((messages = []) =>
        removeDuplicate([
          ...messages,
          {
            ...message,
            files: filesToSend.map((f) => ({
              ...f,
              preview: filePreviews.find((p) => p.name === f.name)?.preview
            }))
          }
        ])
      );

      // Send via socket
      socket.emit("send-chat-message", message);

      // Reset state
      setChatMessage("");
      setFilePreviews([]);
    } catch (err) {
      console.error("Error sending chat message:", err);
    }
  };

  // indicate typing
  const handleTyping = (e) => {
    if (fetchingMessages) return;

    // Update the message text
    setChatMessage(e.target.value);

    // Only emit typing events if we have a socket and other user
    if (!socket || !otherUser?.id) return;

    // Handle typing start
    if (!stateRef.current.isTyping && e.target.value.length > 0) {
      stateRef.current.isTyping = true;
      socket.emit("chat-user-typing", otherUser.id, currentUser);
    }

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop typing indication
    typingTimeoutRef.current = setTimeout(() => {
      if (stateRef.current.isTyping) {
        socket.emit("chat-user-stopped-typing", otherUser.id, currentUser);
        stateRef.current.isTyping = false;
      }
    }, 3000);
  };

  // Add these new useEffect hooks for composition events
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const handleCompositionStart = () => {
      setIsComposing(true);
      if (socket && otherUser?.id && !stateRef.current.isTyping) {
        stateRef.current.isTyping = true;
        socket.emit("chat-user-typing", otherUser.id, currentUser);
      }
    };

    const handleCompositionEnd = () => {
      // Don't immediately stop typing as user might continue typing
      setIsComposing(false);
    };

    textarea.addEventListener("compositionstart", handleCompositionStart);
    textarea.addEventListener("compositionend", handleCompositionEnd);

    return () => {
      textarea.removeEventListener("compositionstart", handleCompositionStart);
      textarea.removeEventListener("compositionend", handleCompositionEnd);
    };
  }, [socket, otherUser, currentUser]);

  const withConversation = conversationId || selected;

  return (
    <div className="w-full mx-auto border rounded-md bg-gray-50 h-full flex flex-col">
      <div className="border-b bg-white p-4 flex items-center gap-3">
        {otherUser?.image && (
          <img
            src={otherUser.image}
            alt={otherUser.firstname}
            className="w-12 h-12 rounded-full object-cover"
          />
        )}
        <div>
          <h3 className="text-xl font-semibold">
            {otherUser?.firstname} {otherUser?.lastname}
          </h3>
          <p
            className={`text-sm ${
              otherUser?.isLoggedIn ? "text-green-500" : "text-gray-500"
            }`}
          >
            {otherUser?.isLoggedIn ? "Online" : "Offline"}
          </p>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 flex flex-col p-4 h-full overflow-hidden">
        <div className="flex-1 overflow-y-auto mb-4 p-2 bg-white border rounded-md h-full">
          {withConversation ? (
            <div className="space-y-4">
              {Object.entries(groupedMessages).map(([date, messages]) => (
                <div key={date} className="space-y-2">
                  <div className="text-center text-xs text-gray-500 my-2">
                    {dayjs(date).format("MMMM D, YYYY")}
                  </div>
                  {messages.map((message) => (
                    <div
                      key={message.tempId || message.id}
                      className={`flex ${
                        message.sender.id === currentUser.id
                          ? "justify-end"
                          : "justify-start"
                      } mb-2`}
                    >
                      <div
                        className={`max-w-[70%] p-3 rounded-lg ${
                          message.sender.id === currentUser.id
                            ? "bg-[#1FC16B1A] text-[#4B5563]"
                            : "bg-[#EBEDF0] text-[#4B5563]"
                        }`}
                      >
                        {/* Message text */}
                        {message.text && (
                          <p className="text-sm mb-2">{message.text}</p>
                        )}

                        {/* File attachments */}
                        {message.files?.length > 0 && (
                          <div className="flex gap-2 mb-2 flex-wrap">
                            {message.files.map((file, index) => (
                              <div key={index} className="max-w-[200px]">
                                {file.type === "image" && file.preview ? (
                                  <img
                                    src={file.preview}
                                    alt="Attachment"
                                    className="max-h-40 rounded border"
                                  />
                                ) : (
                                  <div className="flex items-center gap-2 p-2 bg-gray-100 rounded border">
                                    <FiFile className="text-gray-500" />
                                    <span className="text-xs truncate">
                                      {file.name}
                                    </span>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="flex justify-between items-center mt-1">
                          <span className="text-xs opacity-80">
                            {dayjs(message.createdAt).format("h:mm A")}
                          </span>
                          {message.sender.id === currentUser.id && (
                            <div className="flex items-center gap-1 ml-2">
                              {!message.readAt && (
                                <span className="text-xs">✓</span>
                              )}
                              {message.readAt && (
                                <span className="text-xs">✓✓</span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">Start a conversation</p>
            </div>
          )}
        </div>

        {/* File previews before sending */}
        {filePreviews.length > 0 && (
          <div className="flex gap-2 mb-2 overflow-x-auto p-2 bg-gray-100 rounded">
            {filePreviews.map((preview, index) => (
              <div key={index} className="relative shrink-0">
                {preview.type === "image" ? (
                  <img
                    src={preview.preview}
                    alt="Preview"
                    className="h-20 w-20 object-cover rounded border"
                  />
                ) : (
                  <div className="h-20 w-20 bg-gray-200 rounded border flex flex-col items-center justify-center p-2">
                    <FiFile size={24} className="text-gray-500" />
                    <span className="text-xs text-center truncate w-full">
                      {preview.name}
                    </span>
                  </div>
                )}
                <button
                  onClick={() => removeFilePreview(index)}
                  className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow hover:bg-gray-100"
                >
                  <FiX size={14} className="text-gray-600" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Typing indicator */}
        {typing && (
          <p className="text-gray-500 italic text-sm mb-2">
            {typing.firstname} is typing...
          </p>
        )}

        {/* Enhanced Message Input */}
        <div className="relative">
          {/* Hidden file input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />

          {/* Textarea with send button */}
          <div className="relative bg-white p-2 rounded-md border">
            <textarea
              ref={textareaRef}
              value={chatMessage}
              onChange={handleTyping}
              onCompositionStart={() => setIsComposing(true)}
              onCompositionEnd={() => setIsComposing(false)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey && !isComposing) {
                  e.preventDefault();
                  sendChatMessage();
                }
              }}
              placeholder="Type a message..."
              className="w-full rounded-md focus:outline-none resize-none"
              rows={2}
            />

            <div className="flex justify-between items-end">
              <button
                onClick={handleFileSelect}
                className="text-gray-500 hover:text-gray-700 p-1"
                title="Attach file"
                disabled={uploadingFiles}
              >
                <FiPaperclip size={20} />
              </button>

              <button
                onClick={sendChatMessage}
                disabled={
                  (!chatMessage.trim() && filePreviews.length === 0) ||
                  uploadingFiles
                }
                className={`flex gap-1 items-center px-3 py-2 rounded-md text-white ${
                  (chatMessage.trim() || filePreviews.length > 0) &&
                  !uploadingFiles
                    ? "bg-primary"
                    : "bg-gray-300 cursor-not-allowed"
                }`}
                title="Send message"
              >
                {uploadingFiles ? (
                  "Sending..."
                ) : (
                  <>
                    Send
                    <FiSend size={15} />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatRoom;
