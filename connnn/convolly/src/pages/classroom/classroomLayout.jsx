import React, { useState, useRef, useEffect } from "react";
import <PERSON>goraRTC from "agora-rtc-sdk-ng";
import AC from "agora-chat";
import { APP_ID } from "../../../agora-config";
import JoinRoom from "./components/joinRoom/joinRoom";
import VideoContainer from "./components/videoContainer/videoContainer";
import QuizGame from "./components/quizGame/quizGame";
import AssistantSidebar from "./components/assistantSidebar/assistantSidebar";
import ChatSideBar from "./components/chatSideBar/chatSideBar";
import ControlBar from "./components/controlBar/controlBar";
import usePost from "@/hooks/usePost";
import { useRetrieveClassTokenMutation } from "@/redux/slices/classroomApiSlice";
import { useSelector } from "react-redux";
import ConversationStarters from "./components/conversationStarters/conversationStarters";
import { io } from "socket.io-client";
import VideoPlayer from "./components/videoPlayer/videoPlayer";
import { useLocation } from "react-router-dom";

const ClassroomLayout = () => {
  const location = useLocation();
  const classDetails = location.state;

  console.log(classDetails);

  const [joined, setJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [showChat, setShowChat] = useState(false);
  const [micIcon, setMicIcon] = useState("/svgs/micOn.svg");
  const [agoraTokenResponse, setAgoraTokenResponse] = useState(null);
  const [members, setMembers] = useState([]);
  const [chatLoggedIn, setChatLoggedIn] = useState(false);
  const [messages, setMessages] = useState([]);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [AIResponse, setAIResponse] = useState(null);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [remoteVideoTracks, setRemoteVideoTracks] = useState({});
  const [screenShareUid, setScreenShareUid] = useState(null);
  const [localVideoKey, setLocalVideoKey] = useState(0);
  const [remoteScreenTrack, setRemoteScreenTrack] = useState(null);
  const [screenSharingSessions, setScreenSharingSessions] = useState(new Set());
  const [streamId, setStreamId] = useState(null);
  const [pendingQuizzes, setPendingQuizzes] = useState({});
  const [activeQuizTransfers, setActiveQuizTransfers] = useState({});
  const [socket, setSocket] = useState(null);
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [receivedMessages, setReceivedMessages] = useState([]);
  const [notification, setNotification] = useState(null);
  // Quiz sync state
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState({});
  const [showResults, setShowResults] = useState(false);
  const [broadcastedVideo, setBroadcastedVideo] = useState(null);

  // Refs
  const rtcClient = useRef(null);
  const localAudioTrack = useRef(null);
  const localVideoTrack = useRef(null);
  const remoteUsers = useRef({});
  const conn = useRef(null);
  const screenTrack = useRef(null);
  const localVideoRef = useRef(null);
  const videoRefs = useRef({});
  const screenVideoRef = useRef(null);

  // Initialize Agora Chat connection
  useEffect(() => {
    conn.current = new AC.connection({
      appKey: "411348949#1554976"
    });
  }, []);

  const { handlePost: handleGenerateAgoraToken, isLoading: generatingToken } =
    usePost(useRetrieveClassTokenMutation);

  const isAnyScreenSharing = () => {
    return isScreenSharing || screenSharingSessions.size > 0;
  };

  const notifyScreenSharingStart = async (isStarting) => {
    if (conn.current && chatLoggedIn) {
      try {
        await conn.current.send({
          to: agoraTokenResponse?.channel?.id,
          chatType: "groupChat",
          msg: isStarting ? "screen-share-start" : "screen-share-end",
          type: "txt",
          ext: {
            uid: roomInfo?.chatUser.id,
            name: `${roomInfo?.chatUser.id}`,
            action: isStarting ? "start" : "end"
          }
        });
      } catch (error) {
        console.error("Error sending screen sharing notification:", error);
      }
    }
  };

  const handleUserPublished = async (user, mediaType) => {
    try {
      await rtcClient.current.subscribe(user, mediaType);
      if (mediaType === "video") {
        if (screenSharingSessions.has(user.uid)) {
          setRemoteScreenTrack(user.videoTrack);
          setScreenShareUid(user.uid);
        } else {
          const track = user.videoTrack;
          const isScreenShare =
            track &&
            (track.getMediaStreamTrack()?.getSettings()?.displaySurface ||
              (track.getMediaStreamTrack()?.kind === "video" &&
                track.getMediaStreamTrack()?.label?.includes("screen")));

          if (isScreenShare) {
            setScreenSharingSessions((prev) => new Set([...prev, user.uid]));
            setRemoteScreenTrack(user.videoTrack);
            setScreenShareUid(user.uid);
          } else {
            setRemoteVideoTracks((prev) => ({
              ...prev,
              [user.uid]: user.videoTrack
            }));
          }
        }
      }

      if (mediaType === "audio") {
        user.audioTrack.play();
        updateMicStatus(user, true);
      }

      remoteUsers.current[user.uid] = user;
      setMembers((prev) => {
        const exists = prev.some((m) => m.uid === user.uid);
        return exists
          ? prev
          : [...prev, { uid: user.uid, name: `User ${user.uid}` }];
      });
    } catch (error) {
      console.error(
        `Error subscribing to ${mediaType} for UID: ${user.uid}`,
        error
      );
    }
  };

  const handleUserUnpublished = (user, mediaType) => {
    if (mediaType === "video") {
      if (screenSharingSessions.has(user.uid) || screenShareUid === user.uid) {
        setRemoteScreenTrack(null);
        setScreenShareUid(null);
        setScreenSharingSessions((prev) => {
          const newSet = new Set(prev);
          newSet.delete(user.uid);
          return newSet;
        });
      } else {
        setRemoteVideoTracks((prev) => {
          const newTracks = { ...prev };
          delete newTracks[user.uid];
          return newTracks;
        });
      }
    }
    if (mediaType === "audio") {
      updateMicStatus(user, false);
    }
  };

  const handleUserLeft = (user) => {
    delete remoteUsers.current[user.uid];
    setMembers((prev) => prev.filter((m) => m.uid !== user.uid));

    if (screenSharingSessions.has(user.uid) || screenShareUid === user.uid) {
      setRemoteScreenTrack(null);
      setScreenShareUid(null);
      setScreenSharingSessions((prev) => {
        const newSet = new Set(prev);
        newSet.delete(user.uid);
        return newSet;
      });
    }

    setRemoteVideoTracks((prev) => {
      const newTracks = { ...prev };
      delete newTracks[user.uid];
      return newTracks;
    });
  };

  const toggleScreenShare = async () => {
    if (!isScreenSharing) {
      try {
        const track = await AgoraRTC.createScreenVideoTrack({
          encoderConfig: "1080p_1"
        });
        screenTrack.current = track;

        if (localVideoTrack.current) {
          await rtcClient.current.unpublish(localVideoTrack.current);
        }

        await rtcClient.current.publish(track);
        setScreenShareUid(roomInfo?.chatUser.id);
        setIsScreenSharing(true);

        await notifyScreenSharingStart(true);

        track.on("track-ended", async () => {
          await handleScreenShareEnded();
        });
      } catch (error) {
        console.error("Screen sharing failed:", error);
      }
    } else {
      await handleScreenShareEnded();
    }
  };

  const handleScreenShareEnded = async () => {
    try {
      if (screenTrack.current) {
        await rtcClient.current?.unpublish(screenTrack.current);
        screenTrack.current.close();
        screenTrack.current = null;
      }

      await notifyScreenSharingStart(false);

      if (isVideoOn && localVideoTrack.current) {
        await rtcClient.current.publish(localVideoTrack.current);
        if (localVideoRef.current) {
          localVideoTrack.current.play(localVideoRef.current);
        }
      }

      setScreenShareUid(null);
      setIsScreenSharing(false);
    } catch (error) {
      console.error("Error ending screen share:", error);
    }
  };

  const toggleMic = async () => {
    const muted = !isMuted;
    setIsMuted(muted);
    setMicIcon(muted ? "/svgs/micOff.svg" : "/svgs/micOn.svg");
    await localAudioTrack.current?.setMuted(muted);
  };

  const toggleVideo = async () => {
    const enabled = !isVideoOn;
    setIsVideoOn(enabled);

    if (enabled) {
      try {
        const newVideoTrack = await AgoraRTC.createCameraVideoTrack();
        localVideoTrack.current = newVideoTrack;

        await rtcClient.current.publish(localVideoTrack.current);

        if (localVideoRef.current) {
          localVideoTrack.current.play(localVideoRef.current);
          setLocalVideoKey((prev) => prev + 1);
        }
      } catch (error) {
        console.error("Error enabling video:", error);
        setIsVideoOn(false);
      }
    } else {
      if (localVideoTrack.current) {
        try {
          await rtcClient.current.unpublish(localVideoTrack.current);
          localVideoTrack.current.stop();
          localVideoTrack.current.close();
          localVideoTrack.current = null;
          if (localVideoRef.current) {
            localVideoRef.current.srcObject = null;
          }
          setLocalVideoKey((prev) => prev + 1);
        } catch (error) {
          console.error("Error disabling video:", error);
        }
      }
    }
  };

  const updateMicStatus = (user, isAudioOn) => {
    const micEl = document.querySelector(`#member-${user.uid} .mic-icon`);
    if (micEl) micEl.src = isAudioOn ? "/svgs/micOn.svg" : "/svgs/micOff.svg";
  };

  const userId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const generateToken = async () => {
    const res = await handleGenerateAgoraToken({
      channelId: classDetails?.classroom?.id
    });

    if (res) {
      setAgoraTokenResponse(res?.data);
      startVideoChat(res?.data);
    }
  };

  const startVideoChat = async (roomInfo) => {
    rtcClient.current = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });

    rtcClient.current.on("user-joined", (user) => {
      setMembers((prev) => [
        ...prev,
        { uid: user.uid, name: `User ${user.uid}` }
      ]);
    });
    rtcClient.current.on("user-published", handleUserPublished);
    rtcClient.current.on("user-unpublished", handleUserUnpublished);
    rtcClient.current.on("user-left", handleUserLeft);

    rtcClient.current.on("message-from-channel", ({ uid, text }) => {
      try {
        const message = JSON.parse(text);
        if (message.msg === "ai-quiz" && message.ext?.action === "start-quiz") {
          const quizContent = JSON.parse(message.ext.questions);
          setAIResponse({ type: "quiz", quiz: quizContent });
        }
      } catch (err) {
        console.error("Error parsing RTC data stream message:", err);
      }
    });

    try {
      const [audioTrack, videoTrack] =
        await AgoraRTC.createMicrophoneAndCameraTracks();
      localAudioTrack.current = audioTrack;
      localVideoTrack.current = videoTrack;

      await rtcClient.current.join(
        APP_ID,
        roomInfo?.channel?.id,
        roomInfo?.rtcToken,
        roomInfo?.chatUser.id
      );

      await rtcClient.current.publish([audioTrack, videoTrack]);

      setMembers([{ uid: roomInfo?.chatUser.id, name: "You" }]);
      setJoined(true);

      if (localVideoRef.current && videoTrack && isVideoOn) {
        videoTrack.play(localVideoRef.current);
      }

      const options = {
        accessToken: roomInfo.chatUserToken,
        user: roomInfo.chatUser.id
      };

      conn.current.addEventHandler("connection", {
        onConnected: () => {
          setChatLoggedIn(true);
        },
        onError: (error) => {
          console.error("Chat connection error:", error);
          setChatLoggedIn(false);
        },
        onTextMessage: (msg) => {
          if (msg.msg === "quiz-init") {
            const { quizId, totalChunks, metadata } = msg.ext;
            setPendingQuizzes((prev) => ({
              ...prev,
              [quizId]: {
                receivedChunks: 0,
                totalChunks,
                chunks: {},
                metadata
              }
            }));
            return;
          }

          if (msg.msg === "quiz-chunk") {
            const { quizId, chunkIndex, data, isFinal } = msg.ext;
            setPendingQuizzes((prev) => {
              if (!prev[quizId]) return prev;
              const updated = {
                ...prev,
                [quizId]: {
                  ...prev[quizId],
                  receivedChunks: prev[quizId].receivedChunks + 1,
                  chunks: {
                    ...prev[quizId].chunks,
                    [chunkIndex]: data
                  }
                }
              };
              if (
                isFinal &&
                updated[quizId].receivedChunks === updated[quizId].totalChunks
              ) {
                setTimeout(() => assembleQuiz(quizId), 100);
              }
              return updated;
            });
          }

          if (
            msg.msg === "screen-share-start" &&
            msg.ext?.uid !== roomInfo?.chatUser.id
          ) {
            setScreenSharingSessions((prev) => new Set([...prev, msg.ext.uid]));
          } else if (
            msg.msg === "screen-share-end" &&
            msg.ext?.uid !== roomInfo?.chatUser.id
          ) {
            setScreenSharingSessions((prev) => {
              const newSet = new Set(prev);
              newSet.delete(msg.ext.uid);
              return newSet;
            });
            if (screenShareUid === msg.ext.uid) {
              setRemoteScreenTrack(null);
              setScreenShareUid(null);
            }
          } else if (
            msg.msg !== "introducing-myself" &&
            msg.msg !== "screen-share-start" &&
            msg.msg !== "screen-share-end"
          ) {
            setMessages((prev) => [
              ...prev,
              { senderId: msg.from, text: msg.msg }
            ]);
            if (!showChat) setUnreadMessages((prev) => prev + 1);
          }

          if (msg.msg === "introducing-myself" && msg.ext?.uid) {
            setMembers((prev) => {
              const exists = prev.some((m) => m.uid === msg.ext.uid);
              return exists
                ? prev
                : [...prev, { uid: msg.ext.uid, name: msg.ext.name }];
            });
          }
        }
      });

      await conn.current.open(options);
      await conn.current.joinChatRoom({ roomId: roomInfo?.chatGroup?.id });

      conn.current.send({
        to: roomInfo?.chatGroup?.id,
        chatType: "groupChat",
        msg: "introducing-myself",
        type: "txt",
        ext: {
          uid: roomInfo?.chatUser.id,
          name: `${roomInfo?.chatUser.id}`
        }
      });
    } catch (error) {
      console.error("Error starting video chat:", error);
    }
  };

  const assembleQuiz = (quizId) => {
    setPendingQuizzes((prev) => {
      const quiz = prev[quizId];
      if (!quiz || quiz.receivedChunks < quiz.totalChunks) return prev;

      try {
        const chunks = [];
        for (let i = 0; i < quiz.totalChunks; i++) {
          chunks.push(quiz.chunks[i]);
        }
        const fullData = chunks.join("");
        const quizData = JSON.parse(fullData);
        setAIResponse({ type: "quiz", quiz: quizData });
        const { [quizId]: _, ...remaining } = prev;
        return remaining;
      } catch (error) {
        console.error("Failed to assemble quiz:", error);
        return prev;
      }
    });
  };

  const leaveRoom = async () => {
    try {
      if (screenTrack.current) {
        await rtcClient.current?.unpublish(screenTrack.current);
        screenTrack.current.close();
        screenTrack.current = null;
      }

      if (localAudioTrack.current) {
        localAudioTrack.current.stop();
        localAudioTrack.current.close();
        localAudioTrack.current = null;
      }

      if (localVideoTrack.current) {
        localVideoTrack.current.stop();
        localVideoTrack.current.close();
        localVideoTrack.current = null;
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = null;
        }
      }

      await rtcClient.current?.unpublish();
      await rtcClient.current?.leave();

      setJoined(false);
      setMembers([]);
      setMessages([]);
      setRemoteVideoTracks({});
      setRemoteScreenTrack(null);
      setScreenSharingSessions(new Set());
      setIsScreenSharing(false);
      setScreenShareUid(null);
      setIsVideoOn(true);
      setIsMuted(false);
      setMicIcon("/svgs/micOn.svg");
      setLocalVideoKey((prev) => prev + 1);
      setAIResponse(null);
      setShowAIAssistant(false);
      setStreamId(null);
      setNotification(null);
      // Reset quiz state
      setCurrentQuestionIndex(0);
      setUserAnswers({});
      setShowResults(false);
    } catch (error) {
      console.error("Error leaving room:", error);
    }
  };

  const submitAnswers = (answers) => {
    if (AIResponse?.type === "quiz") {
      const results = AIResponse.quiz.map((q, index) => ({
        question: q.question,
        selectedAnswer: answers[index],
        correctAnswer: q.correctAnswer,
        isCorrect: answers[index] === q.correctAnswer
      }));
      console.log("Quiz results:", results);
    }
  };

  useEffect(() => {
    if (
      joined &&
      localVideoTrack.current &&
      localVideoRef.current &&
      isVideoOn &&
      !isAnyScreenSharing()
    ) {
      localVideoTrack.current.play(localVideoRef.current)?.catch((error) => {
        (async () => {
          try {
            const newVideoTrack = await AgoraRTC.createCameraVideoTrack();
            localVideoTrack.current = newVideoTrack;
            await rtcClient.current.publish(localVideoTrack.current);
            if (localVideoRef.current) {
              localVideoTrack.current.play(localVideoRef.current);
            }
            setLocalVideoKey((prev) => prev + 1);
          } catch (err) {
            console.error("Fallback failed:", err);
          }
        })();
      });
    }
  }, [
    joined,
    isVideoOn,
    localVideoKey,
    isScreenSharing,
    screenSharingSessions.size
  ]);

  // Clear notification after 5 seconds
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => setNotification(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);

  const accessToken = useSelector((state) => state?.app?.userInfo?.accessToken);

  // Update the room-broadcast socket handler in the useEffect
  useEffect(() => {
    const newSocket = io(import.meta.env.VITE_SOCKET_SERVER_URL, {
      autoConnect: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      auth: { token: accessToken }
    });

    setSocket(newSocket);

    newSocket.on("connect", () => {
      console.log("Socket connected:", {
        socketId: newSocket.id,
        status: newSocket.connected,
        time: new Date().toISOString()
      });

      if (agoraTokenResponse?.channel?.id) {
        newSocket.emit("join-room", agoraTokenResponse.channel.id, {
          userId: agoraTokenResponse?.chatUser.id,
          name: `${agoraTokenResponse?.chatUser.id}`,
          role: "student" // or "tutor"
        });

        console.log("Join-room emitted:", {
          room: agoraTokenResponse.channel.id,
          userId: agoraTokenResponse?.chatUser.id,
          socketId: newSocket.id
        });

        setIsSocketConnected(true);
      }
    });

    newSocket.on("joined-room", (data) => {
      console.log("Successfully joined room:", data);
    });

    newSocket.on("room-broadcast", (data) => {
      console.log("Room broadcast received:", data);
      switch (data.action) {
        case "select-answer":
          setUserAnswers((prev) => ({
            ...prev,
            [data.questionIndex]: data.selectedOption
          }));
          setCurrentQuestionIndex(data.questionIndex);
          break;
        case "next-question":
          setCurrentQuestionIndex(data.questionIndex);
          break;
        case "previous-question":
          setCurrentQuestionIndex(data.questionIndex);
          break;
        case "submit-quiz":
          setShowResults(true);
          submitAnswers(data.userAnswers);
          break;
        case "clear-video":
          setBroadcastedVideo(null);
          break;
        default:
          if (data.video) {
            setBroadcastedVideo({
              id: data.video.id,
              title: data.video.title,
              thumbnail: data.video.thumbnail,
              channel: data.video.channel
            });
          } else {
            setReceivedMessages((prev) => [
              ...prev,
              {
                senderId: data.senderId || "Unknown",
                message: data.message,
                timestamp: new Date().toISOString()
              }
            ]);
            setAIResponse(data?.response);
          }
      }
    });

    newSocket.on("connect_error", (err) => {
      console.error("Socket connection error:", err);
    });

    newSocket.on("join-error", (err) => {
      console.error("Room join error:", err);
    });

    return () => {
      newSocket.off("joined-room");
      newSocket.off("room-broadcast");
      newSocket.off("connect_error");
      newSocket.disconnect();
    };
  }, [agoraTokenResponse?.channel?.id, agoraTokenResponse?.chatUser.id]);

  const getVideoLayoutClass = () => {
    if (isAnyScreenSharing()) {
      return "w-full h-full";
    }

    const totalVideos =
      (isVideoOn && localVideoTrack.current ? 1 : 0) +
      Object.keys(remoteVideoTracks).length;

    if (totalVideos <= 1) {
      return "flex items-center justify-center";
    } else if (totalVideos === 2) {
      return "grid grid-cols-2 gap-4";
    } else {
      return "grid grid-cols-1 md:grid-cols-2 gap-4";
    }
  };

  const enterRoom = async (e) => {
    e.preventDefault();
    await generateToken();
  };

  // console.log(first)

  return (
    <div>
      {!joined ? (
        <JoinRoom
          isMuted={isMuted}
          toggleMic={toggleMic}
          isVideoOn={isVideoOn}
          toggleVideo={toggleVideo}
          micIcon={micIcon}
          generatingToken={generatingToken}
          enterRoom={enterRoom}
          classDetails={classDetails}
        />
      ) : (
        <div className="h-screen overflow-hidden flex flex-col">
          <div
            className={`relative h-full overflow-auto flex-1 bg-black ${
              showChat || showAIAssistant ? "w-[calc(100vw-350px)]" : "w-full"
            }`}
          >
            {notification && (
              <div className="absolute top-4 left-4 right-4 z-50 bg-blue-600 text-white p-3 rounded-lg shadow-lg text-sm">
                {notification}
              </div>
            )}

            {broadcastedVideo ? (
              <VideoPlayer
                video={broadcastedVideo}
                socket={socket}
                setBroadcastedVideo={setBroadcastedVideo}
                rtcUid={agoraTokenResponse?.chatUser.id}
                isSocketConnected={isSocketConnected}
                roomInfo={agoraTokenResponse}
              />
            ) : AIResponse ? (
              AIResponse.type === "quiz" ? (
                <QuizGame
                  questions={AIResponse.quiz}
                  setShowAIAssistant={setShowAIAssistant}
                  setAIResponse={setAIResponse}
                  submitAnswers={submitAnswers}
                  socket={socket}
                  isSocketConnected={isSocketConnected}
                  roomInfo={agoraTokenResponse}
                  rtcUid={agoraTokenResponse?.chatUser.id}
                  currentQuestionIndex={currentQuestionIndex}
                  setCurrentQuestionIndex={setCurrentQuestionIndex}
                  userAnswers={userAnswers}
                  setUserAnswers={setUserAnswers}
                  showResults={showResults}
                  setShowResults={setShowResults}
                />
              ) : (
                <ConversationStarters
                  prompts={AIResponse.prompts}
                  videos={AIResponse.videos}
                  setShowAIAssistant={setShowAIAssistant}
                  setAIResponse={setAIResponse}
                />
              )
            ) : (
              <VideoContainer
                isAnyScreenSharing={isAnyScreenSharing}
                isScreenSharing={isScreenSharing}
                screenShareUid={screenShareUid}
                members={members}
                isVideoOn={isVideoOn}
                isMuted={isMuted}
                remoteVideoTracks={remoteVideoTracks}
                localVideoTrack={localVideoTrack.current}
                localVideoRef={localVideoRef}
                videoRefs={videoRefs}
                screenVideoRef={screenVideoRef}
                remoteScreenTrack={remoteScreenTrack}
                localVideoKey={localVideoKey}
                getVideoLayoutClass={getVideoLayoutClass}
              />
            )}
          </div>

          {showChat && (
            <ChatSideBar
              setShowChat={setShowChat}
              chatLoggedIn={chatLoggedIn}
              conn={conn}
              setUnreadMessages={setUnreadMessages}
              messages={messages}
              members={members}
              setMessages={setMessages}
              roomInfo={agoraTokenResponse}
            />
          )}

          {showAIAssistant && (
            <AssistantSidebar
              setShowAIAssistant={setShowAIAssistant}
              setAIResponse={setAIResponse}
              AIResponse={AIResponse}
              rtcUid={agoraTokenResponse?.chatUser.id}
              roomInfo={agoraTokenResponse}
              rtcClient={rtcClient.current}
              streamId={streamId}
              conn={conn}
              chatLoggedIn={chatLoggedIn}
              socket={socket}
              isSocketConnected={isSocketConnected}
              receivedMessages={receivedMessages}
              setReceivedMessages={setReceivedMessages}
              setNotification={setNotification}
              notification={notification}
              setBroadcastedVideo={setBroadcastedVideo}
            />
          )}

          <ControlBar
            isMuted={isMuted}
            toggleMic={toggleMic}
            isVideoOn={isVideoOn}
            toggleVideo={toggleVideo}
            showChat={showChat}
            setShowChat={setShowChat}
            showAIAssistant={showAIAssistant}
            setShowAIAssistant={setShowAIAssistant}
            toggleScreenShare={toggleScreenShare}
            leaveRoom={leaveRoom}
            unreadMessages={unreadMessages}
            setUnreadMessages={setUnreadMessages}
            micIcon={micIcon}
            isScreenSharing={isScreenSharing}
          />
        </div>
      )}
    </div>
  );
};

export default ClassroomLayout;
