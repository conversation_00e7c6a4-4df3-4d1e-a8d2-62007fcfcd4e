import React, { useState, useEffect } from "react";
import { X, Search, Loader2, ChevronDown } from "lucide-react";

const AssistantSidebar = ({
  setShowAIAssistant,
  chatLoggedIn,
  setAIResponse,
  rtcUid,
  roomInfo,
  rtcClient,
  streamId,
  AIResponse,
  socket,
  isSocketConnected,
  receivedMessages,
  setReceivedMessages,
  setNotification,
  notification,
  setBroadcastedVideo
}) => {
  const [step, setStep] = useState(1);
  const [aiPrompt, setAiPrompt] = useState("");
  const [isLoadingAI, setIsLoadingAI] = useState(false);
  const [exerciseType, setExerciseType] = useState("");
  const [exerciseCategory, setExerciseCategory] = useState("");
  const [difficultyLevel, setDifficultyLevel] = useState("Beginner (A1)");
  const [error, setError] = useState(null);
  const [videoSearchQuery, setVideoSearchQuery] = useState("");
  const [isSearchingVideos, setIsSearchingVideos] = useState(false);
  const [videoError, setVideoError] = useState(null);
  const [topic, setTopic] = useState("");
  const [videoDuration, setVideoDuration] = useState("short");
  const [mode, setMode] = useState(null);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [youtubeResults, setYoutubeResults] = useState([]);

  const truncateText = (text, maxLength) => {
    if (!text) return "";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const getExerciseTypes = (category) => {
    const types = {
      grammar: [
        { value: "multiple-choice", label: "Multiple Choice" },
        { value: "fill-in-the-blank", label: "Fill in the Blank" },
        { value: "sentence-correction", label: "Sentence Correction" }
      ],
      reading: [
        { value: "comprehension", label: "Reading Comprehension" },
        { value: "summary", label: "Summary Exercise" }
      ],
      vocabulary: [
        { value: "matching", label: "Matching" },
        { value: "word-definition", label: "Word Definitions" }
      ],
      writing: [
        { value: "essay", label: "Essay Prompt" },
        { value: "paragraph", label: "Paragraph Writing" }
      ]
    };
    return types[category] || [];
  };

  const cleanJsonResponse = (rawContent) => {
    let cleaned = rawContent.replace(/```json\n|\n```|```/g, "").trim();
    const jsonStart = cleaned.indexOf("{");
    const jsonEnd = cleaned.lastIndexOf("}");
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }

    try {
      const parsed = JSON.parse(cleaned);
      if (
        mode === "exercise" &&
        (!parsed.type ||
          parsed.type !== "quiz" ||
          !Array.isArray(parsed.quiz) ||
          !parsed.quiz.every(
            (item) =>
              item.question &&
              Array.isArray(item.options) &&
              item.options.length === 4 &&
              item.correctAnswer
          ) ||
          !Array.isArray(parsed.videos))
      ) {
        throw new Error("Invalid quiz format");
      }
      if (
        mode === "conversation" &&
        (!parsed.type ||
          parsed.type !== "conversation" ||
          !Array.isArray(parsed.prompts) ||
          !Array.isArray(parsed.videos))
      ) {
        throw new Error("Invalid conversation starters format");
      }
      return parsed;
    } catch (error) {
      console.error("JSON Parse Error:", error, "Raw Content:", rawContent);
      throw new Error("Failed to parse response data. Please try again.");
    }
  };

  const searchYouTubeVideos = async () => {
    if (!videoSearchQuery.trim()) return;

    setIsSearchingVideos(true);
    setVideoError(null);

    try {
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&maxResults=5&q=${encodeURIComponent(
          videoSearchQuery
        )}&type=video&videoDuration=${videoDuration}&key=${
          import.meta.env.VITE_GOOGLE_API_KEY
        }`
      );

      if (!response.ok) throw new Error("YouTube API error");

      const data = await response.json();

      if (!data.items || data.items.length === 0) {
        throw new Error("No videos found. Try a different search.");
      }

      const filteredVideos = data.items
        .filter((item) => item.id.videoId)
        .map((item) => ({
          id: item.id.videoId,
          title: truncateText(item.snippet.title, 60),
          fullTitle: item.snippet.title,
          thumbnail: item.snippet.thumbnails?.medium?.url || "",
          channel: truncateText(item.snippet.channelTitle, 30),
          fullChannel: item.snippet.channelTitle,
          duration: getDurationFromSearchResult(item)
        }));

      setYoutubeResults(filteredVideos);
    } catch (error) {
      console.error("YouTube Error:", error);
      setVideoError(error.message);
    } finally {
      setIsSearchingVideos(false);
    }
  };

  const getDurationFromSearchResult = (item) => {
    return videoDuration === "short"
      ? "< 4 min"
      : videoDuration === "medium"
      ? "4-20 min"
      : "> 20 min";
  };

  const handleVideoBroadcast = (video) => {
    if (socket && isSocketConnected) {
      console.log("Emitting room-broadcast for video-share:", {
        roomId: roomInfo?.id,
        video: {
          id: video.id,
          title: video.title,
          thumbnail: video.thumbnail,
          channel: video.channel
        },
        senderId: rtcUid.current,
        streamId
      });

      socket.emit("room-broadcast", roomInfo?.channel?.id, {
        roomId: roomInfo?.channel?.id,
        video: {
          id: video.id,
          title: video.title,
          thumbnail: video.thumbnail,
          channel: video.channel
        },
        senderId: rtcUid.current,
        streamId
      });
    }

    setBroadcastedVideo(video);
  };

  useEffect(() => {
    if (step === 5 && videoSearchQuery) {
      searchYouTubeVideos();
    }
  }, [step, videoSearchQuery, videoDuration]);

  const generateContent = async () => {
    if (!topic || (mode === "exercise" && !exerciseType)) {
      throw new Error("Please select all required fields.");
    }

    const startTime = Date.now();
    try {
      const systemPrompt =
        mode === "exercise"
          ? `Generate a ${difficultyLevel} level ${exerciseCategory} quiz with 3 multiple-choice questions about ${topic}.
           Each question must have exactly 4 options and one correct answer.
           Additionally, suggest 2 relevant YouTube videos (with title and URL) related to ${topic}.
           Return the response in JSON format only, like this:
           {
             "type": "quiz",
             "quiz": [
               {
                 "question": "string",
                 "options": ["string", "string", "string", "string"],
                 "correctAnswer": "string"
               },
               ...
             ],
             "videos": [
               {"title": "string", "url": "string"},
               {"title": "string", "url": "string"}
             ]
           }
           Do not include any markdown, code fences, or additional text outside the JSON.`
          : `Generate 3 conversation starter prompts for a ${difficultyLevel} level discussion about ${topic}.
           Additionally, suggest 2 relevant YouTube videos (with title and URL) related to ${topic}.
           Return the response in JSON format only, like this:
           {
             "type": "conversation",
             "prompts": ["string", "string", "string"],
             "videos": [
               {"title": "string", "url": "string"},
               {"title": "string", "url": "string"}
             ]
           }
           Do not include any markdown, code fences, or additional text outside the JSON.`;

      const response = await fetch(
        "https://api.openai.com/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${import.meta.env.VITE_OPEN_AI_KEY}`
          },
          body: JSON.stringify({
            model: "gpt-4o-mini",
            messages: [
              { role: "system", content: systemPrompt },
              {
                role: "user",
                content:
                  aiPrompt ||
                  (mode === "exercise"
                    ? `Create a ${exerciseType} quiz about ${topic}`
                    : `Create conversation starters about ${topic}`)
              }
            ],
            temperature: 0.7,
            max_tokens: 800
          })
        }
      );

      if (!response.ok)
        throw new Error(`AI request failed: ${response.statusText}`);

      const data = await response.json();
      const rawContent = data.choices[0]?.message?.content;
      if (!rawContent) throw new Error("No content received from AI");

      const content = cleanJsonResponse(rawContent);
      console.log(`Content generation took ${Date.now() - startTime}ms`);
      return content;
    } catch (error) {
      console.error("Content Generation Error:", error);
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoadingAI(true);
    setError(null);

    try {
      const content = await generateContent();
      setAIResponse(content);

      if (socket && isSocketConnected) {
        console.log("Emitting room-broadcast for ai-response:", {
          roomId: roomInfo?.channel?.id,
          response: content,
          senderId: rtcUid.current,
          streamId
        });
        socket.emit("room-broadcast", roomInfo?.channel?.id, {
          roomId: roomInfo?.channel?.id,
          response: content,
          senderId: rtcUid.current,
          streamId
        });

        const message =
          mode === "exercise"
            ? `Generated ${exerciseCategory} quiz on ${topic} with 3 questions`
            : `Generated conversation starters on ${topic} with 3 prompts`;
      } else {
        setError("Socket not connected, cannot share content with others");
      }

      setVideoSearchQuery(
        `${topic} ${
          mode === "exercise" ? `${exerciseCategory} english` : ""
        } lesson`
      );
      setStep(5);
    } catch (error) {
      setError(
        error.message || "Failed to generate content. Please try again."
      );
    } finally {
      setIsLoadingAI(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="p-4 space-y-4 flex flex-col h-full">
            <h3 className="text-lg font-bold text-gray-800">
              Teaching Assistant
            </h3>
            <p className="text-sm text-gray-600">
              Create customized English learning content for your class.
            </p>

            {!isSocketConnected && (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-3 rounded text-sm">
                Connecting to server... (Features may be limited)
              </div>
            )}

            <div className="mt-auto space-y-3">
              <button
                onClick={() => {
                  setMode("conversation");
                  setStep(2);
                }}
                className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Conversation Starters
              </button>
              <button
                onClick={() => {
                  setMode("exercise");
                  setStep(3);
                }}
                className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 transition-colors"
              >
                Exercises
              </button>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="p-4 space-y-4">
            <h3 className="text-lg font-bold text-gray-800">
              Conversation Starters
            </h3>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded text-sm">
                {error}
              </div>
            )}
            {notification && (
              <div className="bg-blue-50 border border-blue-200 text-blue-700 p-3 rounded text-sm">
                {notification}
              </div>
            )}
            {!isSocketConnected && (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-3 rounded text-sm">
                Not connected to server - content won't be shared with others
              </div>
            )}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Student Level
                </label>
                <div className="relative">
                  <select
                    value={difficultyLevel}
                    onChange={(e) => setDifficultyLevel(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded text-sm"
                  >
                    <option value="Beginner (A1)">Beginner (A1)</option>
                    <option value="Absolute beginner (A0)">
                      Absolute beginner (A0)
                    </option>
                    <option value="Elementary (A2)">Elementary (A2)</option>
                    <option value="Intermediate (B1)">Intermediate (B1)</option>
                    <option value="Upper Intermediate (B2)">
                      Upper Intermediate (B2)
                    </option>
                    <option value="Advanced (C1)">Advanced (C1)</option>
                    <option value="Proficient (C2)">Proficient (C2)</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Topic
                </label>
                <input
                  type="text"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="e.g., Travel and Culture"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Instructions (Optional)
                </label>
                <textarea
                  rows={3}
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="Specific requirements for the prompts..."
                />
              </div>

              <div className="flex gap-2 pt-2">
                <button
                  type="button"
                  onClick={() => setStep(1)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded text-sm hover:bg-gray-300"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isLoadingAI || !topic}
                  className="flex-1 bg-blue-600 text-white py-2 rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
                >
                  {isLoadingAI ? (
                    <span className="flex items-center justify-center">
                      <Loader2 className="animate-spin h-4 w-4 mr-2" />
                      Generating...
                    </span>
                  ) : (
                    "Generate Prompts"
                  )}
                </button>
              </div>
            </form>
          </div>
        );

      case 3:
        return (
          <div className="p-4 space-y-4">
            <h3 className="text-lg font-bold text-gray-800">
              Select Quiz Category
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {["Grammar", "Reading", "Vocabulary", "Writing"].map(
                (category) => (
                  <button
                    key={category}
                    onClick={() => {
                      setExerciseCategory(category.toLowerCase());
                      setStep(4);
                    }}
                    className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-sm text-gray-800"
                  >
                    {category}
                  </button>
                )
              )}
            </div>
            <button
              type="button"
              onClick={() => setStep(1)}
              className="w-full bg-gray-200 text-gray-800 py-2 rounded text-sm hover:bg-gray-300"
            >
              Back
            </button>
          </div>
        );

      case 4:
        return (
          <div className="p-4 space-y-4">
            <h3 className="text-lg font-bold text-gray-800">
              Create{" "}
              {exerciseCategory.charAt(0).toUpperCase() +
                exerciseCategory.slice(1)}{" "}
              Quiz
            </h3>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded text-sm">
                {error}
              </div>
            )}
            {notification && (
              <div className="bg-blue-50 border border-blue-200 text-blue-700 p-3 rounded text-sm">
                {notification}
              </div>
            )}
            {!isSocketConnected && (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-3 rounded text-sm">
                Not connected to server - content won't be shared with others
              </div>
            )}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quiz Type
                </label>
                <div className="relative">
                  <select
                    value={exerciseType}
                    onChange={(e) => setExerciseType(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded text-sm"
                    required
                  >
                    <option value="">Select type</option>
                    {getExerciseTypes(exerciseCategory).map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Student Level
                </label>
                <div className="relative">
                  <select
                    value={difficultyLevel}
                    onChange={(e) => setDifficultyLevel(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded text-sm"
                  >
                    <option value="Beginner (A1)">Beginner (A1)</option>
                    <option value="Absolute beginner (A0)">
                      Absolute beginner (A0)
                    </option>
                    <option value="Elementary (A2)">Elementary (A2)</option>
                    <option value="Intermediate (B1)">Intermediate (B1)</option>
                    <option value="Upper Intermediate (B2)">
                      Upper Intermediate (B2)
                    </option>
                    <option value="Advanced (C1)">Advanced (C1)</option>
                    <option value="Proficient (C2)">Proficient (C2)</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Topic
                </label>
                <input
                  type="text"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="e.g., Present Perfect Tense"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Instructions (Optional)
                </label>
                <textarea
                  rows={3}
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="Specific requirements for the quiz..."
                />
              </div>

              <div className="flex gap-2 pt-2">
                <button
                  type="button"
                  onClick={() => setStep(3)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded text-sm hover:bg-gray-300"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isLoadingAI || !exerciseType || !topic}
                  className="flex-1 bg-blue-600 text-white py-2 rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
                >
                  {isLoadingAI ? (
                    <span className="flex items-center justify-center">
                      <Loader2 className="animate-spin h-4 w-4 mr-2" />
                      Generating...
                    </span>
                  ) : (
                    "Generate Quiz"
                  )}
                </button>
              </div>
            </form>
          </div>
        );

      case 5:
        return (
          <div className="p-4 space-y-4 h-full flex flex-col">
            <div className="space-y-3">
              <h3 className="text-lg font-bold text-gray-800">
                Related Videos
              </h3>

              <div className="flex gap-2">
                <input
                  type="text"
                  value={videoSearchQuery}
                  onChange={(e) => setVideoSearchQuery(e.target.value)}
                  placeholder="Search for videos..."
                  className="flex-1 p-2 border border-gray-300 rounded text-sm"
                />
                <button
                  onClick={searchYouTubeVideos}
                  disabled={isSearchingVideos}
                  className="bg-blue-600 text-white p-2 rounded hover:bg-blue-700 disabled:bg-gray-400"
                >
                  <Search className="h-4 w-4" />
                </button>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <span>Video length:</span>
                <select
                  value={videoDuration}
                  onChange={(e) => setVideoDuration(e.target.value)}
                  className="p-1 border rounded text-sm"
                >
                  <option value="short">Short (less than 4min)</option>
                  <option value="medium">Medium (4-20min)</option>
                  <option value="long">Long (above 20min)</option>
                </select>
              </div>

              {videoError && (
                <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded text-sm">
                  {videoError}
                </div>
              )}

              {isSearchingVideos ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="animate-spin h-5 w-5 text-blue-600" />
                </div>
              ) : youtubeResults.length > 0 ? (
                <>
                  <div className="grid gap-2 w-full max-w-full overflow-x-hidden">
                    {youtubeResults.map((video) => (
                      <div
                        key={video.id}
                        className={`p-2 border w-full overflow-auto rounded cursor-pointer transition-colors ${
                          selectedVideo?.id === video.id
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:bg-gray-50"
                        }`}
                        onClick={() => {
                          setSelectedVideo(video);
                          handleVideoBroadcast(video);
                        }}
                        title={`${video.fullTitle}\nChannel: ${video.fullChannel}\nDuration: ${video.duration}`}
                      >
                        <div className="flex gap-3 w-full">
                          <img
                            src={
                              video.thumbnail ||
                              "https://via.placeholder.com/120x90"
                            }
                            alt=""
                            className="w-20 h-12 object-cover rounded flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0 overflow-hidden">
                            <h4 className="font-medium text-sm truncate">
                              {video.title}
                            </h4>
                            <div className="flex justify-between items-center">
                              <p className="text-xs text-gray-500 truncate">
                                {video.channel}
                              </p>
                              <span className="text-xs text-gray-400 whitespace-nowrap">
                                {video.duration}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                 
                </>
              ) : (
                <div className="bg-gray-50 border border-gray-200 text-gray-600 p-4 rounded text-center text-sm">
                  {videoSearchQuery
                    ? "No videos found. Try a different search."
                    : "Search for relevant educational videos above."}
                </div>
              )}
            </div>

            <div className="flex gap-2 py-4">
              <button
                onClick={() => {
                  mode === "exercise" ? setStep(4) : setStep(2);
                }}
                className="flex-1 bg-gray-200 text-gray-800 py-2 rounded text-sm hover:bg-gray-300"
              >
                Edit Exercise
              </button>

              <button
                onClick={() => {
                  setStep(1);
                  setAIResponse(null);
                  setYoutubeResults([]);
                  setSelectedVideo(null);
                  setTopic("");
                  // Emit an event to clear the video for all users
                  if (socket && isSocketConnected) {
                    socket.emit("room-broadcast", roomInfo?.channel?.id, {
                      roomId: roomInfo?.channel?.id,
                      action: "clear-video",
                      senderId: rtcUid.current,
                      streamId
                    });
                  }
                }}
                className="flex-1 bg-blue-600 text-white py-2 rounded text-sm hover:bg-blue-700"
              >
                New Exercise
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="absolute top-0 right-0 w-full max-w-[350px] h-[calc(100vh-100px)] bg-white shadow-xl rounded-lg flex flex-col border border-gray-200">
      <div className="p-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <h2 className="font-semibold text-gray-800">Teaching Assistant</h2>
        <button
          onClick={() => {
            setShowAIAssistant(false);
            setYoutubeResults([]);
            setSelectedVideo(null);
            setVideoSearchQuery("");
            setTopic("");
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="flex-1 h-full overflow-y-auto">{renderStep()}</div>
    </div>
  );
};

export default AssistantSidebar;
