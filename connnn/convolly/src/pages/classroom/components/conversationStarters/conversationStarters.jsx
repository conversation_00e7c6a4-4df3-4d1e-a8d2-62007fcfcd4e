import React from "react";

const ConversationStarters = ({ prompts, videos, setShowAIAssistant, setAIResponse }) => {
  return (
    <div className="h-full w-full flex flex-col items-center p-4 max-h-[80vh]">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl overflow-auto">
        <h2 className="text-2xl font-bold mb-4">Conversation Starters</h2>
        <div className="space-y-4">
          {prompts?.map((prompt, index) => (
            <div key={index} className="border-b pb-4">
              <p className="text-lg font-medium">
                {index + 1}. {prompt}
              </p>
            </div>
          ))}
        </div>
        <h3 className="text-xl font-bold mt-6 mb-4">Recommended Videos</h3>
        <div className="space-y-4">
          {videos?.map((video, index) => (
            <div key={index} className="border-b pb-4">
              <p className="text-lg font-medium">{video?.title}</p>
              <a
                href={video?.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Watch on YouTube
              </a>
            </div>
          ))}
        </div>
        <div className="flex justify-end mt-6">
          <button
            onClick={() => {
              setShowAIAssistant(false);
              setAIResponse(null);
            }}
            className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
          >
            Back to Classroom
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationStarters;