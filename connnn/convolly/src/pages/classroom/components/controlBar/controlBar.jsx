// import React from "react";
// import chat from "@/assets/svgs/chat.svg";
// import screen from "@/assets/svgs/shareScreen.svg";
// import aiAssistantIcon from "@/assets/svgs/aiAssistant.svg";
// import micOn from "@/assets/svgs/micOn.svg";
// import micOff from "@/assets/svgs/micOff.svg";
// import videoOn from "@/assets/svgs/videoOn.svg";
// import videoOff from "@/assets/svgs/videoOff.svg";

// const ControlBar = ({
//   isVideoOn,
//   toggleMic,
//   isMuted,
//   toggleVideo,
//   setShowAIAssistant,
//   setShowChat,
//   showAIAssistant,
//   showChat,
//   toggleScreenShare,
//   leaveRoom,
//   setUnreadMessages,
//   unreadMessages,
//   isScreenSharing
// }) => {

//   return (
//     <div className="w-full flex min-h-[100px] justify-center items-center bg-black p-5 text-white sm:gap-5 gap-14">
//       <div className="flex flex-col justify-center items-center">
//         <img
//           id="mic-icon"
//           onClick={toggleMic}
//           src={isMuted ? micOff : micOn}
//           alt="mic icon"
//           className="cursor-pointer max-w-6"
//         />
//         <p className="">Microphone</p>
//       </div>

//       <div className="flex flex-col justify-center items-center">
//         <img
//           id="video-icon"
//           className="cursor-pointer max-w-6"
//           src={isVideoOn ? videoOn : videoOff}
//           alt="video icon"
//           onClick={toggleVideo}
//         />
//         <p className="">Video</p>
//       </div>

//       <div
//         className={`flex flex-col justify-center items-center p-2 rounded-lg ${
//           showChat ? "bg-gray-700" : ""
//         }`}
//       >
//         <div className="relative">
//           <img
//             id="chat-icon"
//             className="cursor-pointer max-w-6"
//             src={chat}
//             alt="chat icon"
//             onClick={() => {
//               if (showAIAssistant) setShowAIAssistant(false);
//               setTimeout(() => {
//                 setShowChat(!showChat);
//               }, 500);
//             }}
//           />
//           {unreadMessages > 0 && !showChat && (
//             <div className="absolute top-[-3px] right-[-3px] h-2 w-2 bg-red-500 rounded-full"></div>
//           )}
//         </div>
//         <p className="">Chat</p>
//       </div>

//       <div
//         className={`flex flex-col justify-center items-center p-2 rounded-lg ${
//           isScreenSharing ? "bg-gray-700" : ""
//         }`}
//       >
//         <img
//           onClick={toggleScreenShare}
//           id="screen-icon"
//           className="cursor-pointer max-w-6"
//           src={screen}
//           alt="screen icon"
//         />
//         <p className="text-white">Share</p>
//       </div>

//       <div
//         className={`flex flex-col justify-center items-center p-2 rounded-lg ${
//           showAIAssistant ? "bg-gray-700" : ""
//         }`}
//       >
//         <img
//           id="ai-assistant-icon"
//           className="cursor-pointer max-w-6"
//           src={aiAssistantIcon}
//           alt="AI assistant icon"
//           onClick={() => {
//             if (showChat) setShowChat(false);
//             setTimeout(() => {
//               setShowAIAssistant(!showAIAssistant);
//             }, 500);

//           }}
//         />
//         <p className="">AI Assistant</p>
//       </div>

//       <button onClick={leaveRoom} className="text-red-500 font-bold">
//         End Call
//       </button>
//     </div>
//   );
// };

// export default ControlBar;

import React from "react";
import chat from "@/assets/svgs/chat.svg";
import screen from "@/assets/svgs/shareScreen.svg";
import aiAssistantIcon from "@/assets/svgs/aiAssistant.svg";
import micOn from "@/assets/svgs/micOn.svg";
import micOff from "@/assets/svgs/micOff.svg";
import videoOn from "@/assets/svgs/videoOn.svg";
import videoOff from "@/assets/svgs/videoOff.svg";

const ControlBar = ({
  isVideoOn,
  toggleMic,
  isMuted,
  toggleVideo,
  setShowAIAssistant,
  setShowChat,
  showAIAssistant,
  showChat,
  toggleScreenShare,
  leaveRoom,
  unreadMessages,
  isScreenSharing
}) => {
  return (
    <div className="w-full flex min-h-[100px] justify-center items-center bg-black p-5 text-white sm:gap-5 gap-14">
      <div className="flex flex-col justify-center items-center">
        <img
          id="mic-icon"
          onClick={toggleMic}
          src={isMuted ? micOff : micOn}
          alt="mic icon"
          className="cursor-pointer max-w-6"
        />
        <p>Microphone</p>
      </div>

      <div className="flex flex-col justify-center items-center">
        <img
          id="video-icon"
          className="cursor-pointer max-w-6"
          src={isVideoOn ? videoOn : videoOff}
          alt="video icon"
          onClick={toggleVideo}
        />
        <p>Video</p>
      </div>

      <div
        className={`flex flex-col justify-center items-center p-2 rounded-lg ${
          showChat ? "bg-gray-700" : ""
        }`}
      >
        <div className="relative">
          <img
            id="chat-icon"
            className="cursor-pointer max-w-6"
            src={chat}
            alt="chat icon"
            onClick={() => {
              if (showAIAssistant) setShowAIAssistant(false);
              setShowChat(!showChat);
            }}
          />
          {unreadMessages > 0 && !showChat && (
            <div className="absolute top-[-3px] right-[-3px] h-2 w-2 bg-red-500 rounded-full"></div>
          )}
        </div>
        <p>Chat</p>
      </div>

      <div
        className={`flex flex-col justify-center items-center p-2 rounded-lg ${
          isScreenSharing ? "bg-gray-700" : ""
        }`}
      >
        <img
          onClick={toggleScreenShare}
          id="screen-icon"
          className="cursor-pointer max-w-6"
          src={screen}
          alt="screen icon"
        />
        <p>Share</p>
      </div>

      <div
        className={`flex flex-col justify-center items-center p-2 rounded-lg ${
          showAIAssistant ? "bg-gray-700" : ""
        }`}
      >
        <img
          id="ai-assistant-icon"
          className="cursor-pointer max-w-6"
          src={aiAssistantIcon}
          alt="AI assistant icon"
          onClick={() => {
            if (showChat) setShowChat(false);
            setShowAIAssistant(!showAIAssistant);
          }}
        />
        <p>AI Assistant</p>
      </div>

      <button
        onClick={leaveRoom}
        className="text-red-500 font-bold hover:text-red-400"
      >
        End Call
      </button>
    </div>
  );
};

export default ControlBar;
