// import React, { useState } from "react";

// const QuizGame = ({ questions, setShowAIAssistant, setAIResponse, submitAnswers }) => {
//   const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
//   const [userAnswers, setUserAnswers] = useState({});
//   const [showResults, setShowResults] = useState(false);

//   const currentQuestion = questions[currentQuestionIndex];

//   const handleAnswerSelect = (option) => {
//     setUserAnswers((prev) => ({
//       ...prev,
//       [currentQuestionIndex]: option,
//     }));
//   };

//   const handleNext = () => {
//     if (currentQuestionIndex < questions.length - 1) {
//       setCurrentQuestionIndex((prev) => prev + 1);
//     }
//   };

//   const handlePrevious = () => {
//     if (currentQuestionIndex > 0) {
//       setCurrentQuestionIndex((prev) => prev - 1);
//     }
//   };

//   const handleSubmit = () => {
//     submitAnswers(userAnswers);
//     setShowResults(true);
//   };

//   const calculateScore = () => {
//     return questions.reduce((score, question, index) => {
//       return userAnswers[index] === question.correctAnswer ? score + 1 : score;
//     }, 0);
//   };

//   const renderResults = () => {
//     const score = calculateScore();
//     return (
//       <div className="bg-white rounded-lg p-6 w-full max-w-3xl overflow-auto">
//         <h2 className="text-2xl font-bold mb-4">Quiz Results</h2>
//         <p className="text-lg mb-6">
//           You scored {score} out of {questions.length}
//         </p>
//         <div className="space-y-4">
//           {questions.map((question, index) => (
//             <div key={index} className="border-b pb-4">
//               <p className="text-lg font-medium mb-2">
//                 {index + 1}. {question.question}
//               </p>
//               <p className="text-sm">
//                 <span className="font-medium">Your Answer: </span>
//                 <span
//                   className={
//                     userAnswers[index] === question.correctAnswer
//                       ? "text-green-600"
//                       : "text-red-600"
//                   }
//                 >
//                   {userAnswers[index] || "Not answered"}
//                 </span>
//               </p>
//               <p className="text-sm">
//                 <span className="font-medium">Correct Answer: </span>
//                 <span className="text-green-600">{question.correctAnswer}</span>
//               </p>
//               <p className="text-sm mt-1">
//                 {userAnswers[index] === question.correctAnswer ? (
//                   <span className="text-green-600">Correct</span>
//                 ) : (
//                   <span className="text-red-600">Incorrect</span>
//                 )}
//               </p>
//             </div>
//           ))}
//         </div>
//         <div className="flex justify-between mt-6">
//           <button
//             onClick={() => setShowResults(false)}
//             className="bg-gray-300 text-gray-800 py-2 px-4 rounded hover:bg-gray-400"
//           >
//             Review Answers
//           </button>
//           <button
//             onClick={() => {
//               setShowAIAssistant(false);
//               setAIResponse(null);
//             }}
//             className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
//           >
//             Back to Classroom
//           </button>
//         </div>
//       </div>
//     );
//   };

//   const renderQuiz = () => {
//     return (
//       <div className="bg-white rounded-lg p-6 w-full max-w-3xl overflow-auto">
//         <h2 className="text-2xl font-bold mb-4">
//           Question {currentQuestionIndex + 1} of {questions.length}
//         </h2>
//         <p className="text-lg mb-4">{currentQuestion.question}</p>
//         <div className="space-y-2">
//           {currentQuestion.options.map((option, index) => (
//             <button
//               key={index}
//               onClick={() => handleAnswerSelect(option)}
//               className={`w-full text-left p-3 rounded-lg border ${
//                 userAnswers[currentQuestionIndex] === option
//                   ? "bg-blue-100 border-blue-500"
//                   : "border-gray-300 hover:bg-gray-100"
//               }`}
//             >
//               {option}
//             </button>
//           ))}
//         </div>
//         <div className="flex justify-between mt-6">
//           <button
//             onClick={handlePrevious}
//             disabled={currentQuestionIndex === 0}
//             className="bg-gray-300 text-gray-800 py-2 px-4 rounded disabled:opacity-50"
//           >
//             Previous
//           </button>
//           {currentQuestionIndex < questions.length - 1 ? (
//             <button
//               onClick={handleNext}
//               className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
//             >
//               Next
//             </button>
//           ) : (
//             <button
//               onClick={handleSubmit}
//               className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
//             >
//               Submit
//             </button>
//           )}
//         </div>
//       </div>
//     );
//   };

//   return (
//     <div className="h-full w-full flex flex-col items-center p-4 max-h-[80vh]">
//       {showResults ? renderResults() : renderQuiz()}
//       {!showResults && (
//         <button
//           onClick={() => {
//             setShowAIAssistant(false);
//             setAIResponse(null);
//           }}
//           className="mt-4 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
//         >
//           Back to Classroom
//         </button>
//       )}
//     </div>
//   );
// };

// export default QuizGame;

// ...................................

import React from "react";

const QuizGame = ({
  questions,
  setShowAIAssistant,
  setAIResponse,
  submitAnswers,
  socket,
  isSocketConnected,
  roomInfo,
  rtcUid,
  currentQuestionIndex,
  setCurrentQuestionIndex,
  userAnswers,
  setUserAnswers,
  showResults,
  setShowResults
}) => {
  const currentQuestion = questions[currentQuestionIndex];

  const handleAnswerSelect = (option) => {
    setUserAnswers((prev) => ({
      ...prev,
      [currentQuestionIndex]: option
    }));

    if (socket && isSocketConnected) {
      socket.emit("room-broadcast", roomInfo?.channel?.id, {
        roomId: roomInfo?.channel?.id,
        action: "select-answer",
        questionIndex: currentQuestionIndex,
        selectedOption: option,
        senderId: rtcUid.current
      });
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);

      if (socket && isSocketConnected) {
        socket.emit("room-broadcast", roomInfo?.channel?.id, {
          roomId: roomInfo?.channel?.id,
          action: "next-question",
          questionIndex: nextIndex,
          senderId: rtcUid.current
        });
      }
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      const prevIndex = currentQuestionIndex - 1;
      setCurrentQuestionIndex(prevIndex);

      if (socket && isSocketConnected) {
        socket.emit("room-broadcast", roomInfo?.channel?.id, {
          roomId: roomInfo?.channel?.id,
          action: "previous-question",
          questionIndex: prevIndex,
          senderId: rtcUid.current
        });
      }
    }
  };

  const handleSubmit = () => {
    submitAnswers(userAnswers);
    setShowResults(true);

    if (socket && isSocketConnected) {
      socket.emit("room-broadcast", roomInfo?.channel?.id, {
        roomId: roomInfo?.channel?.id,
        action: "submit-quiz",
        userAnswers,
        senderId: rtcUid.current
      });
    }
  };

  const calculateScore = () => {
    return questions.reduce((score, question, index) => {
      return userAnswers[index] === question.correctAnswer ? score + 1 : score;
    }, 0);
  };

  const renderResults = () => {
    const score = calculateScore();
    return (
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl overflow-auto">
        <h2 className="text-2xl font-bold mb-4">Quiz Results</h2>
        <p className="text-lg mb-6">
          You scored {score} out of {questions.length}
        </p>
        <div className="space-y-4">
          {questions.map((question, index) => (
            <div key={index} className="border-b pb-4">
              <p className="text-lg font-medium mb-2">
                {index + 1}. {question.question}
              </p>
              <p className="text-sm">
                <span className="font-medium">Your Answer: </span>
                <span
                  className={
                    userAnswers[index] === question.correctAnswer
                      ? "text-green-600"
                      : "text-red-600"
                  }
                >
                  {userAnswers[index] || "Not answered"}
                </span>
              </p>
              <p className="text-sm">
                <span className="font-medium">Correct Answer: </span>
                <span className="text-green-600">{question.correctAnswer}</span>
              </p>
              <p className="text-sm mt-1">
                {userAnswers[index] === question.correctAnswer ? (
                  <span className="text-green-600">Correct</span>
                ) : (
                  <span className="text-red-600">Incorrect</span>
                )}
              </p>
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-6">
          <button
            onClick={() => setShowResults(false)}
            className="bg-gray-300 text-gray-800 py-2 px-4 rounded hover:bg-gray-400"
          >
            Review Answers
          </button>
          <button
            onClick={() => {
              setShowAIAssistant(false);
              setAIResponse(null);
            }}
            className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
          >
            Back to Classroom
          </button>
        </div>
      </div>
    );
  };

  const renderQuiz = () => {
    return (
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl overflow-auto">
        <h2 className="text-2xl font-bold mb-4">
          Question {currentQuestionIndex + 1} of {questions.length}
        </h2>
        <p className="text-lg mb-4">{currentQuestion.question}</p>
        <div className="space-y-2">
          {currentQuestion.options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleAnswerSelect(option)}
              className={`w-full text-left p-3 rounded-lg border ${
                userAnswers[currentQuestionIndex] === option
                  ? "bg-blue-100 border-blue-500"
                  : "border-gray-300 hover:bg-gray-100"
              }`}
            >
              {option}
            </button>
          ))}
        </div>
        <div className="flex justify-between mt-6">
          <button
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
            className="bg-gray-300 text-gray-800 py-2 px-4 rounded disabled:opacity-50"
          >
            Previous
          </button>
          {currentQuestionIndex < questions.length - 1 ? (
            <button
              onClick={handleNext}
              className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
            >
              Next
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
            >
              Submit
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="h-full w-full flex flex-col items-center p-4 max-h-[80vh]">
      {showResults ? renderResults() : renderQuiz()}
      {!showResults && (
        <button
          onClick={() => {
            setShowAIAssistant(false);
            setAIResponse(null);
          }}
          className="mt-4 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
        >
          Back to Classroom
        </button>
      )}
    </div>
  );
};

export default QuizGame;


