#members {
  background-color: #f9fafb;
  border-left: 1px solid #e5e7eb;
}

.speaker {
  border: 1px solid #e5e7eb;
}

.speaking-indicator {
  color: green;
  transition: opacity 0.3s ease;
}

.speaking-indicator.hidden {
  opacity: 0;
  height: 0;
  overflow: hidden;
}

#remote-container > div {
  transition: all 0.3s ease-in-out;
}

.notification-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background-color: #ef4444;
  color: white;
  border-radius: 9999px;
  height: 1.25rem;
  width: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  line-height: 1rem;
}

.ai-response-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  max-width: 80%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.ai-response-content {
  white-space: pre-line;
  color: #1f2937;
  line-height: 1.6;
}

video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}