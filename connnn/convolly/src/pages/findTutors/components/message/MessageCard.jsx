import { IdCard, X } from "lucide-react";
import React from "react";
import img from "../../../../assets/images/tutor1.png";
import { Button } from "@/components/button/button";
import TextInput from "@/components/inputs/textInput";
import { useForm } from "react-hook-form";

const MessageCard = ({ onClose }) => {
	const [message, setMessage] = React.useState("");
	const { register, handleSubmit } = useForm();

	const onSubmit = (data) => {
		if (onClose) onClose();
		console.log(data);
	};

	const tutorName = "<PERSON>";
	const studentName = "John";

	return (
		<div className="sm:w-[669px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
			<div className="flex gap-[145px] pt-4 justify-between">
				<p className="text-2xl font-bold text-[#1A1A40]"></p>
				<button onClick={onClose}>
					<X />
				</button>
			</div>
			<div className="px-1 sm:px-2 min-h-[552px]">
				<div className="gap-3 ">
					<div className="flex justify-start my-4">
						<div className="">
							<img src={img} alt="tutor logo" className="w-24 h-150" />
						</div>
						<p className="text-lg sm:text-[26px] px-2 text-[#1A1A40]">
							{tutorName}
						</p>
					</div>
					<div className="pb-4 text-[#4B5563] sm:font-medium text-xs sm:text-[18px]">
						<div className="">
							<p className="mb-1">Hi {studentName}</p>
							<p className=" pb-4  font-medium ">
								I hope everything is going well for you. Thanks for looking at
								my profile
							</p>
						</div>

						<p className="mt-4 space-y-2">
							Introduce yourself to the teacher, what are your learning goals?
							How can I help you in the best way?
						</p>
					</div>
				</div>
				<div className="mt-2">
					<p className="text-md sm:text-[22px] font-bold text-[#1A1A40]">
						Send a personal message to the teacher
					</p>
					<div className="text-xs sm:text-lg">
						<form
							onSubmit={handleSubmit(onSubmit)}
							className="flex flex-col space-y-2"
						>
							<textarea
								{...register("message", { required: "Message is required" })}
								placeholder="Your Message"
								className="border border-[#E8E8E8] mt-2 rounded-md w-full min-h-[200px] p-3 focus:outline-none"
							/>
						</form>
					</div>
				</div>
				<div className="mt-6">
					<Button
						onClick={handleSubmit(onSubmit)}
						className="bg-primary border w-full text-white  rounded-md hover:bg-primary-dark transition-colors"
					>
						Send Message
					</Button>
				</div>
			</div>
		</div>
	);
};

export default MessageCard;
