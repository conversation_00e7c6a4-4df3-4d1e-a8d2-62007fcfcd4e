import React, { useState } from "react";
import fullStar from "@/assets/svgs/fullStar.svg";
import lessonIcon from "@/assets/svgs/lessons.svg";
import greenCheck from "@/assets/svgs/greencheck.svg";
import { Button } from "@/components/button/button";
import MessageCard from "../message/MessageCard";

const TutorCard = ({ tutor }) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [expanded, setExpanded] = useState(false);
	const MAX_LENGTH = 150;

	const toggleExpanded = () => setExpanded((prev) => !prev);

	const aboutMeText = tutor?.aboutMe || ""; // fallback to empty string if null
	const isLong = aboutMeText.length > MAX_LENGTH;
	const displayText =
		expanded || !isLong
			? aboutMeText
			: `${aboutMeText.slice(0, MAX_LENGTH)}...`;

	const openModal = () => {
		setIsModalOpen(true);
		console.log("button clicked");
	};
	console.log("tutor review", tutor.reviewStats);

	const closeModal = () => setIsModalOpen(false);

	return (
		<div className="border border-[#E8E8E8] rounded-xl p-3 flex gap-5 mb-5">
			<div className="w-full">
				{/* Desktop view */}
				<div className="md:flex hidden gap-5 items-stretch">
					<img
						src={tutor.image}
						alt="tutor image"
						className="w-full max-w-[290px] max-h-[200px] object-center rounded-xl"
					/>

					<div className="flex w-full gap-5">
						<div>
							<div className="flex gap-2 items-center mb-2">
								<h3 className="text-md xl:text-[26px] font-bold">
									{tutor?.fullname}
								</h3>

								{tutor?.approvalStatus === "approved" && (
									<img
										src={greenCheck}
										alt="verified icon"
										className="w-5 h-5"
									/>
								)}
							</div>

							{/* Subjects */}
							{tutor?.teachingSubjects?.map((subject) => (
								<div key={subject.id} className="flex flex-wrap mb-3">
									{subject.qualities.map((q, index) => (
										<span
											key={index}
											className="border border-[#E8E8E8] p-2 rounded-full m-1 text-sm"
										>
											{q}
										</span>
									))}
								</div>
							))}

							<div className="flex items-center gap-1 mb-2">
								<img src={lessonIcon} alt="lessons icon" />
								<span className="text-[#4B5563] mr-5">
									{tutor?.totalLessons} Lessons
								</span>
							</div>

							{/* Languages */}
							{tutor?.languages?.map((lang) => (
								<div key={lang.id} className="flex items-center gap-1">
									<span className="text-[#4B5563]">{lang.name}: </span>
									<span className="text-black mr-5">{lang.level}</span>
								</div>
							))}
						</div>

						<div className="flex gap-5 lg:min-w-[45%] justify-end">
							<div className="flex flex-col">
								<div className="flex gap-2 items-center">
									{tutor?.reviewStats ? (
										<p className="text-[#4B5563]">
											<span className="flex items-center gap-1">
												<img
													src={fullStar}
													alt="star icon"
													className="w-6 h-6"
												/>
												{tutor.reviewStats.avgRating}
											</span>
											<span className="flex">
												<p>({tutor.reviewStats.totalReview} </p>
												<p>
													review {tutor.reviewStats.totalReview > 1 ? "s" : ""})
												</p>
											</span>
										</p>
									) : (
										<>
											<img src={fullStar} alt="star icon" className="w-6 h-6" />
											<p className="text-[#4B5563]">No reviews yet</p>
										</>
									)}
								</div>
							</div>

							<div className="flex flex-col">
								<h2 className="font-bold text-2xl">US${tutor?.basePrice}</h2>
								<p className="text-[#4B5563] shrink-0"></p>
							</div>
						</div>
					</div>
				</div>

				{/* Mobile View */}
				<div className="md:hidden">
					{isModalOpen && (
						<div className="inset-0 fixed z-50 flex items-center justify-center bg-black bg-opacity-50">
							<div className="w-sm">
								<MessageCard onClose={closeModal} />
							</div>
						</div>
					)}

					<div className="flex gap-3 mb-3">
						<img
							src={tutor?.image}
							alt="tutor"
							className="w-full max-w-[100px] max-h-[120px] object-cover rounded-lg"
						/>

						<div className="w-full">
							<div className="flex items-center mb-2">
								<h3 className="text-lg font-bold">
									{tutor?.firstname} {tutor?.lastname}
								</h3>
								<img src={greenCheck} alt="verified" className="w-5 h-5" />
							</div>

							<div className="flex gap-3">
								<div className="flex flex-col">
									<div className="flex gap-2 items-center">
										<img src={fullStar} alt="star" className="w-5 h-5" />
										<h2 className="font-bold text-lg">{tutor?.rating}</h2>
									</div>
									<p className="text-[#4B5563]">{tutor?.reviews}</p>
								</div>

								<div className="flex flex-col">
									<h2 className="font-bold text-lg">US${tutor?.basePrice}</h2>
									<p className="text-[#4B5563]">50-min lesson</p>
								</div>
							</div>
						</div>
					</div>

					<div className="flex flex-wrap mb-3">
						{tutor?.teachingSubjects?.map((subject) => (
							<div key={subject.id} className="flex flex-wrap mb-3">
								{subject.qualities.map((q, index) => (
									<span
										key={index}
										className="border border-[#E8E8E8] p-2 rounded-full m-1 text-sm"
									>
										{q}
									</span>
								))}
							</div>
						))}
					</div>

					<div className="flex items-center gap-1 mb-2">
						<img src={lessonIcon} alt="lessons" />
						<span className="text-[#4B5563] mr-5">
							{tutor?.totalLessons} Lessons
						</span>
					</div>

					<div className="flex items-center gap-1">
						{tutor?.languages?.map((lang) => (
							<div key={lang.id}>
								<span className="text-[#4B5563]">{lang.name}: </span>
								<span className="text-black">{lang.level}</span>
							</div>
						))}
					</div>
				</div>

				{/* About Me */}
				<div>
					<h3 className="sm:text-xl text-secondary font-bold mb-3 mt-5">
						About
					</h3>
					<p className="text-[#4B5563] sm:text-lg mb-3">{displayText}</p>
					{isLong && (
						<a
							href="#"
							onClick={(e) => {
								e.preventDefault();
								toggleExpanded();
							}}
							className="underline block mb-5 text-primary font-bold"
						>
							{expanded ? "Read less" : "Read more"}
						</a>
					)}
					<Button className="w-full lg:hidden">Book free lesson</Button>
					<button
						onClick={openModal}
						className="w-full mt-2 border-primary border rounded-md py-2 fill-none lg:hidden"
					>
						Send Message
					</button>
				</div>
			</div>

			{/* Video and CTA buttons (Desktop only) */}
			<div className="lg:max-w-[240px] grow hidden lg:flex flex-col gap-4">
				<video
					src={tutor?.introVideo}
					controls
					className="w-full h-full object-cover rounded-xl"
				/>

				<Button className="w-full text-lg">Book free lesson</Button>
				<button
					onClick={openModal}
					className="w-full text-lg border rounded-md px-2 py-1 text-primary border-primary"
				>
					Send Message
				</button>
			</div>

			{/* Modal (shared) */}
			{isModalOpen && (
				<div className="inset-0 fixed z-50 flex items-center justify-center bg-black bg-opacity-50">
					<div className="sm:w-[669px]">
						<MessageCard onClose={closeModal} />
					</div>
				</div>
			)}
		</div>
	);
};

export default TutorCard;
