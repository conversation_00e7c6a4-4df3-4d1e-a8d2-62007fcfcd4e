import React from "react";
import <PERSON><PERSON><PERSON> from "./TopLogo";
import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import InputField from "../../../components/inputs";
import { Button } from "../../../components/button/button";
import { useForgotPasswordMutation } from "../../../redux/slices/authApiSlice";
import usePost from "../../../hooks/usePost";

const ForgotPassword = () => {
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors }
  } = useForm();
  const navigate = useNavigate();

  // use the post mutation hook to handle the forgot password mutation
  const { handlePost: handleForgotPassword, isLoading: forgotPasswordLoading } =
    usePost(useForgotPasswordMutation);

  const forgotPassword = async (data) => {
    const res = await handleForgotPassword(data);

    if (res) {
      reset();
      navigate("/verify-password");
    }
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <TopLogo />

      <div className="flex flex-1 flex-col items-center justify-center px-4 mx-auto sm:px-6">
        <div className="w-full max-w-[511.33px] space-y-8">
          <div className="text-center gap-2 sm:h-[92px] w-full">
            <h1 className="text-2xl sm:text-4xl font-bold text-[#1A1A40]">
              Forgot password?
            </h1>
            <p className="text-sm sm:text-xl mt-2 font-medium gap-2 text-[18px] text-[#4B5563]">
              Don't worry we will help you recover your password
            </p>
          </div>

          <div className="relative h-[326px] gap-6">
            <div className="text-center w-full gap-[4px]">
              <h2 className="sm:text-[22px] text-lg text-[#1A1A40] font-bold">
                Please enter your registered email ID
              </h2>
              <p className="text-sm sm:text-lg text-[18px] text-[#606060] mb-5">
                We will send a verification code to the registered ID
              </p>
            </div>

            <div className="gap-7">
              <form onSubmit={handleSubmit(forgotPassword)}>
                <InputField
                  placeHolder="Enter your email address"
                  fieldName="email"
                  label="Email"
                  isRequired={true}
                  fieldType="email"
                  register={register}
                />

                <Button
                  className="w-full h-[50px] mb-3"
                  disabled={forgotPasswordLoading}
                >
                  Reset password
                </Button>
              </form>

              <div className="text-center w-full text-sm mt-5">
                <Link
                  to="/signin"
                  className="font-medium text-[18px] text-primary hover:underline"
                >
                  Back to Login
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
