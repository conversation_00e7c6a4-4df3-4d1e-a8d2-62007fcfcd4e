import React, { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import TopLogo from "./TopLogo";

const VerifyPassword = () => {
	const [code, setCode] = useState(["", "", "", ""]);
	const [timer, setTimer] = useState(30);
	const inputRefs = useRef([]);

	// Handle OTP input
	const handleChange = (value, index) => {
		if (!/^\d?$/.test(value)) return;

		const newCode = [...code];
		newCode[index] = value;
		setCode(newCode);

		if (value && index < 3) {
			inputRefs.current[index + 1]?.focus();
		}
	};

	// Handle backspace to focus previous input
	const handleKeyDown = (e, index) => {
		if (e.key === "Backspace" && !code[index] && index > 0) {
			inputRefs.current[index - 1]?.focus();
		}
	};

	// Countdown for resend
	useEffect(() => {
		if (timer <= 0) return;

		const countdown = setInterval(() => {
			setTimer((prev) => prev - 1);
		}, 1000);

		return () => clearInterval(countdown);
	}, [timer]);

	const formatTime = (seconds) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins < 10 ? "0" : ""}${mins}:${secs < 10 ? "0" : ""}${secs}`;
	};

	return (
		<div className="min-h-screen bg-white flex flex-col">
			<TopLogo />

			<div className="flex flex-1 flex-col items-center justify-center px-4 py-8 sm:px-6">
				<div className="w-full max-w-[511.33px] space-y-8">
					<div className="text-center">
						<h1 className="text-xl sm:text-[38px] font-bold text-[#1A1A40]">
							Password verification
						</h1>
						<p className="font-medium text-[18px] text-[#4B5563]">
							We sent <NAME_EMAIL>
						</p>
					</div>

					<div className="space-y-7">
						<div className="space-y-6">
							<div className="text-center space-y-1">
								<p className="text-xl sm:text-[22px] text-[#1A1A40] font-bold">
									Please enter your verification code
								</p>
								<p className="text-[#606060] text-[18px] font-medium">
									Please enter the 4 digit code sent to your mail
								</p>
							</div>

							<div className="flex justify-center gap-4">
								{code.map((digit, index) => (
									<input
										key={index}
										ref={(el) => (inputRefs.current[index] = el)}
										type="text"
										maxLength="1"
										value={digit}
										onChange={(e) => handleChange(e.target.value, index)}
										onKeyDown={(e) => handleKeyDown(e, index)}
										className="w-[60px] h-[60px] border-[1px] border-[#E8E8E8] outline-primary hover:border-[#E8E8E8] rounded text-center text-xl"
									/>
								))}
							</div>
						</div>

						<div>
							<button
								type="submit"
								className="w-full h-[50px] text-white text-center rounded-md font-bold text-[22px] bg-primary"
							>
								Verify
							</button>
						</div>

						<div className="text-center w-full text-sm pt-2">
							{timer > 0 ? (
								<p className="font-medium text-[18px] text-primary">
									Resend code in: {formatTime(timer)}
								</p>
							) : (
								<Link
									to="#"
									onClick={() => setTimer(30)}
									className="font-medium text-[18px] text-primary hover:underline"
								>
									Resend code
								</Link>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default VerifyPassword;
