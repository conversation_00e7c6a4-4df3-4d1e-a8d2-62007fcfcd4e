import { Check } from "lucide-react";
import { Button } from "@/components/button/button";

const SuccessModal = ({ isOpen, onClose, onContinue }) => {
  if (!isOpen) return null;

  return (
    <div className="">
      <div className="fixed inset-0 flex items-center justify-center z-50">
        {/* Overlay */}
        <div
          className="absolute inset-0 bg-black bg-opacity-30"
          onClick={onClose}
        ></div>
        {/* Modal Content */}
        <div className="w-[534px] top-[539px] left-[453px] bg-white rounded-lg shadow-lg gap-[20px] pt-[34px] pr-[26px] pb-[34px] pl-[26px] text-center z-10">
          {/* Success Icon */}
          <div className="flex gap-3 items-center justify-center mb-6">
            <div className="bg-[#1BC717] w-[40px] h-[40px] rotate-0 rounded-[797.81px] p-2 flex items-center justify-center">
              <Check className="text-white w-[33px] h-[33px] border-1 border-[#FFFFFF] rounded-e-full" />
            </div>
          </div>

          {/* Success Message */}
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Congratulations
          </h2>
          <p className="text-gray-600 mb-8">
            Your password has been
            <br />
            created successfully
          </p>

          <Button
            className="w-full"
            onClick={onContinue || onClose}
            disabled={false}
          >
            Reset password
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
