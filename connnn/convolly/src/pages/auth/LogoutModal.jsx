import React from "react";
import { X } from "lucide-react";
import { Button } from "@/components/button/button";
import { useSelector, useDispatch } from "react-redux";
import { useLogoutMutation } from "@/redux/slices/authApiSlice";

const LogoutModal = ({ onCancel }) => {
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const [logout] = useLogoutMutation();
	const dispatch = useDispatch();

	const handleConfirmLogout = async () => {
		try {
			// Clear UI state immediately for better perceived performance
			localStorage.clear();
			dispatch({ type: "auth/logout" });

			// Don't wait for the API response before redirecting
			logout({ userId: user?.id })
				.unwrap()
				.catch((err) => console.error("Logout API error:", err));

			// Redirect immediately
			window.location.href = "/";
		} catch (err) {
			console.error("Failed during logout cleanup:", err);
		}
	};

	return (
		<div className="sm:w-[700px] sm:h-[300px] w-sm mx-2 p-6 bg-white rounded-lg shadow-lg">
			<div className="flex justify-between pt-4 items-center pb-4">
				<p className="text-lg sm:text-2xl font-bold text-[#1A1A40]">Logout</p>
				<button
					onClick={onCancel}
					className="text-gray-500 hover:text-gray-700"
				>
					<X size={30} className="text-[#1A1A40]" />
				</button>
			</div>
			<div className="mt-6">
				<div className="mb-8">
					<h1 className="text-lg sm:text-[22px] text-[#1A1A40] font-semibold text-center">
						Are you sure you want to log out of this session?
					</h1>
				</div>
				<div className="flex items-center justify-center gap-4">
					<Button
						onClick={handleConfirmLogout}
						className="flex-1 h-[50px] bg-white border text-black hover:text-white rounded-md hover:bg-[#B00313]"
					>
						Yes, Logout
					</Button>
					<Button
						onClick={onCancel}
						className="flex-1 h-[50px] bg-primary text-white"
					>
						No, Cancel
					</Button>
				</div>
			</div>
		</div>
	);
};

export default LogoutModal;
