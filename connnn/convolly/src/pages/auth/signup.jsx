import React, { useEffect } from "react";
import logo from "../../assets/svgs/logo.svg";
import signWithGoogle from "../../assets/svgs/signWithGoogle.svg";
import signWithLinkedIn from "../../assets/svgs/signInWithLinkedin.svg";
import InputField from "../../components/inputs";
import { useForm } from "react-hook-form";
import tutorSignupBackground from "../../assets/images/tutorSignupBackground.png";
import studentSignupBackground from "../../assets/images/studentSignupBackground.png";
import { Button } from "../../components/button/button";
import { useNavigate } from "react-router-dom";
import { useLocalSignUpMutation } from "../../redux/slices/authApiSlice";
import usePost from "../../hooks/usePost";
import { jwtDecode } from "jwt-decode";
import { useParams } from "react-router-dom";
import Loader from "@/components/loader/loader";
import {
	decryptPrivateKey,
	encryptPrivateKey,
	exportPrivateKey,
	exportPublicKey,
	generateKeyPair,
} from "../messaging/utils/crypto";

const SignupPage = () => {
	const {
		register,
		handleSubmit,
		control,
		reset,
		formState: { errors },
	} = useForm();

	const navigate = useNavigate();
	const { role } = useParams();
	const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;

	// use the generic post mutation hook to handle the signin mutation
	const { handlePost: handleSignup, isLoading: signingUp } = usePost(
		useLocalSignUpMutation
	);

	const registerUser = async (data) => {
		const keyPair = await generateKeyPair();
		const exportedPublicKey = await exportPublicKey(keyPair.publicKey);
		const { encryptedPrivateKey, iv, salt } = await encryptPrivateKey(
			keyPair.privateKey,
			data.password
		);

		const res = await handleSignup({
			...data,
			role: role || "student",
			provider: data.provider || "local",
			encryptedData: {
				publicKey: exportedPublicKey.toString(),
				encryptedPrivateKey: encryptedPrivateKey.toString(),
				iv,
				salt,
			},
		});

		if (res) {
			reset();
			navigate("/signin");
		}
	};

	const handleGoogleCallback = async (response) => {
		// Decode the ID token to get user info
		const decodedToken = jwtDecode(response?.credential);

		// Extract user info from the decoded token
		const { given_name, family_name, email } = decodedToken;

		const userData = {
			firstname: given_name,
			lastname: family_name,
			email: email,
			provider: "google",
		};

		try {
			const data = await registerUser(userData);
		} catch (err) {
			console.error("Error during Google signup:", err);
		}
	};

	useEffect(() => {
		if (window.google) {
			google.accounts.id.initialize({
				client_id: GOOGLE_CLIENT_ID,
				callback: handleGoogleCallback,
			});

			google.accounts.id.renderButton(
				document.getElementById("googleSignUpBtn"),
				{
					theme: "outline",
					size: "large",
					shape: "pill",
					width: "100%",
				}
			);
		}
	}, []);

	return (
		<div className="flex min-h-screen overflow-hidden relative">
			{signingUp && <Loader />}

			<div
				className="hidden lg:flex fixed left-0 top-0 w-1/2 h-screen flex-col z-10"
				style={{
					backgroundImage: `url(${
						role == "student" ? studentSignupBackground : tutorSignupBackground
					})`,
					backgroundSize: "cover",
					backgroundPosition: "center",
				}}
			>
				<div className="absolute inset-0 bg-black/30 z-0" />

				<div className="relative z-10 w-full h-full p-12 flex flex-col justify-end">
					<h2 className="text-2xl text-white font-bold mb-2 leading-[50px]">
						{role == "student"
							? "Unlock Fluent English — One Lesson at a Time"
							: "Share Your Knowledge. Inspire Learners Worldwide."}
					</h2>
					<p className="text-white max-w-[600px]">
						{role == "student"
							? "Join thousands of learners improving their English skills with interactive lessons, real-time practice, and personalized guidance."
							: "Join our global network of English tutors, connect with eager students, and earn by doing what you love—teaching."}
					</p>
				</div>
			</div>

			<div className="ml-auto w-full lg:w-1/2 overflow-y-auto flex flex-col justify-center lg:p-12 md:p-8 p-5 min-h-screen">
				<img
					src={logo}
					alt="convolly logo"
					className="sm:w-[190px] w-[120px] mb-10"
				/>

				<h2 className="md:text-4xl text-2xl font-bold mb-2 sm:leading-[50px] leading-[50px]">
					Create account
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					Start creating your tutor profile
				</p>

				<div className="sm:flex items-center gap-5 mb-5">
					<div
						className="flex border-[1px] border-[#E8E8E8] rounded-[30px] gap-2 cursor-pointer grow p-1 px-3 justify-center max-sm:mb-5"
						id="googleSignUpBtn"
					>
						{/* <img src={signWithGoogle} alt="sign with google" />
            <span className="sm:text-lg text-[#121212]">
              Sign up with Google
            </span> */}
					</div>

					<div
						className="flex border-[1px] border-[#E8E8E8] rounded-[30px] gap-2 cursor-pointer grow p-2 px-4 justify-center"
						id="linkedinSignInBtn"
					>
						<img src={signWithLinkedIn} alt="sign with linkedin" />
						<span className="sm:text-lg text-[#121212]">
							Sign in with LinkedIn
						</span>
					</div>
				</div>

				<div className="flex items-center text-center mb-5">
					<hr className="flex-1 border-none h-[1px] bg-[#E8E8E8]" />
					<span className="py-0 px-[10px] text-xs border-[#E8E8E8] border rounded-md text-[#A4A4A4] bg-[#FAFAFA] p-1">
						OR
					</span>
					<hr className="flex-1 border-none h-[1px] bg-[#E8E8E8]" />
				</div>

				<form onSubmit={handleSubmit(registerUser)} className="w-full">
					<div className="sm:flex items-center gap-5">
						<InputField
							label="First name"
							register={register}
							fieldName="firstname"
							placeHolder="Enter your first name"
							isRequired={true}
							error={errors?.firstName?.message}
							disabled={signingUp}
						/>

						<InputField
							label="Last name"
							register={register}
							fieldName="lastname"
							placeHolder="Enter your last name"
							isRequired={true}
							error={errors?.lastName?.message}
							disabled={signingUp}
						/>
					</div>

					<InputField
						label="Email address"
						register={register}
						fieldName="email"
						fieldType="email"
						placeHolder="Enter your email address"
						isRequired={true}
						error={errors?.email?.message}
						disabled={signingUp}
					/>

					<InputField
						label="Password"
						register={register}
						fieldName="password"
						fieldType="password"
						placeHolder="Enter your password"
						isRequired={true}
						validate={true}
						disabled={signingUp}
						registerOptions={{
							pattern: {
								value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{8,}$/,
								message:
									"Password must be at least 8 characters, include uppercase, lowercase, and a special character",
							},
						}}
						error={errors?.password?.message}
					/>

					<Button className="w-full h-[50px] mb-3" disabled={signingUp}>
						Save And Continue
					</Button>
				</form>

				<p className="sm:text-lg">
					Already have an account?{" "}
					<a className="text-primary font-semibold" href="/signin">
						Sign in
					</a>
				</p>
			</div>
		</div>
	);
};

export default SignupPage;
