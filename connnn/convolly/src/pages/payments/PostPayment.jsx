import React, { useState, useEffect } from "react";
import { CustomSelect } from "@/components/select/select";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import MultiSelect from "@/components/select/multiSelect";
import Morning from "@/assets/svgs/morning";
import Afternoon from "@/assets/svgs/afternoon";
import Evening from "@/assets/svgs/evening";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import SuccessModal from "@/components/modal/successModal";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/navbar/navbar";
import SkipModal from "./components/SkipModal";

const PostPayment = ({
	setActiveTab,
	studentDetails,
	refetchStudentDetails,
}) => {
	const studentId = useSelector((state) => state?.app?.userInfo?.user?.id);
	const navigate = useNavigate();
	const [selectedDurations, setSelectedDurations] = useState([]);
	const [showSuccess, setShowSuccess] = useState(false);
	const [showSkipModal, setShowSkipModal] = useState(false);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		watch,
		formState: { errors },
	} = useForm();

	// Options for PageOne
	const industryOptions = [
		{ value: true, label: "Yes, I do" },
		{ value: false, label: "No" },
	];

	const lessonDurationOptions = [
		{ value: "25mins", label: "25mins" },
		{ value: "50mins", label: "50mins" },
		{ value: "both", label: "Both" },
	];

	const languageOptions = [
		{ value: "English", label: "English" },
		{ value: "French", label: "French" },
		{ value: "Spanish", label: "Spanish" },
		{ value: "German", label: "German" },
	];

	// Options for PageTwo
	const levelOptions = [
		{ value: "basic", label: "Basic" },
		{ value: "conversational", label: "Conversational" },
		{ value: "fluent", label: "Fluent" },
		{ value: "native", label: "native" },
	];

	const timezones = [
		{
			label: "18:03 (GMT+0) - Africa/Lagos",
			value: "18:03 (GMT+0) - Africa/Lagos",
		},
		{
			label: "13:03 (GMT+0) - America/New York",
			value: "13:03 (GMT+0) - America/New York",
		},
		{
			label: "18:03 (GMT+0) - Europe/London",
			value: "18:03 (GMT+0) - Europe/London",
		},
		{
			label: "22:33 (GMT+0) - Asia/Kolkata",
			value: "22:33 (GMT+0) - Asia/Kolkata",
		},
		{
			label: "02:03 (GMT+0) - Asia/Tokyo",
			value: "02:03 (GMT+0) - Asia/Tokyo",
		},
		{
			label: "03:03 (GMT+0) - Australia/Sydney",
			value: "03:03 (GMT+0) - Australia/Sydney",
		},
	];

	const daysOfWeek = [
		{ label: "Monday", value: "Monday" },
		{ label: "Tuesday", value: "Tuesday" },
		{ label: "Wednesday", value: "Wednesday" },
		{ label: "Thursday", value: "Thursday" },
		{ label: "Friday", value: "Friday" },
		{ label: "Saturday", value: "Saturday" },
		{ label: "Sunday", value: "Sunday" },
	];

	const timeOfDay = [
		{ name: "Morning", time: "9-11", Icon: Morning },
		{ name: "Afternoon", time: "12-18", Icon: Afternoon },
		{ name: "Evening", time: "19-22", Icon: Evening },
	];

	const { handlePost: handleUpdateStudent, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	const handleSkipContinue = () => {
		setShowSkipModal(false);
		navigate("/trial-lesson");
	};

	const handleContinue = () => {
		setShowSkipModal(false);
		navigate("/trial-lesson");
	};
	const closeModal = () => {
		setShowSkipModal(false);
	};

	useEffect(() => {
		if (studentDetails) {
			// PageOne values
			setValue("learningReasons", studentDetails?.learningReasons);
			setValue(
				"needIndustryKnowledge",
				String(studentDetails?.needIndustryKnowledge)
			);
			setValue(
				"preferredLessonDuration",
				studentDetails?.preferredLessonDuration
			);
			setValue("nativeLanguage", studentDetails?.nativeLanguage);
			setValue("skillsToImprove", studentDetails?.skillsToImprove);

			// PageTwo values
			setValue("level", studentDetails?.languages?.[0]?.level);
			setValue("daysAvailable", studentDetails?.daysAvailable);
			setValue("location", studentDetails?.location?.state);
		}
	}, [studentDetails, setValue]);

	const onboardingComplete = () => {
		setShowSuccess(false);
		navigate("/student/dashboard");
	};

	const handleSubmitForm = async (data) => {
		console.log(data);

		// Format the availability
		const timeAvailable = selectedDurations.map((label) => {
			const timeSlot = timeOfDay.find(
				(t) => t.name.toLowerCase() === label.toLowerCase()
			);
			const [from, to] = timeSlot.time.split("-");

			return {
				label,
				from,
				to,
			};
		});

		// Remove the level and put it in the languages array
		const { level, ...rest } = data;

		const res = await handleUpdateStudent({
			...rest,
			timeAvailable,
			languages: [{ name: "English", level }],
			nativeLanguage: { name: data?.nativeLanguage },
			location: { state: data?.location },
			userId: studentId,
			role: "student",
		});

		if (res) {
			setShowSuccess(true);
			refetchStudentDetails();
		}
	};

	return (
		<div className="mx-2 sm:mx-4 pt-2 py-4">
			<div className="gap-[133px] top-[16px]">
				<Navbar />
			</div>
			<div className="w-full mx-auto px-4 sm:mt-14 mt-6 text-secondary">
				<SuccessModal
					isOpen={showSuccess}
					onClose={onboardingComplete}
					title="Congratulations"
					message="You have onboarded successfully"
					onButtonClick={onboardingComplete}
					buttonText="Continue"
				/>
				{showSkipModal && (
					<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
						<SkipModal
							isOpen={showSkipModal}
							onClose={closeModal}
							onSkip={handleSkipContinue}
						/>
					</div>
				)}

				<form onSubmit={handleSubmit(handleSubmitForm)}>
					<div className="gap-4 mx-auto mb-7">
						<h1 className="text-[#1A1A40] py-4 sm:text-4xl text-2xl font-bold">
							Here are questions you already answered. Feel free to edit your
							choices
						</h1>

						<p className="text-[#4B5563] sm:text-lg">
							Personalize your experience and ensure smooth navigation from the
							start. You can select more than one options.{" "}
						</p>
					</div>
					<div>
						<h1 className="py-4 text-[#1A1A40] sm:text-3xl font-bold text-2xl">
							Learning goals and Objectives
						</h1>
						<p className="text-[#4B5563] py-4 sm:text-lg">
							Personalize your experience and ensure smooth navigation from the
							start. You can select more than one options
						</p>
						<br />
						<MultiSelect
							options={[
								{
									label: "Job promotion or career growth",
									value: "Job promotion or career growth",
								},
								{
									label: "Communication with international clients",
									value: "Communication with international clients",
								},
								{
									label: "Relocating or studying abroad",
									value: "Relocating or studying abroad",
								},
								{
									label: "Preparing for job interviews",
									value: "Preparing for job interviews",
								},
								{
									label: "Collaboration with global teams",
									value: "Collaboration with global teams",
								},
								{
									label: "Presentations and meetings",
									value: "Presentations and meetings",
								},
								{
									label: "Enhancing technical ideas or products",
									value: "Enhancing technical ideas or products",
								},
							]}
							placeholder="Select area of focus"
							label="What is your main reason for improving your English?"
							control={control}
							name="learningReasons"
							parentClassName="mb-7"
						/>

						<MultiSelect
							options={[
								{ label: "Accent reduction", value: "Accent reduction" },
								{ label: "Phonetics", value: "Phonetics" },
								{
									label: "Listening and Reading comprehension",
									value: "Listening and Reading comprehension",
								},
								{
									label: "Conversational fluency",
									value: "Conversational fluency",
								},
								{
									label: "Grammar and Vocabulary focus",
									value: "Grammar and Vocabulary focus",
								},
								{ label: "Academic writing", value: "Academic writing" },
							]}
							placeholder="Select your English goal"
							label="What English skills do you want to improve?"
							control={control}
							name="skillsToImprove"
							parentClassName="mb-7"
						/>

						<p className="max-sm:text-sm text-secondary mb-2">
							Do you need industry-specific knowledge?
						</p>

						{industryOptions.map(({ label, value }, index) => (
							<label
								className={`flex items-center gap-2 w-fit ${
									index < industryOptions.length - 1 ? "mb-2" : "mb-7"
								}`}
								key={value}
							>
								<input
									type="radio"
									value={value}
									{...register("needIndustryKnowledge")}
									className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
								/>
								<span className="sm:text-base text-sm">{label}</span>
							</label>
						))}

						<p className="max-sm:text-sm text-secondary mb-2">
							What is your preferred lesson duration?
						</p>
						{lessonDurationOptions.map(({ label, value }, index) => (
							<label
								className={`flex items-center gap-2 w-fit ${
									index < lessonDurationOptions.length - 1 ? "mb-2" : "mb-7"
								}`}
								key={value}
							>
								<input
									type="radio"
									value={value}
									{...register("preferredLessonDuration")}
									className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
								/>
								<span className="sm:text-base text-sm">{label}</span>
							</label>
						))}

						<CustomSelect
							placeholder="Select your language"
							label="What is your native language?"
							options={languageOptions}
							control={control}
							name="nativeLanguage"
							isRequired={true}
							error={errors?.language?.message}
							className="p-5 py-2"
							parentClassName="mb-1"
						/>
						<a href="#" className="">
							Add another language
						</a>
					</div>
					<br />
					<div>
						<CustomSelect
							placeholder="Select your English level"
							label="What is your current English level?"
							options={levelOptions}
							control={control}
							name="level"
							isRequired={true}
							error={errors?.level?.message}
							className="p-5 py-[22px]"
							parentClassName="mb-7"
						/>

						<MultiSelect
							options={daysOfWeek}
							placeholder="Select days of the week you will take lessons"
							label="When can you take lessons?"
							control={control}
							name="daysAvailable"
							parentClassName="mb-7"
						/>

						<p className="max-sm:text-sm text-secondary mb-2">
							What is your preferred time of day for lessons?
						</p>

						{timeOfDay.map(({ name, time, Icon }, index) => (
							<label
								className={`flex items-center gap-2 w-fit ${
									index < timeOfDay.length - 1 ? "mb-2" : "mb-7"
								}`}
								key={name}
							>
								<input
									type="checkbox"
									value={name.toLowerCase()}
									onChange={(e) => {
										const { checked, value } = e.target;
										setSelectedDurations((prev) =>
											checked
												? [...prev, value]
												: prev.filter((val) => val !== value)
										);
									}}
									className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
								/>
								<div className="sm:text-base text-sm flex gap-2">
									{<Icon />}
									{name + " (" + time + " )"}
								</div>
							</label>
						))}

						<CustomSelect
							placeholder="13:03 (GMT+0) - America/New York"
							label="Choose your time zone"
							options={timezones}
							control={control}
							name="location"
							isRequired={true}
							error={errors?.location?.message}
							className="p-5 py-[22px]"
							parentClassName="mb-10"
						/>
						<div className="flex flex-row justify-between gap-5 mt-10">
							<button
								className="w-[333px] rounded-md text-sm sm:text-xl bg-[#D2D2D2] text-black h-[50px] mt-5"
								disabled={updating}
								type="submit"
								onClick={setShowSkipModal}
							>
								Skip
							</button>
							<button
								className="sm:w-full rounded-md text-white text-sm sm:text-xl bg-primary w-full h-[50px] mt-5"
								disabled={updating}
								onClick={handleContinue}
								type="submit"
							>
								Save And Continue
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	);
};

export default PostPayment;
