import React from "react";
import { Card, CardContent } from "../../../../components/card/card";
import fullStar from "../../../../assets/svgs/fullStar.svg";
import user1 from "../../../../assets/images/user1.png";
import user2 from "../../../../assets/images/user2.png";
import user3 from "../../../../assets/images/user3.png";
import amazon from "../../../../assets/svgs/amazon.svg";
import apple from "../../../../assets/svgs/apple.svg";
import google from "../../../../assets/svgs/google.svg";
import microsoft from "../../../../assets/svgs/microsoft.svg";
import netflix from "../../../../assets/svgs/netflix.svg";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import AutoCarousel from "../../../../components/carousel/AutoCarousel";

export const WhatClientSays = () => {
	const statsData = [
		{ value: "1000+", label: "Active Learners" },
		{ value: "500+", label: "Expert Tutors" },
		{ value: "20+", label: "Industries Covered" },
	];

	const testimonials = [
		{
			quote:
				'"As a financial analyst working with international clients, finding a tutor who understood banking terminology in Spanish was crucial. My Convolly tutor had 10 years of experience in finance and created lessons specifically for client meetings and presentations."',
			name: "James Wilson",
			position: "Financial Analyst at Goldman Sachs",
			avatar: user1,
		},
		{
			quote:
				'"I needed to learn technical Japanese for my engineering role at Toyota. My tutor had worked in automotive engineering and taught me specialized vocabulary that I use daily. The industry-specific approach made all the difference."',
			name: "Rebecca Thompson",
			position: "Mechanical Engineer at Toyota",
			avatar: user2,
		},
		{
			quote:
				'"Our company expanded to Brazil, and I needed to learn Portuguese quickly. My Convolly tutor focused on healthcare terminology and cultural nuances specific to medical discussions. Within 6 months, I was able to lead meetings with our Brazilian partners."',
			name: "Dr. Olivia Martinez",
			position: "Medical Director at Johnson & Johnson",
			avatar: user3,
		},
		{
			quote:
				'"Our company expanded to Brazil, and I needed to learn Portuguese quickly. My Convolly tutor focused on healthcare terminology and cultural nuances specific to medical discussions. Within 6 months, I was able to lead meetings with our Brazilian partners."',
			name: "Dr. Olivia Martinez",
			position: "Medical Director at Johnson & Johnson",
			avatar: user3,
		},
		{
			quote:
				'"Our company expanded to Brazil, and I needed to learn Portuguese quickly. My Convolly tutor focused on healthcare terminology and cultural nuances specific to medical discussions. Within 6 months, I was able to lead meetings with our Brazilian partners."',
			name: "Dr. Olivia Martinez",
			position: "Medical Director at Johnson & Johnson",
			avatar: user3,
		},
	];

	const companyLogos = [microsoft, google, netflix, amazon, apple];

	return (
		<section className="flex flex-col w-full py-[50px] px-3 bg-[#eff3ff]">
			<div className="flex flex-col items-center gap-14 w-full">
				<div className="flex flex-wrap items-center justify-center sm:gap-12 gap-8">
					{statsData.map((stat, index) => (
						<Card
							key={index}
							className="w-full md:w-[423px] shadow-[0px_1px_2px_#0000000d] bg-white rounded-2xl"
						>
							<CardContent className="flex flex-col items-center gap-2 p-8">
								<span className="font-semibold text-primary text-3xl text-center tracking-normal leading-[40px]">
									{stat.value}
								</span>
								<span className="font-normal text-gray-700 text-base text-center tracking-normal leading-6">
									{stat.label}
								</span>
							</CardContent>
						</Card>
					))}
				</div>

				<div className="flex flex-col gap-14 w-full">
					<h2 className="font-bold text-secondary sm:text-[46px] text-3xl sm:text-center tracking-normal leading-tight">
						What Our Clients Say
					</h2>

					<div className="flex flex-col items-center gap-14 w-full">
						<Swiper
							modules={[Autoplay]}
							slidesPerView={3}
							spaceBetween={30}
							loop={true}
							speed={8000} // Increased speed value for slower movement
							autoplay={{
								delay: 20,
								disableOnInteraction: false,
								reverseDirection: false,
							}}
							className="w-full smooth-scroll-slider"
							// Add responsive breakpoints for mobile view
							breakpoints={{
								// when window width is >= 320px (mobile)
								320: {
									slidesPerView: 1,
									spaceBetween: 20,
								},
								// when window width is >= 640px (tablet)
								640: {
									slidesPerView: 2,
									spaceBetween: 20,
								},
								// when window width is >= 768px (desktop)
								768: {
									slidesPerView: 3,
									spaceBetween: 30,
								},
							}}
						>
							{testimonials.map((testimonial, index) => (
								<SwiperSlide key={index}>
									<Card className="shadow-[0px_1px_2px_#0000000d] bg-white rounded-2xl hover:shadow-xl sm:hover:scale-105 transition-all h-full">
										<CardContent className="flex flex-col h-full p-6 gap-6">
											<div className="flex flex-col gap-4">
												<div className="flex items-center">
													{[...Array(5)].map((_, i) => (
														<img key={i} src={fullStar} alt="full star" />
													))}
												</div>
												<p className="text-gray-700 text-sm leading-6 tracking-normal">
													{testimonial.quote}
												</p>
											</div>
											<div className="flex items-center gap-4 mt-auto">
												<img
													src={testimonial?.avatar}
													alt={`${testimonial?.name} image`}
												/>
												<div className="flex flex-col gap-1">
													<h3 className="font-semibold text-secondary text-base leading-6 tracking-normal">
														{testimonial.name}
													</h3>
													<p className="text-gray-600 text-sm leading-5 tracking-normal">
														{testimonial.position}
													</p>
												</div>
											</div>
										</CardContent>
									</Card>
								</SwiperSlide>
							))}
						</Swiper>

						<div className="flex flex-col items-center gap-8 w-full">
							<h3 className="font-semibold text-gray-900 text-lg text-center tracking-normal leading-6">
								Trusted by Leading Companies
							</h3>
							<div className="flex flex-wrap items-center justify-center sm:gap-[63px] gap-7 w-full">
								{companyLogos.map((logo, index) => (
									<img
										key={index}
										className="w-[30px] h-8"
										alt={`Company logo ${index + 1}`}
										src={logo}
									/>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};
