import React from "react";
import { Card, CardContent } from "../../../../components/card/card";
import legal from "../../../../assets/svgs/legal.svg";
import engineering from "../../../../assets/svgs/engineering.svg";
import financeAndBanking from "../../../../assets/svgs/financeAndBanking.svg";
import healthCare from "../../../../assets/svgs/healthCare.svg";
import marketing from "../../../../assets/svgs/marketing.svg";
import realEstate from "../../../../assets/svgs/realEstate.svg";
import technology from "../../../../assets/svgs/technology.svg";
import travelAndHospitality from "../../../../assets/svgs/travelAndHospitality.svg";

export const BrowswByCategories = () => {
	const industries = [
		{ name: "Finance & Banking", icon: financeAndBanking },
		{ name: "Technology", icon: technology },
		{ name: "Healthcare", icon: healthCare },
		{ name: "Legal", icon: legal },
		{ name: "Marketing", icon: marketing },
		{ name: "Real Estate", icon: realEstate },
		{ name: "Engineering", icon: engineering },
		{ name: "Travel & Hospitality", icon: travelAndHospitality },
	];

	return (
		<section className="w-full py-[50px] sm:px-8 px-3 bg-gray-50">
			<div className="flex flex-col items-center gap-12 max-w-[1374px] mx-auto">
				<h2 className="w-full text-secondary font-semibold text-[30px] leading-[44px] tracking-normal sm:text-[46px] sm:leading-[54px]">
					Browse by Industry
				</h2>

				{/* Grid layout for better alignment and responsiveness */}
				<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 gap-6 w-full justify-items-center">
					{industries.map((industry, index) => (
						<Card
							key={index}
							className="w-full max-w-[160px] h-[140px] bg-white rounded-lg shadow-[0px_1px_2px_#0000000d] border-none hover:shadow-lg transition-shadow duration-200 cursor-pointer"
						>
							<CardContent className="flex flex-col items-center justify-center gap-3 p-4 h-full">
								{/* Icon container with consistent sizing */}
								<div className="flex w-14 h-14 items-center justify-center bg-[#1fc16b1a] rounded-full flex-shrink-0">
									<img
										className="w-[22px] h-[22px] object-contain"
										alt={`${industry.name} icon`}
										src={industry.icon}
									/>
								</div>

								{/* Text container with consistent height */}
								<div className="flex items-center justify-center text-center min-h-[40px]">
									<p className="text-secondary text-[14px] leading-[18px] font-normal max-w-full break-words">
										{industry.name}
									</p>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		</section>
	);
};
