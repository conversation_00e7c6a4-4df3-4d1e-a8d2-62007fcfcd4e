import React from "react";
import { <PERSON><PERSON> } from "../../../../components/button/button";
import { Card, CardContent } from "../../../../components/card/card";
import startLearning from "../../../../assets/images/startLearning.png";
import findPerfectMatch from "../../../../assets/images/findPerfectMatch.png";
import bookTrialSession from "../../../../assets/images/bookTrialSession.png";

const howItWorksSteps = [
	{
		number: "1",
		title: "Find Your Perfect Match",
		description: `Search for tutors based on your industry, language needs, and schedule
    preferences. Our platform matches you with experienced professionals who
    understand your specific field and can help you achieve your language learning
    goals.`,
		imagePath: findPerfectMatch,
	},
	{
		number: "2",
		title: "Book a Trial Session",
		description: `Schedule a 25-minute trial lesson to ensure the tutor is the right fit for your
    learning goals. Experience our platform's features and discuss your specific
    needs with your potential tutor.`,
		imagePath: bookTrialSession,
	},
	{
		number: "3",
		title: "Start Learning",
		description: `Begin your personalized language learning journey with regular sessions tailored to your career needs. Track your progress and build confidence in professional communication.`,
		imagePath: startLearning,
	},
];

export const HowItWorks = () => {
	const gotoSignup = () => {
		window.location.href = "signup/student";
	};
	return (
		<section className="w-full py-[50px] sm:px-8 px-3 bg-[#f7faff]">
			<div className="flex flex-col items-center gap-14 max-w-[1372px] mx-auto">
				<div className="flex flex-col items-center gap-[40px] lg:gap-[80px] w-full">
					<h2 className="w-full text-secondary font-semibold text-[30px] leading-[44px] tracking-normal sm:text-[46px] sm:leading-[54px] md:text-center">
						How It Works
					</h2>

					<div className="flex flex-col items-start gap-14 w-full">
						{howItWorksSteps.map((step, index) => (
							<div
								key={step.number}
								className={`flex flex-col lg:gap-14 gap-9 items-center w-full ${
									index % 2 !== 0 ? "lg:flex-row-reverse" : "lg:flex-row"
								}`}
							>
								<div
									className="relative lg:w-[600px] w-full md:h-[342px] h-[200px] rounded-lg max-lg:order-2"
									style={{
										background: `url(${step.imagePath}) 50% 50% / cover`,
									}}
								/>

								<div className="flex flex-col lg:w-[716px] items-start gap-3 max-lg:order-1">
									<div className="relative w-[52px] h-[52px] bg-primary rounded-full flex items-center justify-center">
										<span className="font-semibold text-white text-[20px] tracking-[-0.4px] leading-[28px]">
											{step.number}
										</span>
									</div>

									<Card className="border-none shadow-none bg-transparent">
										<CardContent className="flex flex-col items-start gap-4 p-0">
											<h3 className="font-semibold text-secondary sm:text-2xl text-xl tracking-[-0.48px] leading-[32px]">
												{step.title}
											</h3>

											<p className="text-gray-600 sm:text-lg text-base font-normal tracking-[-0.32px] leading-[24px]">
												{step.description}
											</p>
										</CardContent>
									</Card>
								</div>
							</div>
						))}
					</div>
				</div>

				<Button onClick={gotoSignup} className="w-[198px] h-[50px]">
					<span className="font-semibold text-white text-[20px] text-center tracking-[-0.4px] leading-[28px]">
						Get Started
					</span>
				</Button>
			</div>
		</section>
	);
};
