import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { BookIcon, ChevronLeft, Edit } from "lucide-react";
import Loader from "@/components/loader/loader";
import DeleteprofileConfirmationModal from "./components/DeleteprofileConfirmationModal";
import ChangeEmailForm from "./components/ChangeEmailForm";
import useGet from "@/hooks/useGet";
import { useGetTutorDetailsQuery } from "@/redux/slices/admin/adminTutorsApiSlice";
import user from "@/assets/svgs/userVector.svg";
import AdminTutorsReviewCard from "../components/tutors/AdminTutorsReviewCard";

const AdminTutorProfile = () => {
	const { id } = useParams();
	const [showDeleteModal, setShowDeleteModal] = useState(false);
	const [showEditEmailModal, setShowEditEmailModal] = useState(false);

	const { data: tutor, isLoading: gettingTutorDetails } = useGet(
		useGetTutorDetailsQuery,
		id,
		!!id
	);

	const closeEditModal = () => {
		setShowEditEmailModal(false);
	};

	const navigate = useNavigate();

	const seeAllTutorReviews = () => {
		navigate(`/admin/tutors/${id}/reviews`);
	};

	return (
		<div className="bg-white rounded-lg">
			{gettingTutorDetails && <Loader />}

			<div className="p-4 border rounded-md mb-4">
				<h1 className="text-xl my-4 font-bold text-[#1A1A40]">
					Tutor's Details
				</h1>

				<div className="flex items-start justify-between">
					<div className="flex items-center gap-4">
						<img
							src={tutor?.image || user}
							alt={tutor?.fullname}
							className="w-14 h-14 rounded-full object-cover"
						/>

						<div>
							<h2 className="text-xl font-semibold">{tutor?.fullname}</h2>
							<p className="text-gray-600">Teaches: English Language</p>
						</div>

						<div className="flex flex-col ml-10">
							<p className="text-[#1A1A40] text-lg">$ {tutor?.basePrice}</p>
							<p className="text-gray-600 text-sm">per hour</p>
						</div>
					</div>

					<div>
						<button
							onClick={() => setShowDeleteModal(true)}
							className="bg-[#D00416] px-4 py-2 font-semibold text-white rounded-lg"
						>
							Delete Profile
						</button>
					</div>
				</div>

				<div className="mt-4">
					<div className="flex">
						<div className="flex flex-wrap gap-2 mt-2 items-center">
							<h3 className="font-medium text-[#1A1A40]">Languages:</h3>

							{tutor?.languages.map((lang, index) => (
								<span
									key={index}
									className="px-1 text-gray-700 rounded-full text-sm"
								>
									{lang.name} ({lang.level})
								</span>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* about us */}
			<div className="p-4 border mb-4 rounded-md">
				<h2 className="text-xl font-semibold mb-4">About Us</h2>
				<div className="text-[#4B5563]">
					<p>{tutor?.aboutMe}</p>
				</div>

				<div className="mt-4">
					<div className="mt-4 flex flex-wrap gap-2">
						{tutor?.teachingSubjects?.map((subject, index) => (
							<div key={index}>
								{subject.qualities?.map((quality, qIndex) => (
									<span
										key={`quality-${qIndex}`}
										className="rounded-xl bg-[#EBEDF0] text-[#4B5563] px-3 py-1 text-sm mr-2"
									>
										{quality}
									</span>
								))}

								{subject.specialities?.map((speciality, sIndex) => (
									<span
										key={`speciality-${sIndex}`}
										className="rounded-xl bg-[#EBEDF0] text-[#4B5563] px-3 py-1 text-sm mr-2"
									>
										{speciality}
									</span>
								))}
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Personal Information */}
			<div className="p-4 border rounded-md">
				<h2 className="text-xl font-semibold mb-4">Personal Information</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<p className="text-gray-600 text-sm">Full name</p>
						<p className="font-medium">{tutor?.fullname}</p>
					</div>

					<div>
						<p className="text-gray-600 text-sm">Email address</p>
						<div className="flex items-center gap-1">
							<p className="font-medium">{tutor?.email}</p>
							<Edit onClick={() => setShowEditEmailModal(true)} size={16} />
						</div>
					</div>

					<div>
						<p className="text-gray-600 text-sm">Country of residence</p>
						<p className="font-medium">{tutor?.country}</p>
					</div>

					<div>
						<p className="text-gray-600 text-sm">Phone number</p>
						<p className="font-medium">{tutor?.phone}</p>
					</div>
				</div>
			</div>

			<div className="p-4 border my-4 rounded-md">
				<h2 className="text-xl font-semibold mb-4">Certificates</h2>

				<div className="text-[#4B5563] gap-4 py-4">
					{tutor?.certificates?.length > 0 ? (
						tutor.certificates.map((certificate) => (
							<div
								key={certificate.id}
								className="flex my-4 items-center gap-4"
							>
								<img
									src={certificate.file?.url}
									alt={certificate.title}
									className="w-14 h-14 rounded-full object-cover"
								/>
								<div>
									<h2 className="text-lg font-semibold capitalize">
										{certificate.title}
									</h2>
									<p className="text-gray-800">{certificate.subject}</p>
									<p className="text-gray-600">
										{new Date(certificate.startDate).toLocaleDateString()} -{" "}
										{new Date(certificate.endDate).toLocaleDateString()}
									</p>
								</div>
							</div>
						))
					) : (
						<p className="text-gray-500">No certificates uploaded yet.</p>
					)}
				</div>
			</div>
			<div className="p-4 border my-4 rounded-md">
				<div className="flex items-center justify-between mb-1">
					<h2 className="text-xl font-semibold mb-4">Reviews</h2>
					<p
						onClick={seeAllTutorReviews}
						className="text-md cursor-pointer  pr-6 font-medium text-primary"
					>
						See all
					</p>
				</div>
				<AdminTutorsReviewCard />
				<AdminTutorsReviewCard />
			</div>

			<div>
				{showDeleteModal && (
					<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
						<DeleteprofileConfirmationModal
							onCancel={() => setShowDeleteModal(false)}
						/>
					</div>
				)}
			</div>

			<div>
				{showEditEmailModal && (
					<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
						<ChangeEmailForm onClose={closeEditModal} />
					</div>
				)}
			</div>
		</div>
	);
};

export default AdminTutorProfile;
