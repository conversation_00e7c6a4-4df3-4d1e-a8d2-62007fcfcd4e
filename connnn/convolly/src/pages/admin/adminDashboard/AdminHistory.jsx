import React from "react";
// import TransactionHistoryModal from "../../components/history/TransactionHistoryModal";
import DateRangePicker from "./components/DateRangePicker";
import TableComponent from "@/components/table/table";
import { useGetDashboardTransactionaQuery } from "@/redux/slices/admin/adminDashboardApiSlice";
import useGet from "@/hooks/useGet";
import Loader from "@/components/loader/loader";

const AdminHistory = () => {
	const { data: transactions, isLoading: gettingTransactions } = useGet(
		useGetDashboardTransactionaQuery,
		""
	);

	const getStatusStyle = (status) => {
		switch (status.toLowerCase()) {
			case "paid":
				return "bg-[#ECFDF3] text-[#037847]";
			case "pending":
				return "bg-[#FFDB431A] text-[#DFB400]";
			case "failed":
				return "bg-[#FB37481A] text-[#D00416]";
			case "refunded":
				return "bg-[#4D00FF1A] text-[#3C00C6]";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const columns = [
		{ header: "Transaction ID", accessor: "transactionId" },
		{ header: "Tutor's Name", accessor: "tutorName" },
		{ header: "Wise email address", accessor: "wiseEmail" },
		{ header: "Payout method", accessor: "payoutMethod" },
		{ header: "Date", accessor: "date" },
		{ header: "Time", accessor: "time" },
		{ header: "Amount", accessor: "amount" },
		{
			header: "Status",
			accessor: "status",
			render: (row) => (
				<span
					className={`px-2 py-1 rounded-full text-sm ${getStatusStyle(
						row.status
					)}`}
				>
					{row.status}
				</span>
			),
		},
	];

	const headerActions = (
		<div className="items-end gap-4 flex text-sm">
			<p className="text-[#1A1A40] text-md p-2">Filter by:</p>
			<DateRangePicker />
			<input
				type="text"
				className="p-2 border rounded-lg"
				placeholder="Transaction ID"
			/>
			{/* <TransactionHistoryModal /> */}
		</div>
	);

	return (
		<div>
			{gettingTransactions && <Loader />}

			<TableComponent
				columns={columns}
				headerActions={headerActions}
				data={transactions || []}
				title="Transaction History"
			/>
		</div>
	);
};

export default AdminHistory;
