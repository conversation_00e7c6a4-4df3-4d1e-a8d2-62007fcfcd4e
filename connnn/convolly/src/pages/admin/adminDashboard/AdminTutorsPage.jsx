import React from "react";
import { useGetAllTutorsQuery } from "@/redux/slices/admin/adminTutorsApiSlice";
import useGet from "@/hooks/useGet";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/button/button";
import Loader from "@/components/loader/loader";
import user from "@/assets/svgs/userVector.svg";

import LessonsIcon from "@/assets/images/adminDashboard/lessonsIcon.png";
import RatingIcon from "@/assets/images/adminDashboard/ratingIcon.png";
import studentsIcon from "@/assets/images/adminDashboard/studentsIcon.png";
import hoursIcon from "@/assets/images/adminDashboard/hoursIcon.png";

const AdminTutorsPage = () => {
  const navigate = useNavigate();

  const { data: tutors, isLoading: gettingTutors } = useGet(
    useGetAllTutorsQuery,
    ""
  );

  return (
    <div className="">
      {gettingTutors && <Loader />}

      <h1 className="font-semibold text-2xl mb-5">Tutors</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tutors?.map((tutor) => (
          <div className="w-auto bg-white border rounded-lg shadow-md p-4 justify-between hover:shadow-lg transition-shadow duration-200">
            <div className="flex justify-between">
              <div className="text-[#1A1A40] flex items-center gap-4">
                <img
                  src={tutor.image || user}
                  alt={tutor.fullname}
                  className="w-14 h-14 rounded-full object-cover"
                />

                <div>
                  <p className="text-[18px] font-medium">{tutor.fullname}</p>
                  <p className="text-gray-600 space-x-2 gap-2">
                    Teaches: English Language
                  </p>
                </div>
              </div>
              <div className="flex flex-col">
                <p className="text-[#1A1A40] text-lg">$ {tutor.basePrice}</p>
                <p className="text-gray-600 text-sm">per hour</p>
              </div>
            </div>
            <hr className=" py-3 my-3" />

            <div>
              <div className="flex items-center gap-5 sm:gap-10 mb-2">
                <div className="flex gap-1" >
                  <img
                    src={RatingIcon}
                    alt="LessonsIcon"
                    className="w-7 h-6 object-cover"
                  />{" "}
                  <p className="space-x-2">{tutor.rating}</p>
                </div>

                <div className="flex space-x-2 pr-4">
                  <img
                    src={LessonsIcon}
                    alt="LessonsIcon"
                    className="w-7 h-6 object-cover"
                  />{" "}
                  <p className="text-gray-600">{tutor.totalLessons} Lessons</p>
                </div>

                {/* <div className="flex space-x-2 pr-4">
                  <img
                    src={studentsIcon}
                    alt="LessonsIcon"
                    className="w-7 h-6 object-cover"
                  />{" "}
                  <p className="text-gray-600">{tutor.tutors} students</p>
                </div> */}
              </div>

              <div className="flex flex-wrap items-center gap-2 mt-2">
                <p className="text-[#1A1A40] text-md">Languages: </p>
                {tutor?.languages?.map((language, index) => (
                  <span
                    key={index}
                    className="px-2 text-gray-700 rounded-full text-sm"
                  >
                    {language?.name} ({language?.level})
                  </span>
                ))}
              </div>
            </div>

            <div className="mt-4 items-center">
              <Button
                className="h-50 w-full"
                onClick={() => navigate(`/admin/tutors/${tutor.id}`)}
              >
                View Profile
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AdminTutorsPage;
