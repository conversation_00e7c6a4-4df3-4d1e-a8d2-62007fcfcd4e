import { DeleteIcon, EditIcon, TvIcon, Check, X } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";

const AdminReviews = () => {
	const { register, handleSubmit, reset } = useForm();
	const [reviews, setReviews] = useState([
		{
			id: "1",
			tutorName: "Dr. <PERSON>",
			studentName: "Michael Brown",
			subscription: "Premium Monthly",
			wiseEmail: "<EMAIL>",
			amount: "$45.00",
			date: "15/06/2023",
			status: "completed",
			isEditing: false,
		},
		{
			id: "2",
			tutorName: "<PERSON><PERSON>",
			studentName: "<PERSON>",
			subscription: "Basic Quarterly",
			wiseEmail: "<EMAIL>",
			amount: "$120.00",
			date: "14/06/2023",
			status: "pending",
			isEditing: false,
		},
	]);

	const [isAddingNew, setIsAddingNew] = useState(false);

	const onSubmit = (data) => {
		if (isAddingNew) {
			setReviews([
				...reviews,
				{ ...data, id: Date.now().toString(), isEditing: false },
			]);
			setIsAddingNew(false);
		} else {
			setReviews(
				reviews.map((review) =>
					review.id === data.id ? { ...data, isEditing: false } : review
				)
			);
		}
		reset();
	};

	const handleEdit = (id) => {
		setReviews(
			reviews.map((review) => ({
				...review,
				isEditing: review.id === id,
			}))
		);
	};

	const handleCancel = (id) => {
		if (isAddingNew) {
			setIsAddingNew(false);
		} else {
			setReviews(
				reviews.map((review) => ({
					...review,
					isEditing: false,
				}))
			);
		}
		reset();
	};

	const handleDelete = (id) => {
		setReviews(reviews.filter((review) => review.id !== id));
	};

	return (
		<div>
			<div className="flex justify-between mb-4">
				<h1 className="text-2xl font-semibold">Reviews</h1>
				<div className="text-[#A4A4A4] text-[14px] p-4">
					<div className="items-end gap-4 flex">
						<p className="text-[#1A1A40] text-md p-2">Filter by:</p>
						<input
							type="text"
							className="p-2 border text-center rounded-lg"
							placeholder="Enter username or email"
						/>
						<button
							onClick={() => setIsAddingNew(true)}
							className="px-4 text-white flex items-center gap-2 py-2 border rounded-lg bg-primary hover:bg-primary-dark transition-colors"
						>
							<TvIcon size={18} />
							<p>Add new review</p>
						</button>
					</div>
				</div>
			</div>

			<div>
				<table className="w-full">
					<thead className="border-b border-[#E0E0E0]">
						<tr className="bg-[#F5F5F5]">
							<th className="p-4 text-left">Tutor Name</th>
							<th className="p-4 text-left">Student Name</th>
							<th className="p-4 text-left">Subscription</th>
							<th className="p-4 text-left">Wise Email</th>
							<th className="p-4 text-left">Amount</th>
							<th className="p-4 text-left">Date</th>
							<th className="p-4 text-left">Status</th>
							<th className="p-4 text-left">Actions</th>
						</tr>
					</thead>
					<tbody>
						{isAddingNew && (
							<tr className="border-b shadow border-[#E0E0E0]">
								<td colSpan="8">
									<form onSubmit={handleSubmit(onSubmit)} className="p-4">
										<div className="grid grid-cols-7 gap-4 items-center">
											<input
												{...register("tutorName", { required: true })}
												className="p-2 border rounded"
												placeholder="Tutor Name"
											/>
											<input
												{...register("studentName", { required: true })}
												className="p-2 border rounded"
												placeholder="Student Name"
											/>
											<select
												{...register("subscription", { required: true })}
												className="p-2 border rounded"
											>
												<option value="Basic Monthly">Basic Monthly</option>
												<option value="Premium Monthly">Premium Monthly</option>
												<option value="Basic Quarterly">Basic Quarterly</option>
											</select>
											<input
												{...register("wiseEmail", {
													required: true,
													pattern: /^\S+@\S+$/i,
												})}
												className="p-2 border rounded"
												placeholder="Wise Email"
											/>
											<input
												{...register("amount", { required: true })}
												className="p-2 border rounded"
												placeholder="Amount"
											/>
											<input
												type="date"
												{...register("date", { required: true })}
												className="p-2 border rounded"
											/>
											<select
												{...register("status", { required: true })}
												className="p-2 border rounded"
											>
												<option value="pending">Pending</option>
												<option value="completed">Completed</option>
												<option value="cancelled">Cancelled</option>
											</select>
											<div className="flex gap-2">
												<button type="submit" className="text-green-500">
													<Check size={18} />
												</button>
												<button
													type="button"
													onClick={() => handleCancel()}
													className="text-red-500"
												>
													<X size={18} />
												</button>
											</div>
										</div>
									</form>
								</td>
							</tr>
						)}

						{reviews.map((review) => (
							<tr key={review.id} className="border-b shadow border-[#E0E0E0]">
								{review.isEditing ? (
									<>
										<td colSpan="8">
											<form onSubmit={handleSubmit(onSubmit)} className="p-4">
												<div className="grid grid-cols-7 gap-4 items-center">
													<input
														{...register("tutorName", { required: true })}
														defaultValue={review.tutorName}
														className="p-2 border rounded"
													/>
													<input
														{...register("studentName", { required: true })}
														defaultValue={review.studentName}
														className="p-2 border rounded"
													/>
													<select
														{...register("subscription", { required: true })}
														defaultValue={review.subscription}
														className="p-2 border rounded"
													>
														<option value="Basic Monthly">Basic Monthly</option>
														<option value="Premium Monthly">
															Premium Monthly
														</option>
														<option value="Basic Quarterly">
															Basic Quarterly
														</option>
													</select>
													<input
														{...register("wiseEmail", {
															required: true,
															pattern: /^\S+@\S+$/i,
														})}
														defaultValue={review.wiseEmail}
														className="p-2 border rounded"
													/>
													<input
														{...register("amount", { required: true })}
														defaultValue={review.amount}
														className="p-2 border rounded"
													/>
													<input
														type="date"
														{...register("date", { required: true })}
														defaultValue={review.date}
														className="p-2 border rounded"
													/>
													<select
														{...register("status", { required: true })}
														defaultValue={review.status}
														className="p-2 border rounded"
													>
														<option value="pending">Pending</option>
														<option value="completed">Completed</option>
														<option value="cancelled">Cancelled</option>
													</select>
													<div className="flex gap-2">
														<button type="submit" className="text-green-500">
															<Check size={18} />
														</button>
														<button
															type="button"
															onClick={() => handleCancel(review.id)}
															className="text-red-500"
														>
															<X size={18} />
														</button>
													</div>
												</div>
												<input
													type="hidden"
													{...register("id")}
													defaultValue={review.id}
												/>
											</form>
										</td>
									</>
								) : (
									<>
										<td className="p-4">{review.tutorName}</td>
										<td className="p-4">{review.studentName}</td>
										<td className="p-4">{review.subscription}</td>
										<td className="p-4">{review.wiseEmail}</td>
										<td className="p-4">{review.amount}</td>
										<td className="p-4">{review.date}</td>
										<td className="p-4">
											<span
												className={`px-2 py-1 rounded-full text-xs ${
													review.status === "completed"
														? "bg-green-100 text-green-800"
														: review.status === "pending"
														? "bg-yellow-100 text-yellow-800"
														: "bg-red-100 text-red-800"
												}`}
											>
												{review.status}
											</span>
										</td>
										<td className="p-4">
											<div className="flex gap-2">
												<button
													onClick={() => handleEdit(review.id)}
													className="text-blue-500 hover:text-blue-700"
												>
													<EditIcon size={16} />
												</button>
												<button
													onClick={() => handleDelete(review.id)}
													className="text-red-500 hover:text-red-700"
												>
													<DeleteIcon size={16} />
												</button>
											</div>
										</td>
									</>
								)}
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default AdminReviews;
