import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";

const StudentChart = () => {
  const data = [
    { name: "Subscription", value: 124, color: "#68DBF2", change: "+22" },
    { name: "Free trial", value: 124, color: "#FF92AE", change: "+32" },
    { name: "Single lesson", value: 124, color: "#FFEF5E", change: "+42" }
  ];

  const totalStudents = data.reduce((sum, item) => sum + item.value, 0);

  const COLORS = ["#68DBF2", "#FF92AE", "#FFEF5E"];

  return (
    <div className="bg-white rounded-lg border border-gray-200 px-6 py-4 w-[440px]">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">Students</h2>

      <div className="flex items-center justify-center mb-6">
        <div className="relative">
          <ResponsiveContainer width={200} height={200}>
            <PieChart>
              <Pie
                data={data}
                cx={100}
                cy={100}
                innerRadius={60}
                outerRadius={90}
                paddingAngle={0}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index]} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-2xl font-bold text-gray-900">
              {totalStudents}
            </span>
          </div>
        </div>
      </div>

      <div className="space-y-4 flex justify-between">
        {data.map((item, index) => (
          <div key={item.name} className="items-center ">
            <div className="flex items-center gap-3">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-sm text-gray-600">{item.name}</span>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-900">
                {item.value}
              </span>
              <span className="text-xs text-green-600 font-medium">
                ({item.change})
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StudentChart;
