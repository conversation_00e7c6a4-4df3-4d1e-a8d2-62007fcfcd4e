import React from "react";
import TableComponent from "@/components/table/table";
import useGet from "@/hooks/useGet";
import { useGetBookingsQuery } from "@/redux/slices/admin/adminBookingsApiSlice";
import Loader from "@/components/loader/loader";

const AdminBookings = () => {
  const { data: bookings, isLoading: gettingBookings } = useGet(
    useGetBookingsQuery,
    ""
  );

  const columns = [
    {
      header: "Student Name",
      accessor: "studentId.fullname",
    },
    {
      header: "Student Email",
      accessor: "studentId.email",
    },
    {
      header: "Tutor Name",
      accessor: "tutorId.fullname",
    },
    {
      header: "Tutor Email",
      accessor: "tutorId.email",
    },
    {
      header: "Plan Type",
      accessor: "planType",
    },
    { header: "Lessons/Week", accessor: "lessonsPerWeek" },
    {
      header: "Monthly Price",
      accessor: "monthlyPrice"
    },
    { header: "Status", accessor: "status" },
    {
      header: "Start Date",
      accessor: "currentPeriodStart"
    },
    {
      header: "End Date",
      accessor: "currentPeriodEnd"
    }
  ];

  return (
    <div className="overflow-hidden">
      {gettingBookings && <Loader />}

      <h1 className="text-2xl font-semibold">Bookings</h1>

      <TableComponent columns={columns} data={bookings || []} title="" />
    </div>
  );
};

export default AdminBookings;
