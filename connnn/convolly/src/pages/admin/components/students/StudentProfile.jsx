import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/button/button";
import { BookIcon, ChevronLeft, Edit } from "lucide-react";
import Loader from "@/components/loader/loader";
import DeleteprofileConfirmationModal from "../profile/DeleteprofileConfirmationModal";
import ChangeEmailForm from "../profile/ChangeEmailForm";

// Mock data - this would normally come from an API or context
const mockStudents = [
	{
		id: "1",
		name: "<PERSON>",
		img: "https://randomuser.me/api/portraits/men/1.jpg",
		currentLevel: "Beginner (A1)",
		lessons: 30,
		tutors: 15,
		rating: 20,
		languages: [
			{ language: "English", level: "A1" },
			{ language: "French", level: "C2" },
			{ language: "Spanish", level: "B2" },
		],
		personalInfo: {
			firstName: "Obi",
			middleName: "-",
			lastName: "<PERSON>",
			email: "<EMAIL>",
			country: "United State of America",
			phone: "+****************",
		},
	},
	// Add more students as needed
];

const StudentProfile = () => {
	const { id } = useParams();
	const navigate = useNavigate();
	const [student, setStudent] = useState(null);
	const [loading, setLoading] = useState(true);
	const [showDeleteModal, setShowDeleteModal] = React.useState(false);
	const [showEditEmailModal, setShowEditEmailModal] = React.useState(false);

	const openModal = () => {
		setShowDeleteModal(true);
	};
	const closeModal = () => {
		setShowDeleteModal(false);
	};

	useEffect(() => {
		// Simulate API fetch
		const fetchStudent = () => {
			setLoading(true);
			const foundStudent = mockStudents.find((s) => s.id === id);

			setTimeout(() => {
				setStudent(foundStudent);
				setLoading(false);
			}, 500); // Simulate network delay
		};

		fetchStudent();
	}, [id]);

	if (loading) {
		return <Loader />;
	}

	return (
		<div className="bg-white rounded-lg p-6 max-w-6xl mx-auto">
			{/* Header with back button */}

			{/* Student Overview */}
			<div className="p-4 border rounded-md mb-8">
				<h1 className="text-xl my-4 font-bold text-[#1A1A40]">
					Student Details
				</h1>

				<div className="flex items-start justify-between">
					<div className="flex items-center gap-4">
						<img
							src={student.img}
							alt={student.name}
							className="w-14 h-14 rounded-full object-cover"
						/>
						<div>
							<h2 className="text-xl font-semibold">{student.name}</h2>
							<p className="text-gray-600">
								Current level: {student.currentLevel}
							</p>
						</div>
					</div>
					<div>
						<button
							onClick={openModal}
							className="bg-[#D00416] px-4 py-2 font-semibold text-white rounded-lg"
						>
							Delete Profile
						</button>
					</div>
				</div>

				<div className=" mt-6">
					<div className="flex space-x-6">
						<div className="flex space-x-1 text-center">
							<BookIcon size={20} />
							<p className="font-medium">{student.lessons}</p>
							<p className="text-gray-600">Lessons</p>
						</div>
						<div className="flex space-x-1 text-center">
							<BookIcon size={20} />{" "}
							<p className="font-medium">{student.tutors}</p>
							<p className="text-gray-600">Tutors</p>
						</div>
						<div className="flex space-x-1 text-center">
							<BookIcon size={20} />
							<p className="text-gray-600">Rating</p>
							<p className="font-medium">{student.rating}</p>
						</div>
					</div>
					{/* Languages */}
					<div className="flex">
						<div className="flex flex-wrap gap-2 mt-2">
							<h3 className="font-medium text-[#1A1A40]">Languages:</h3>

							{student.languages.map((lang, index) => (
								<span
									key={index}
									className="px-3 py-1 text-gray-700 rounded-full text-sm"
								>
									{lang.language} ({lang.level})
								</span>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* Personal Information */}
			<div className="p-4 border rounded-md">
				<h2 className="text-xl font-semibold mb-4">Personal Information</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<div className="space-y-4">
						<div>
							<p className="text-gray-600 text-sm">First name</p>
							<p className="font-medium">{student.personalInfo.firstName}</p>
						</div>
						<div className="flex">
							<div>
								<p className="text-gray-600 text-sm">Email address</p>
								<p className="font-medium">{student.personalInfo.email}</p>
								<Edit onClick={() => setShowEditEmailModal(true)} size={16} />
							</div>
						</div>
					</div>
					<div className="space-y-4">
						<div>
							<p className="text-gray-600 text-sm">Middle name</p>
							<p className="font-medium">{student.personalInfo.middleName}</p>
						</div>
						<div>
							<p className="text-gray-600 text-sm">Country of residence</p>
							<p className="font-medium">{student.personalInfo.country}</p>
						</div>
					</div>
					<div className="space-y-4">
						<div>
							<p className="text-gray-600 text-sm">Last name</p>
							<p className="font-medium">{student.personalInfo.lastName}</p>
						</div>
						<div>
							<p className="text-gray-600 text-sm">Phone number</p>
							<p className="font-medium">{student.personalInfo.phone}</p>
						</div>
					</div>
				</div>
			</div>
			{showDeleteModal && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<DeleteprofileConfirmationModal
						onCancel={() => setShowDeleteModal(false)}
					/>
				</div>
			)}

			{showEditEmailModal && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<ChangeEmailForm onClose={() => setShowEditEmailModal(false)} />
				</div>
			)}
		</div>
	);
};

export default StudentProfile;
