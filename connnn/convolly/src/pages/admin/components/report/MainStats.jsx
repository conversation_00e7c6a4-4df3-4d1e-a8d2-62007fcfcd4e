import CoinsIcon from "@/assets/svgs/admin/CoinsIcon";

const MainStats = ({ stats }) => {
	// Default values in case stats aren't provided
	const {
		totalRevenue = 77,
		totalCommision = 100,
		totalProcessingFee = 203,
	} = stats || {};

	const cards = [
		{
			Icon: CoinsIcon,
			value: totalRevenue,
			label: "Total revenue",
		},
		{
			Icon: CoinsIcon,
			value: totalCommision,
			label: "Total commission",
		},
		{
			Icon: CoinsIcon,
			value: totalProcessingFee,
			label: "Total processing fee",
		},
	];

	return (
		<>
			<div className="mb-4">
				<p className="text-[26px] font-semibold text-[#1A1A40]">
					Financial Report
				</p>
			</div>
			<div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
				{cards.map(({ label, Icon, value }, index) => (
					<div
						key={index}
						className="h-[148px] rounded-md border border-[#E8E8E8] flex flex-col justify-between p-4"
					>
						<div className="flex justify-between">
							<p className="bg-gray-200 w-30 rounded-md p-2">
								<Icon className="" />
							</p>
							<span></span>
						</div>
						<p className="text-[26px] text-[#1A1A40] font-bold my-2">{value}</p>
						<p className="text-[14px] text-[#4B5563]">{label}</p>
					</div>
				))}
			</div>
		</>
	);
};

export default MainStats;
