import React, { useState, useRef, useEffect } from "react";
import { Download, ChevronDown } from "lucide-react";

const SortModal = ({ closeModal }) => {
	return (
		<div className="absolute right-0 mt-2 w-24 bg-white border rounded-lg shadow-lg z-50">
			<div className="flex text-xs flex-col p-2">
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Monthly
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Yearly
				</button>
			</div>
		</div>
	);
};

const ReportHeader = () => {
	const [isSortModalOpen, setIsSortModalOpen] = useState(false);
	const modalRef = useRef(null);
	const buttonRef = useRef(null);

	const openSortModal = () => setIsSortModalOpen(true);
	const closeSortModal = () => setIsSortModalOpen(false);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				isSortModalOpen &&
				!modalRef.current?.contains(event.target) &&
				!buttonRef.current?.contains(event.target)
			) {
				closeSortModal();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [isSortModalOpen]);

	return (
		<div className="relative">
			<div className="flex items-center justify-between mb-6">
				<div className="flex items-center gap-6">
					<div className="flex items-center gap-2">
						<div className="w-3 h-3 rounded-full bg-green-500"></div>
						<span className="text-sm text-gray-700">Revenue</span>
					</div>
					<div className="flex items-center gap-2">
						<div className="w-3 h-3 rounded-full bg-indigo-500"></div>
						<span className="text-sm text-gray-700">Commission</span>
					</div>
					<div className="flex items-center gap-2">
						<div className="w-3 h-3 rounded-full bg-yellow-500"></div>
						<span className="text-sm text-gray-700">Processing fee</span>
					</div>
				</div>{" "}
				<div className="flex items-center gap-3">
					<button className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<span>Export</span>
						<Download size={16} />
					</button>
					<div className="relative">
						<button
							ref={buttonRef}
							className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							onClick={openSortModal}
						>
							<span>Weekly</span>
							<ChevronDown size={16} />
						</button>

						{isSortModalOpen && (
							<div ref={modalRef}>
								<SortModal closeModal={closeSortModal} />
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export default ReportHeader;
