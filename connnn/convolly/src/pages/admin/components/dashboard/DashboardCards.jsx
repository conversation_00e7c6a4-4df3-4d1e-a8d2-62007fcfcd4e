import TotalClasses from "@/assets/svgs/studentDashboard/totalClasses";
import TotalNoOfTutorsIcon from "@/assets/svgs/studentDashboard/totalNoOfTutorsIcon";

const DashboardCards = ({ stats }) => {
	// Default values in case stats aren't provided
	const {
		totalRevenue = 77,
		totalTutors = 21,
		totalStudents = 100,
		totalLessons = 203,
		totalHoursTaught = 190,
	} = stats || {};

	const cards = [
		{
			Icon: TotalClasses,
			value: totalRevenue,
			label: "Total Revenue",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: totalStudents,
			label: "Total no of Students",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: totalTutors,
			label: "Total no of Tutors",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: totalLessons,
			label: "Total no of Lessons",
		},
		{
			Icon: TotalNoOfTutorsIcon,
			value: totalHoursTaught,
			label: "Total no of hours taught",
		},
	];

	return (
		<div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
			{cards.map(({ label, Icon, value }, index) => (
				<div
					key={index}
					className="h-[148px] rounded-md border border-[#E8E8E8] flex flex-col justify-between p-4"
				>
					<Icon />
					<p className="text-[26px] text-[#1A1A40] font-bold my-2">{value}</p>
					<p className="text-[14px] text-[#4B5563]">{label}</p>
				</div>
			))}
		</div>
	);
};

export default DashboardCards;
