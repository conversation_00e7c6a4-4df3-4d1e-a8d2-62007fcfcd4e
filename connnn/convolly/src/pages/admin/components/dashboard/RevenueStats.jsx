import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import RevenueHeader from "./RevenueHeader";

const RevenueStats = () => {
	const [isSortModalOpen, setIsSortModalOpen] = React.useState(false);

	const openSortModal = () => {
		setIsSortModalOpen(true);
	};

	const closeSortModal = () => {
		setIsSortModalOpen(false);
	};

	const data = [
		{ month: "Jan", value: 8 },
		{ month: "Feb", value: 22 },
		{ month: "Mar", value: 38 },
		{ month: "Apr", value: 12 },
		{ month: "May", value: 62 },
		{ month: "Jun", value: 75 },
		{ month: "Jul", value: 0 },
		{ month: "Aug", value: 0 },
		{ month: "Sep", value: 0 },
		{ month: "Oct", value: 23 },
		{ month: "Nov", value: 0 },
		{ month: "Dec", value: 9 },
	];

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 w-full">
      <RevenueHeader />

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              domain={[0, 100]}
              ticks={[0, 20, 40, 60, 80, 100]}
            />
            <Bar
              dataKey="value"
              fill="#10B981"
              radius={[2, 2, 0, 0]}
              barSize={20}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default RevenueStats;
