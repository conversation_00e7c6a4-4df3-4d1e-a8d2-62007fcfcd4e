import React from "react";
import tutor from "@/assets/images/tutor1.png";
import SendMessage from "./AdminSendMessage";

const AdminMessageBody = () => {
	const tutor = {
		name: "<PERSON>",
		date: "Mon 19, May",
		avatar: "",
		online: true,
	};

	const messages = [
		{
			id: 1,
			sender: "tutor",
			text: "Hello! How can I help you today?",
			time: "10:30 AM",
		},
		{
			id: 2,
			sender: "student",
			text: "Hi! I need help with my math assignment.",
			time: "10:32 AM",
		},
		{
			id: 3,
			sender: "tutor",
			text: "Sure, which part are you struggling with?",
			time: "10:33 AM",
		},
	];

	return (
		<div className="flex flex-col h-full mx-auto bg-white">
			{/* Header */}
			<div className="flex items-center p-4 border-b">
				<div className="relative">
					<img
						src={tutor.avatar}
						alt={tutor.name}
						className="w-12 h-12 rounded-full object-cover"
					/>
					{tutor.online && (
						<span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
					)}
				</div>
				<div className="ml-4">
					<h2 className="text-xl font-semibold text-gray-800">{tutor.name}</h2>
					<p className="text-sm text-gray-500">
						{tutor.online ? "Online" : "Last seen recently"}
					</p>
				</div>
			</div>

			{/* Date divider */}
			<div className="flex items-center justify-center my-4">
				<div className="px-3 py-1 bg-gray-100 rounded-full">
					<span className="text-sm text-gray-600">{tutor.date}</span>
				</div>
			</div>

			{/* Messages container */}
			<div className="flex-1 p-4 overflow-y-auto space-y-4">
				{messages.map((message) => (
					<div
						key={message.id}
						className={`flex ${
							message.sender === "tutor" ? "justify-start" : "justify-end"
						}`}
					>
						<div
							className={`max-w-xs md:max-w-md lg:max-w-lg px-4 py-2 rounded-lg ${
								message.sender === "tutor"
									? "bg-gray-100 text-gray-800 rounded-tl-none"
									: "bg-green-100 text-gray-800 rounded-tr-none"
							}`}
						>
							<p>{message.text}</p>
							<p
								className={`text-xs mt-1 text-right ${
									message.sender === "tutor"
										? "text-gray-500"
										: "text-green-500"
								}`}
							>
								{message.time}
							</p>
						</div>
					</div>
				))}
			</div>

			{/* Message input */}
			<div className="p-4 border rounded-md mb-4">
				<SendMessage />
			</div>
		</div>
	);
};

export default AdminMessageBody;

