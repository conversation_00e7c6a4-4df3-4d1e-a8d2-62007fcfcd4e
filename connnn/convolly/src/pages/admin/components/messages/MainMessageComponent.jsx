import React from "react";
import { SearchIcon } from "lucide-react";
import AdminMessageBody from "./AdminMessageBody";
import AdminMessageCard from "./AdminMessageCard";

const MainMessageComponent = () => {
	return (
		<div className="sm:flex">
			<div className="sm:w-[40%] w-full lg:max-w-[421px] border border-[#E8E8E8] rounded-md">
				<div className="flex rounded-md border-b p-2 mb-8 items-center">
					<SearchIcon />
					<input
						type="text"
						className="rounded-md w-full p-2"
						placeholder="search"
					/>
				</div>
				<div className="w-full border-t space-y-2 pt-2">
					<AdminMessageCard unreadCount={3} />
					<AdminMessageCard unreadCount={0} isSender={true} />
					<AdminMessageCard unreadCount={1} />
				</div>
			</div>

			<div className="flex-1 sm:w-[60%] w-full rounded-md border-[#E8E8E8] border border-l-0 px-2">
				<AdminMessageBody />
			</div>
		</div>
	);
};

export default MainMessageComponent;
