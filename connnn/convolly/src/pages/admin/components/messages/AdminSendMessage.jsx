import { Paperclip, Send } from "lucide-react";
import React, { useState } from "react";

const AdminSendMessage = () => {
	const [message, setMessage] = useState("");

	const handleSubmit = (e) => {
		e.preventDefault();
		if (message.trim()) {
			// Handle message sending logic here
			console.log("Message sent:", message);
			setMessage("");
		}
	};

	return (
		<form onSubmit={handleSubmit} className="gap-2">
			<div className="flex flex-col pb-6">
				<input
					type="text"
					value={message}
					onChange={(e) => setMessage(e.target.value)}
					placeholder="Type your message..."
					className="flex-1 py-3 rounded-md focus:outline-none"
				/>
			</div>

			<div className="flex justify-between">
				<button
					type="button"
					className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
				>
					<Paperclip size={20} />
				</button>

				<button
					type="submit"
					className="p-2 bg-primary flex rounded-md spacex-2 text-white"
					disabled={!message.trim()}
				>
					<Send size={20} />
					<p>Send</p>
				</button>
			</div>
		</form>
	);
};

export default AdminSendMessage;
