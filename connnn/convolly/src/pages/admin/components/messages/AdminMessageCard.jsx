import React from "react";
import tutor from "@/assets/images/tutor1.png";
import { Check, CheckCheck } from "lucide-react";

const AdminMessageCard = ({
	tutorName = "<PERSON> Obi",
	message = "Love the way the tutor teaches",
	date = "12/12/2019",
	unreadCount = 3,
	isSender = false,
}) => {
	return (
		<div
			className={`flex items-center p-3 w-full border-b hover:bg-gray-50 transition-colors cursor-pointer ${
				unreadCount > 0 ? "bg-blue-50" : ""
			}`}
		>
			{/* Tutor avatar and info */}
			<div className="flex items-center flex-1 min-w-0">
				<img
					src={tutor}
					alt={tutorName}
					className="w-12 h-12 rounded-full object-cover border-2 border-white"
				/>

				<div className="ml-3 min-w-0 flex-1">
					<div className="flex items-start justify-between">
						<div className="flex-1 min-w-0">
							<p className="text-md font-medium text-gray-900 truncate">
								{tutorName}
							</p>
							<p
								className={`text-sm truncate ${
									unreadCount > 0
										? "text-gray-900 font-medium"
										: "text-gray-600"
								}`}
							>
								{message}
							</p>
						</div>

						<div className="flex flex-col items-end ml-2">
							<p className="text-xs text-gray-500 whitespace-nowrap">{date}</p>
							{unreadCount > 0 && (
								<span className="mt-1 inline-flex items-center justify-center h-5 w-5 rounded-full bg-green-500 text-white text-xs font-medium">
									{unreadCount}
								</span>
							)}
						</div>
					</div>

					{isSender && (
						<div className="flex justify-end mt-1">
							{unreadCount > 0 ? (
								<Check className="h-4 w-4 text-gray-400" />
							) : (
								<CheckCheck className="h-4 w-4 text-blue-500" />
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default AdminMessageCard;

