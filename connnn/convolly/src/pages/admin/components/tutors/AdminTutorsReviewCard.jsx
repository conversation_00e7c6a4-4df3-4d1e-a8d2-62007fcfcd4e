import React, { useState } from "react";
import StarRating from "@/assets/images/studentDashboard/Star 5.png";
import likes from "@/assets/images/tutorDashboard/favourite.png";
import comments from "@/assets/images/tutorDashboard/comment-03.png";
import img from "@/assets/images/tutor1.png";
import { DeleteIcon, TrashIcon } from "lucide-react";

const student = {
	name: "<PERSON> Obi",
	avatar: img,
	courseTitle: "Grammar and Vocabulary focus",
	ratingDate: "12/23/2023",
};

const AdminTutorsReviewCard = () => {
	const [showOptions, setShowOptions] = useState(false);

	const handleShowOptions = () => {
		setShowOptions(true);
		console.log("button clicked");
	};

	const hideOption = () => {
		setShowOptions(false);
	};

	return (
		<>
			<div className="flex justify-between border-b">
				<div className="my-2 pb-2 mb-4 rounded-md p-4 justify-between flex flex-row pr-4">
					<div className="flex justify-between flex-row">
						<div className="flex pr-6 w-[220px] ">
							<img
								src={student.avatar}
								alt=""
								className="object-cover w-16 h-16 mr-6 rounded-md"
							/>
							<div className="mt-1">
								<p className="text-[#1A1A40] text-lg mt-2 font-semibold">
									{student.name}
								</p>
							</div>
						</div>

						{/* rating */}
						<div className="flex flex-col w-[1000px] ">
							<div className="flex gap-5 flex-row py-2 ">
								<p className="text-[#1A1A40] text-md flex font-semibold">
									<img src={StarRating} alt="" />
									<img src={StarRating} alt="" />
									<img src={StarRating} alt="" />
									<img src={StarRating} alt="" />
								</p>{" "}
								<p className="text-[16px] text-[#4B5563]">
									{student.ratingDate}
								</p>
							</div>
							<div>
								<p className="text-[#4B5563] text-sm w-1000">
									Start creating your public student profile. Your progress will
									be automatically saved as you complete each section. You can
									return at any time to finish your registration.
								</p>
							</div>
						</div>
					</div>
				</div>
				<div className="mt-8 flex flex-col justify-between">
					<button className="mt-6 px-6">
						<TrashIcon />
					</button>
				</div>
			</div>
		</>
	);
};

export default AdminTutorsReviewCard;
