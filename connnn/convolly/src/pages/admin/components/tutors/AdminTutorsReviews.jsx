import React from "react";
import { Star } from "lucide-react";
import AdminTutorsReviewCard from "./AdminTutorsReviewCard";

const AdminTutorsReviews = () => {
	return (
		<div className="">
			<div className="bg-white px-2 w-full">
				<div className="flex cols-3 justify-between">
					{/* Total Reviews */}
					<div className="">
						<h3 className="text-[22px] font-semibold">Total reviews</h3>
						<p className="text-[38px] font-semibold">20</p>
					</div>

					{/* Average Rating */}
					<div className="">
						<h3 className="text-[22px] font-semibold">Average rating</h3>
						<div className="flex items-center gap-1">
							<p className="text-[38px] font-semibold">4.5</p>
							<div className="flex">
								{[...Array(5)].map((_, i) => (
									<Star
										key={i}
										className={`w-5 h-5 ${
											i < 4
												? "fill-yellow-400 stroke-yellow-400"
												: "stroke-gray-300"
										}`}
									/>
								))}
							</div>
						</div>
					</div>

					{/* Rating Breakdown */}
					<div className="space-y-2 w-1/3">
						{[5, 4, 3, 2, 1].map((stars, index) => (
							<div key={stars} className="flex items-center gap-2">
								<div className="flex items-center w-8">
									<span className="text-sm text-gray-600">{stars}</span>
								</div>
								<div className="flex-1 bg-gray-100 rounded-full h-2">
									<div
										className="bg-[#54C68A] h-2 rounded-full"
										style={{
											width: `${[13, 2, 0, 2, 1][index] * 5}%`,
										}}
									></div>
								</div>
								<span className="text-xs font-semibold w-4">
									{[13, 2, 0, 2, 1][index]}
								</span>
							</div>
						))}
					</div>
				</div>
			</div>
			<br />
			<div>
				<AdminTutorsReviewCard />
				<AdminTutorsReviewCard />
				<AdminTutorsReviewCard />
			</div>
		</div>
	);
};

export default AdminTutorsReviews;
