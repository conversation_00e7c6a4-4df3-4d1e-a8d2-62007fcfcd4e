import React from "react";
import { useNavigate } from "react-router-dom";

const DashboardTransanctionHistory = () => {
  const navigate = useNavigate();

  const seeAllTransactions = () => {
    navigate("/admin/history/");
  };

  const transactions = [
    {
      id: "TX123456",
      tutorName: "<PERSON>",
      wiseEmail: "<EMAIL>",
      payoutMethod: "Wise Transfer",
      date: "15/06/2023",
      time: "10:30 AM",
      amount: "$250.00",
      status: "paid"
    },
    {
      id: "TX789012",
      tutorName: "<PERSON>",
      wiseEmail: "<EMAIL>",
      payoutMethod: "PayPal",
      date: "14/06/2023",
      time: "02:15 PM",
      amount: "$180.50",
      status: "pending"
    },
    {
      id: "TX345678",
      tutorName: "<PERSON>",
      wiseEmail: "<EMAIL>",
      payoutMethod: "Bank Transfer",
      date: "13/06/2023",
      time: "09:45 AM",
      amount: "$320.75",
      status: "failed"
    },
    {
      id: "TX901234",
      tutorName: "<PERSON>",
      wiseEmail: "<EMAIL>",
      payoutMethod: "Wise Transfer",
      date: "12/06/2023",
      time: "04:20 PM",
      amount: "$150.00",
      status: "refunded"
    },
    {
      id: "TX567890",
      tutorName: "David Lee",
      wiseEmail: "<EMAIL>",
      payoutMethod: "PayPal",
      date: "11/06/2023",
      time: "11:10 AM",
      amount: "$275.25",
      status: "paid"
    }
  ];

  // Function to get status styling based on status value
  const getStatusStyle = (status) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-[#ECFDF3] text-[#037847]";
      case "pending":
        return "bg-[#FFDB431A] text-[#DFB400]";
      case "failed":
        return "bg-[#FB37481A] text-[#D00416]";
      case "refunded":
        return "bg-[#4D00FF1A] text-[#3C00C6]";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div>
      <div className="flex justify-between mb-4">
        <h1 className="text-2xl font-semibold">Transaction History</h1>
        <div className="text-[#A4A4A4] text-[14px] p-4">
          <div className="items-end gap-4 flex">
            <span
              onClick={seeAllTransactions}
              className="text-primary cursor-pointer"
            >
              See all
            </span>
          </div>
        </div>
      </div>
	  
      <div>
        <table className="w-full">
          <thead className="border-b border-[#E0E0E0]">
            <tr className="bg-[#F5F5F5]">
              <th className="p-4 text-left">Transaction ID</th>
              <th className="p-4 text-left">Tutor's Name</th>
              <th className="p-4 text-left">Wise email address</th>
              <th className="p-4 text-left">Payout method</th>
              <th className="p-4 text-left">Date</th>
              <th className="p-4 text-left">Time</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map((transaction, index) => (
              <tr key={index} className="border-b shadow border-[#E0E0E0]">
                <td className="p-4">{transaction.id}</td>
                <td className="p-4">{transaction.tutorName}</td>
                <td className="p-4">{transaction.wiseEmail}</td>
                <td className="p-4">{transaction.payoutMethod}</td>
                <td className="p-4">{transaction.date}</td>
                <td className="p-4">{transaction.time}</td>
                <td className="p-4">{transaction.amount}</td>
                <td className="p-4">
                  <p
                    className={`text-center p-2 rounded-2xl ${getStatusStyle(
                      transaction.status
                    )}`}
                  >
                    {transaction.status}
                  </p>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DashboardTransanctionHistory;
