import { Button } from "@/components/button/button";
import { X } from "lucide-react";
import React from "react";
import { useGetPendingTutorDetailsQuery } from "@/redux/slices/admin/adminPendingTutorApiSlice";
import useGet from "@/hooks/useGet";

const ViewApplicationModal = ({ onClose, tutorId, setActiveTab }) => {
	const {
		data: application,
		isLoading: gettingApplication,
		error,
	} = useGet(() => tutorId && useGetPendingTutorDetailsQuery(tutorId));

	if (gettingApplication) {
		return <div>Loading...</div>;
	}

	if (error) {
		return <div>Error loading application details</div>;
	}

	const handleNext = () => {
		setActiveTab("Profile");
	};

	return (
		<div className="bg-white p-2 sm:p-6 rounded-md flex flex-col h-full">
			<div className="flex justify-between">
				<h1 className="text-2xl font-semibold text-[#1A1A40]">
					Application Details
				</h1>
				<X size={24} onClick={onClose} className="cursor-pointer" />
			</div>

			{/* Scrollable content container */}
			<div className="mt-4 overflow-y-auto flex-1">
				<div className="flex p-2 sm:p-4 border w-auto rounded-md justify-between">
					<div className="flex gap-4">
						<div className="flex">
							<img
								src={application?.image}
								className="w-12 h-12 object-cover rounded-full"
								alt="Tutor"
							/>
							<div className="pt-3 mx-4">
								<p className="text-[#1A1A40] space-x-3 text-lg font-semibold">
									{application?.firstname} {application?.lastname}
								</p>
							</div>
						</div>
						<div>
							{application?.teachingSubjects?.map((subject, index) => (
								<div key={index} className="flex">
									<span>Subject:</span>
									<span className="ml-2 text-[#4B5563]"> {subject?.title}</span>
								</div>
							))}

							<div className="flex">
								<span>Industry of Specialization:</span>
								<span className="text-[#4B5563] ml-2">
									{application?.industry}
								</span>
							</div>
						</div>
					</div>
					<div className="px-4">
						<div className="flex">
							<span>Phone:</span>
							<span className="text-[#4B5563] ml-2">{application?.phone}</span>
						</div>
						<div className="flex">
							<span>Email:</span>
							<span className="text-[#4B5563] ml-2"> {application?.email}</span>
						</div>
					</div>
				</div>

				{/* Personal Information */}
				<div className="border p-4 rounded-md mt-4">
					<h1 className="text-2xl text-[#1A1A40] font-semibold py-2">
						Personal Information
					</h1>
					<div className="gap-4">
						<div className="py-2">
							<p className="text-[#1A1A40] text-md font-medium">Gender</p>
							<p className="text-[#4B5563]">{application?.gender}</p>
						</div>
						<div className="py-2">
							<p className="text-[#1A1A40] text-md font-medium">Nationality</p>
							<p className="text-[#4B5563]">{application?.countryOfBirth}</p>
						</div>
						<div className="py-2">
							<p className="text-[#1A1A40] text-md font-medium">Languages</p>
							{application?.languages?.map((lang, index) => (
								<p className="text-[#4B5563]" key={index}>
									{lang?.name}
								</p>
							))}
						</div>
					</div>
				</div>

				{/* Proficiency section */}
				<div className="border p-4 rounded-md mt-4">
					<h1 className="text-2xl text-[#1A1A40] font-semibold py-2">
						Proficiency
					</h1>

					{application?.teachingSubjects?.map((subject, index) => (
						<div key={subject.id || index} className="gap-4 mb-6">
							{/* Subject Title and Experience */}
							<div className="py-2">
								<p className="text-[#1A1A40] text-md font-medium">
									{subject.title} experience level
								</p>
								<p className="text-[#4B5563]">{subject.experienceLevel}</p>
							</div>

							{/* Teaching Experience */}
							<div className="py-2">
								<p className="text-[#1A1A40] text-md font-medium">
									Years of experience
								</p>
								<p className="text-[#4B5563]">
									{application?.teachingExperience ?? "N/A"}
								</p>
							</div>

							{/* Qualities */}
							<div className="py-2">
								<p className="text-[#1A1A40] mb-2 text-md font-medium">
									Qualities
								</p>
								<div className="flex flex-wrap gap-2">
									{subject.qualities?.map((quality, index) => (
										<span
											key={index}
											className="bg-gray-100 rounded-xl px-2 py-1 text-[#4B5563]"
										>
											{quality}
										</span>
									))}
								</div>
							</div>

							{/* Specialities */}
							<div className="py-2">
								<p className="text-[#1A1A40] mb-2 text-md font-medium">
									Specialities
								</p>
								<div className="flex flex-wrap gap-2">
									{subject.specialities?.map((spec, index) => (
										<span
											key={index}
											className="bg-gray-100 rounded-xl px-2 py-1 text-[#4B5563]"
										>
											{spec}
										</span>
									))}
								</div>
							</div>
						</div>
					))}
				</div>

				<div className="border p-4 rounded-md mt-4">
					<h1 className="text-2xl text-[#1A1A40] font-semibold py-2">
						Certification and Education
					</h1>
					<div className="gap-4">
						<div className="py-2">
							{application?.academics?.map((academic, index) => (
								<div key={index} className="flex justify-between pb-2">
									<div>
										<p className="text-[#1A1A40] text-lg font-medium">
											{academic?.university}
										</p>
										<div className="flex">
											<p className="text-[#1A1A40] text-md font-medium">
												{academic?.degreeType}
											</p>
											,
											<p className="text-[#1A1A40] ml-2 text-md font-medium">
												{academic?.degree}
											</p>
										</div>
									</div>

									<div className="flex gap-2 text-gray-500 text-sm">
										<span>{academic?.startDate}</span>-
										<span>{academic?.endDate}</span>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>

				{/* timezone  */}
				<div className="rounded-md mt-4">
					<h2 className="text-[text-[#1A1A40] pb-2">Choose Your timezone</h2>
					<form action="">
						<input
							type="text"
							className="py-2 border w-full rounded-md"
							name="timezone"
							id="timezone"
						/>
					</form>
				</div>

				{/* video intro */}
				<div className="mt-4">
					<h1 className="text-2xl text-[#1A1A40] font-semibold py-2">
						Video introduction
					</h1>
					<div className="h-[357px] rounded-md border p-4 gap-4">
						{application?.introVideo?.url && (
							<video
								controls
								className="w-full h-full rounded-md object-cover"
								src={application.introVideo}
							>
								Your browser does not support the video tag.
							</video>
						)}
					</div>
				</div>
			</div>

			<div className="my-4 pb-4 flex justify-between">
				<span></span>
				<Button onClick={handleNext} className="h-50 px-4 py-2 flex mr-4">
					Next
				</Button>
			</div>
		</div>
	);
};

export default ViewApplicationModal;
