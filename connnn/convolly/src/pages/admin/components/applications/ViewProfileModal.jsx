import { Button } from "@/components/button/button";
import { ChevronLeft, X } from "lucide-react";
import React from "react";
import {
	useApprovePendingTutorMutation,
	useGetPendingTutorDetailsQuery,
	useRejectPendingTutorMutation,
} from "@/redux/slices/admin/adminPendingTutorApiSlice";
import useGet from "@/hooks/useGet";
import { toast } from "react-toastify";

const ViewProfileModal = ({ onClose, tutorId, setActiveTab }) => {
	const {
		data: application,
		isLoading,
		error,
	} = useGet(() => useGetPendingTutorDetailsQuery(tutorId));

	const [approveTutor, { isLoading: isApproving }] =
		useApprovePendingTutorMutation();
	const [rejectTutor, { isLoading: isRejecting }] =
		useRejectPendingTutorMutation();

	const handleApprove = async () => {
		try {
			await approveTutor(tutorId).unwrap();
			toast.success("<PERSON><PERSON> approved successfully");
			onClose();
		} catch (error) {
			toast.error(error.data?.message || "Failed to approve tutor");
		}
	};

	const handleReject = async () => {
		try {
			// Switch to Reject tab which contains the RejectFeedbackModalForm
			setActiveTab("Reject");
		} catch (error) {
			toast.error("Failed to initiate rejection process");
		}
	};

	if (isLoading) return <div className="p-4">Loading profile details...</div>;
	if (error)
		return (
			<div className="p-4 text-red-500">Error loading profile details</div>
		);
	if (!application) return <div className="p-4">No profile data found</div>;

	return (
		<div className="bg-white p-2 sm:p-6 rounded-md w-auto flex flex-col h-full">
			<div className="flex justify-between items-center">
				<div className="flex text-sm sm:text-xl items-center">
					<ChevronLeft
						size={24}
						onClick={() => setActiveTab("Application")}
						className="cursor-pointer mr-2"
					/>
					<h1 className="font-semibold text-[#1A1A40]">Profile description</h1>
				</div>
				<X size={24} onClick={onClose} className="cursor-pointer" />
			</div>

			<div className="mt-6 overflow-y-auto flex-1">
				<div className="text-[#4B5563]">
					<h1 className="text-xl sm:text-2xl font-semibold text-[#1A1A40]">
						1. Introduction about yourself
					</h1>
					<div className="mt-2 text-sm sm:text-md">
						<p>
							Show potential students who you are! Share your teaching
							experience and passion for education and briefly mention your
							interests and hobbies.
						</p>
					</div>
					<div className="border p-4 rounded-md mt-4">
						<p>{application.aboutMe}</p>
					</div>
				</div>

				<div className="text-[#4B5563] mt-4">
					<h1 className="text-xl sm:text-2xl font-semibold text-[#1A1A40]">
						2. Teaching experience
					</h1>
					<div className="mt-2 text-sm sm:text-md">
						<p>
							Provide a detailed description of your relevant teaching
							experience. Include certifications, teaching methodology,
							education, and subject expertise.
						</p>
					</div>
					<div className="border p-4 rounded-md mt-4">
						<p>{application?.teachingExperience}</p>
					</div>
				</div>

				<div className="text-[#4B5563] mt-4">
					<h1 className="text-xl sm:text-2xl font-semibold text-[#1A1A40]">
						3. Motivate potential student
					</h1>
					<div className="mt-2 text-sm sm:text-md">
						<p>
							Encourage students to book their first lesson. Highlight the
							benefits of learning with you!
						</p>
					</div>
					<div className="border p-4 rounded-md mt-4">
						<p>{application?.motivatePotentialStudent}</p>
					</div>
				</div>

				<div className="text-[#4B5563] mt-4">
					<h1 className="text-xl sm:text-2xl font-semibold text-[#1A1A40]">
						4. Write a catchy headline
					</h1>
					<div className="mt-2 text-sm sm:text-md">
						<p>
							Your headline is the first thing students see about you. Make it
							attention-grabbing, mention your specific teaching language and
							encourage students to read your full description.
						</p>
					</div>
					<div className="border p-4 rounded-md mt-4">
						<p>{application?.headline}</p>
					</div>
				</div>

				<div className="text-[#4B5563] mt-4 mb-4">
					<div className="mt-2">
						<p>Price</p>
					</div>
					<div className="flex justify-between border rounded-md p-2">
						<p>{application?.basePrice}</p>
						<p>$</p>
					</div>
				</div>
			</div>

			<div className="my-4 pb-4 flex justify-end">
				<div className="flex gap-2">
					<Button
						className="w-50 flex"
						disabled={isApproving}
						onClick={handleApprove}
					>
						{isApproving ? "Approving..." : "Approve"}
					</Button>
					<Button
						onClick={handleReject}
						className="w-50 flex bg-[#FB3748] hover:bg-[#e03040]"
						disabled={isRejecting}
					>
						{isRejecting ? "Rejecting..." : "Reject"}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default ViewProfileModal;
