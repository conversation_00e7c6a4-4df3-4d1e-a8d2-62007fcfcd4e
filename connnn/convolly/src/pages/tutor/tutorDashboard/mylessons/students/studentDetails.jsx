// import React, { useState } from "react";
// import { format, addDays, startOfWeek, addWeeks } from "date-fns";
// import rightArrow from "@/assets/svgs/rightArrow.svg";
// import leftArrow from "@/assets/svgs/leftArrow.svg";

// const TutorDetails = () => {
//   const [weekOffset, setWeekOffset] = useState(0);

//   const startDate = addWeeks(
//     startOfWeek(new Date(), { weekStartsOn: 0 }),
//     weekOffset
//   );

//   const endDate = addDays(startDate, 6);

//   const formatRange = `${format(startDate, "MMM d")} – ${format(
//     endDate,
//     "d, yyyy"
//   )}`;

//   return (
//     <div>
//       <div>{/*  show the card on the left here*/}</div>

//       <div className="flex items-center gap-2 mb-7">
//         <p className="border border-[#E8E8E8] p-[6px] rounded-lg px-5">Today</p>
//         <button
//           onClick={() => setWeekOffset((w) => w - 1)}
//           className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
//         >
//           <img src={leftArrow} alt="prev" />
//         </button>
//         <button
//           onClick={() => setWeekOffset((w) => w + 1)}
//           className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
//         >
//           <img src={rightArrow} alt="next" />
//         </button>
//         <p className="sm:text-xl text-lg font-bold">{formatRange}</p>
//       </div>
//     </div>
//   );
// };

// export default TutorDetails;

import React, { useState } from "react";
import {
  format,
  addDays,
  startOfWeek,
  addWeeks,
  setHours,
  setMinutes
} from "date-fns";
import rightArrow from "@/assets/svgs/rightArrow.svg";
import leftArrow from "@/assets/svgs/leftArrow.svg";

// Time slots from 7:00 AM to 2:30 PM (30 min intervals)
const timeSlots = Array.from({ length: 16 }, (_, i) => {
  const hour = 7 + Math.floor(i / 2);
  const minutes = i % 2 === 0 ? 0 : 30;
  return { hour, minutes };
});

// Sample available slots (day: 0 = Sunday, 1 = Monday, ..., 6 = Saturday)
const availableTimes = [
  { day: 1, hour: 7, minutes: 0 },
  { day: 1, hour: 8, minutes: 0 },
  { day: 2, hour: 9, minutes: 30 },
  { day: 4, hour: 14, minutes: 0 },
  { day: 6, hour: 10, minutes: 0 }
];

const TutorDetails = () => {
  const [weekOffset, setWeekOffset] = useState(0);
  const [selectedSlot, setSelectedSlot] = useState(null);

  const startDate = addWeeks(
    startOfWeek(new Date(), { weekStartsOn: 0 }),
    weekOffset
  );
  const endDate = addDays(startDate, 6);
  const formatRange = `${format(startDate, "MMM d")} – ${format(
    endDate,
    "d, yyyy"
  )}`;

  const handleClickSlot = (dayIndex, time) => {
    const date = setHours(
      setMinutes(addDays(startDate, dayIndex), time.minutes),
      time.hour
    );
    setSelectedSlot(date);
  };

  const isAvailable = (dayIndex, time) => {
    return availableTimes.some(
      (slot) =>
        slot.day === dayIndex &&
        slot.hour === time.hour &&
        slot.minutes === time.minutes
    );
  };

  return (
    <div className="flex gap-5">
      <div className="w-full max-w-[300px]">
        {/*  show the card on the left here*/}
      </div>

      <div className="w-full">
        {/* Header Navigation */}
        <div className="flex items-center gap-2 mb-7">
          <p
            className="border border-[#E8E8E8] p-[6px] rounded-lg px-5 cursor-pointer"
            onClick={() => setWeekOffset(0)}
          >
            Today
          </p>
          <button
            onClick={() => setWeekOffset((w) => w - 1)}
            className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
          >
            <img src={leftArrow} alt="prev" />
          </button>
          <button
            onClick={() => setWeekOffset((w) => w + 1)}
            className="bg-white border border-[#E8E8E8] p-[6px] rounded-lg"
          >
            <img src={rightArrow} alt="next" />
          </button>
          <p className="sm:text-xl text-lg font-bold">{formatRange}</p>
        </div>

        {/* Calendar */}
        <div className="rounded-lg border overflow-x-auto">
          {/* Weekdays Header */}
          <div className="grid grid-cols-7">
            {[...Array(7)].map((_, i) => {
              const date = addDays(startDate, i);
              const isToday =
                format(date, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");
              return (
                <div
                  key={i}
                  className={`text-center py-3 font-bold ${
                    isToday ? "bg-green-500 text-white rounded-t" : ""
                  }`}
                >
                  {format(date, "EEE")} <br /> {format(date, "d")}
                </div>
              );
            })}
          </div>

          {/* Time Slots */}
          {timeSlots.map((time, i) => (
            <div className="grid grid-cols-7" key={i}>
              {[...Array(7)].map((_, dayIdx) => {
                const available = isAvailable(dayIdx, time);
                const timeLabel = `${time.hour
                  .toString()
                  .padStart(2, "0")}:${time.minutes
                  .toString()
                  .padStart(2, "0")}`;

                return (
                  <div
                    key={dayIdx}
                    className={`text-center py-4 text-lg font-semibold ${
                      available
                        ? "text-black cursor-pointer hover:bg-blue-50"
                        : "text-gray-400 cursor-not-allowed"
                    }`}
                    onClick={() => available && handleClickSlot(dayIdx, time)}
                  >
                    {timeLabel}
                  </div>
                );
              })}
            </div>
          ))}
        </div>

        {/* Popup Modal */}
        {selectedSlot && (
          <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-40 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl w-[300px] text-center">
              <p className="text-lg font-semibold mb-3">Slot Selected</p>
              <p className="text-sm mb-4">
                {format(selectedSlot, "eeee, MMMM d yyyy • h:mm a")}
              </p>
              <button
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                onClick={() => alert("Continue to booking flow")}
              >
                Continue
              </button>
              <button
                className="mt-3 block text-gray-500 underline text-sm mx-auto"
                onClick={() => setSelectedSlot(null)}
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TutorDetails;
