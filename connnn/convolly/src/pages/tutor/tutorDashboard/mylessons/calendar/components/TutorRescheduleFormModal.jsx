import { Button } from "@/components/button/button";
import { X } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
	useGetTutorCalendarQuery,
	useReScheduleClassMutation,
} from "@/redux/slices/student/scheduleApiSlice";
import { toast } from "react-toastify";
import useGet from "@/hooks/useGet";
import userVector from "@/assets/svgs/userVector.svg";

const TutorRescheduleFormModal = ({ onClose, lesson }) => {
	const tutorId = lesson?.bookingData?.tutor?.id;
	const student = lesson?.bookingData?.student;
	console.log(lesson);

	const [rescheduleClass, { isLoading }] = useReScheduleClassMutation();

	const { data: tutorCalendar, isLoading: gettingTutorCalendar } = useGet(
		useGetTutorCalendarQuery,
		tutorId,
		!!tutorId
	);

	// Extract and format available slots from tutor calendar
	const availableSlots = React.useMemo(() => {
		if (!tutorCalendar?.availableSlots) return [];

		return tutorCalendar.availableSlots.map((slot) => {
			const startDate = new Date(slot.startDateTime);
			return {
				date: startDate.toISOString().split("T")[0],
				time: `${startDate.getHours().toString().padStart(2, "0")}:${startDate
					.getMinutes()
					.toString()
					.padStart(2, "0")}`,
				originalData: slot,
			};
		});
	}, [tutorCalendar]);

	// Group available slots by date for easier lookup
	const slotsByDate = React.useMemo(() => {
		const grouped = {};
		availableSlots.forEach((slot) => {
			if (!grouped[slot.date]) {
				grouped[slot.date] = [];
			}
			grouped[slot.date].push(slot.time);
		});
		return grouped;
	}, [availableSlots]);

	// Filter dates that have available slots for the date picker
	const filterAvailableDates = (date) => {
		const dateStr = date.toISOString().split("T")[0];
		return slotsByDate[dateStr]?.length > 0;
	};

	// Get time options for the selected date
	const getTimeOptions = (selectedDate) => {
		if (!selectedDate) return [];
		const dateStr = selectedDate.toISOString().split("T")[0];
		return slotsByDate[dateStr] || [];
	};

	const {
		register,
		handleSubmit,
		watch,
		setValue,
		formState: { errors },
	} = useForm({
		defaultValues: {
			lessonType: "50 mins standard lesson",
			reason: "",
			date: null,
			time: "",
		},
	});

	const onSubmit = async (data) => {
		const selectedDate = data.date;
		const [hour, minute] = data.time.split(":").map(Number);

		const newStartDateTime = new Date(selectedDate);
		newStartDateTime.setHours(hour, minute, 0, 0);

		const newEndDateTime = new Date(
			newStartDateTime.getTime() + lesson.duration * 60000
		);

		const payload = {
			id: lesson.id,
			newStartDateTime: newStartDateTime.toISOString(),
			newEndDateTime: newEndDateTime.toISOString(),
			reason: data.reason === "Other" ? data.otherReason : data.reason,
		};

		try {
			await rescheduleClass(payload).unwrap();
			toast.success("Lesson successfully rescheduled!");
			onClose();
		} catch (error) {
			console.error("Rescheduling failed:", error);
			const message =
				error?.data?.detail ||
				error?.message ||
				"Failed to reschedule. Please try again.";
			toast.error(message);
		}
	};

	const lessonTypes = ["50 mins standard lesson", "25 mins quick lesson"];
	const reasons = [
		"Personal reasons",
		"Schedule conflict",
		"Technical issues",
		"Other",
	];

	// Get currently selected date and corresponding time options
	const selectedDate = watch("date");
	const timeOptions = getTimeOptions(selectedDate);

	console.log("availableSlots", availableSlots);
	console.log("slotsByDate", slotsByDate);

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-xl p-6 w-[700px] max-h-[90vh] overflow-y-auto">
				<div className="flex justify-between items-center pb-4">
					<h2 className="text-2xl font-bold text-[#1A1A40]">
						Reschedule lesson
					</h2>
					<button
						className="text-gray-600 hover:text-gray-800"
						onClick={onClose}
					>
						<X size={30} />
					</button>
				</div>

				<form onSubmit={handleSubmit(onSubmit)}>
					<p className="py-2 text-gray-600 font-medium">Current lesson</p>

					<div className="mb-4 rounded-md border p-4">
						<div className="flex border-b pb-4 items-center space-x-4">
							<img
								src={student?.image || userVector}
								alt="student"
								className="object-cover w-16 h-16 rounded-md"
							/>
							<div className="p-2 space-y-2">
								<p className="text-[#1A1A40] font-semibold">Every Friday</p>
								<p className="text-[#4B5563]">09:00 - 9:50</p>
							</div>
						</div>
						<div className="text-[14px] font-sans text-[#4B5563] flex justify-between px-2 mt-4">
							<p>{student?.name}</p>
							<p></p>
							<p>{lesson?.title}</p>
						</div>
					</div>

					<p className="py-2 text-gray-600 font-medium">New lesson details</p>

					<div className="mb-4 shadow-lg rounded-md border p-4 space-y-4">
						<div className="w-full">
							<select
								{...register("lessonType", {
									required: "Lesson type is required",
								})}
								className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
							>
								{lessonTypes.map((type) => (
									<option key={type} value={type}>
										{type}
									</option>
								))}
							</select>
							{errors.lessonType && (
								<p className="text-red-500 text-sm mt-1">
									{errors.lessonType.message}
								</p>
							)}
						</div>

						<div className="flex space-x-4">
							<div className="w-1/2">
								<DatePicker
									selected={selectedDate}
									onChange={(date) => {
										setValue("date", date);
										setValue("time", ""); // Reset time when date changes
									}}
									minDate={new Date()}
									filterDate={filterAvailableDates}
									className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
									placeholderText="Select available date"
									required
									popperPlacement="auto"
									showPopperArrow={true}
								/>
								{!gettingTutorCalendar && availableSlots.length === 0 && (
									<p className="text-red-500 text-sm mt-1">
										No available slots found for this tutor
									</p>
								)}
							</div>
							<div className="w-1/2">
								<select
									{...register("time", { required: "Time is required" })}
									className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
									disabled={!selectedDate || timeOptions.length === 0}
								>
									<option value="">Select a time</option>
									{timeOptions.map((time) => (
										<option key={time} value={time}>
											{time}
										</option>
									))}
									{selectedDate && timeOptions.length === 0 && (
										<option disabled>No available slots for this date</option>
									)}
								</select>
								{errors.time && (
									<p className="text-red-500 text-sm mt-1">
										{errors.time.message}
									</p>
								)}
							</div>
						</div>

						<div className="w-full">
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Reason for rescheduling
							</label>
							<select
								{...register("reason", { required: "Reason is required" })}
								className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
							>
								<option value="">Select a reason</option>
								{reasons.map((reason) => (
									<option key={reason} value={reason}>
										{reason}
									</option>
								))}
							</select>
							{errors.reason && (
								<p className="text-red-500 text-sm mt-1">
									{errors.reason.message}
								</p>
							)}
						</div>

						{watch("reason") === "Other" && (
							<div className="w-full">
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Please specify
								</label>
								<textarea
									{...register("otherReason", {
										required:
											watch("reason") === "Other"
												? "Please specify your reason"
												: false,
									})}
									className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
									rows={3}
									placeholder="Enter your reason here..."
								/>
								{errors.otherReason && (
									<p className="text-red-500 text-sm mt-1">
										{errors.otherReason.message}
									</p>
								)}
							</div>
						)}
					</div>

					<div className="flex mt-6 space-x-4">
						<Button
							type="submit"
							className="w-full h-[50px] bg-primary font-semibold text-white rounded-md"
							disabled={isLoading || !selectedDate || timeOptions.length === 0}
						>
							{isLoading ? "Processing..." : "Continue"}
						</Button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default TutorRescheduleFormModal;
