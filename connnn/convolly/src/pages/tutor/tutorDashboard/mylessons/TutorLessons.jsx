import React, { useState } from "react";
import LessonsContainer from "./components/LessonsContainer";
import { useNavigate, useLocation } from "react-router-dom";
import TotalClasses from "@/assets/svgs/studentDashboard/totalClasses";
import TotalNoOfStudentsIcon from "@/assets/svgs/studentDashboard/totalNoOfTutorsIcon";
import CalendarIcon from "@/assets/svgs/studentDashboard/calendar";
import Calender from "./calendar/Calender";
import Students from "./students/Students";
import MainScheduleModal from "../components/scheduleModals/MainScheduleModal";

const TutorLessons = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const searchParams = new URLSearchParams(location.search);
	const activeTab = searchParams.get("tab") || "lessons";
	const [showScheduleModal, setShowScheduleModal] = useState(false);

	const openModal = () => {
		setShowScheduleModal(true);
	};

	const closeModal = () => {
		setShowScheduleModal(false);
	};

	const navItems = [
		{
			id: "lessons",
			Icon: TotalClasses,
			name: "Lessons",
		},
		{
			id: "calendar",
			Icon: CalendarIcon,
			name: "Calendar",
		},
		{
			id: "students",
			Icon: TotalNoOfStudentsIcon,
			name: "students",
		},
	];

	const handleTabChange = (tabId) => {
		navigate(`?tab=${tabId}`, { replace: true });
	};

	return (
		<div className="">
			<div className="flex flex-col sm:flex-row sm:justify-between border-b border-[#EBEDF0] pb-5 mb-8 bg-white">
				<div className="flex gap-3">
					{navItems.map((item) => (
						<button
							key={item.id}
							onClick={() => handleTabChange(item.id)}
							className={`px-4 flex gap-2 items-center w-full rounded-md py-2 transition-colors ${
								activeTab === item.id
									? "bg-primary text-white"
									: "bg-gray-100 hover:bg-gray-200"
							}`}
						>
							<item.Icon
								stroke={activeTab === item.id ? "#ffffff" : "#1A1A40"}
							/>
							<span>{item.name}</span>
						</button>
					))}
				</div>
				{/* <div>
					<button
						className="text-primary border border-primary w-full rounded-md px-2 sm:px-4 mt-2 sm:mt-0 py-2 "
						onClick={openModal}
					>
						Schedule lesson
					</button>
				</div> */}
			</div>
			{/* {showScheduleModal && (
				<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
					<MainScheduleModal onClose={closeModal} />
				</div>
			)} */}

			{/* Render content based on active tab */}
			{activeTab === "lessons" && <LessonsContainer />}
			{activeTab === "calendar" && <Calender />}
			{activeTab === "students" && <Students />}
		</div>
	);
};

export default TutorLessons;
