import React from "react";
import { useForm } from "react-hook-form";
import { X } from "lucide-react";
import { Button } from "@/components/button/button";
import { useRequestWithdrawalMutation } from "@/redux/slices/tutor/tutorDashboardApiSlice";
import usePost from "@/hooks/usePost";
import Loader from "@/components/loader/loader";

const WithdrawModal = ({ onClose, onContinue, availableBalance }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm();

  const { handlePost: requestWithdrawal, isLoading: requesting } = usePost(
    useRequestWithdrawalMutation
  );

  const amount = watch("amount");
  const transactionFee = 1.0;
  const amountReceived = amount ? parseFloat(amount) - transactionFee : 0;

  const onSubmit = async (data) => {
    const response = await requestWithdrawal({
      email: data.email,
      amount: data.amount
    });

    if (response) {
      onContinue(data);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md lg:max-w-[700px] relative">
      {requesting && <Loader />}
      <div className="flex items-center justify-between">
        <h2 className="text-lg sm:text-[24px] font-semibold text-[#1A1A40]">
          Withdrawal Details
        </h2>
        <button
          onClick={onClose}
          aria-label="Close modal"
          className="text-[#1A1A40]"
        >
          <X size={24} />
        </button>
      </div>

      <div className="mt-8">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label
              htmlFor="email"
              className="text-sm sm:text-[18px] text-[#1A1A40] block"
            >
              Enter email linked to your Wise account
            </label>
            <input
              type="email"
              id="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address"
                }
              })}
              placeholder="Enter email"
              className={`ring-1 ${
                errors.email ? "ring-red-500" : "ring-primary"
              } border-[#E8E8E8] rounded-md w-full p-2 mt-2 text-sm sm:text-base`}
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">
                {errors.email.message}
              </p>
            )}
          </div>
          <div className="mb-4">
            <label
              htmlFor="amount"
              className="text-sm sm:text-[18px] text-[#1A1A40] block"
            >
              Enter Amount to Withdraw (Available: $
              {parseFloat(availableBalance || 0).toFixed(2)})
            </label>
            <input
              type="number"
              id="amount"
              {...register("amount", {
                required: "Amount is required",
                min: {
                  value: transactionFee + 0.01,
                  message: `Amount must be greater than transaction fee ($${transactionFee.toFixed(
                    2
                  )})`
                },
                valueAsNumber: true
              })}
              className={`ring-1 ${
                errors.amount ? "ring-red-500" : "ring-primary"
              } border-[#E8E8E8] rounded-md w-full p-2 mt-2 text-sm sm:text-base`}
              placeholder="Enter amount"
              step="0.01"
            />
            {errors.amount && (
              <p className="text-red-500 text-sm mt-1">
                {errors.amount.message}
              </p>
            )}
          </div>

          {/* Transaction Details */}
          <div className="mt-4 bg-gray-50 p-4 rounded-md">
            <div className="py-2 text-[#1A1A40] text-sm sm:text-md flex justify-between">
              <p>Transaction Fee</p>
              <p>${transactionFee.toFixed(2)}</p>
            </div>
            <div className="py-2 text-[#1A1A40] text-sm sm:text-md flex justify-between">
              <p>You Will Receive</p>
              <p>${amountReceived.toFixed(2)}</p>
            </div>
            <div className="py-2 text-[#1A1A40] text-sm sm:text-md flex justify-between">
              <p>Available Balance</p>
              <p>${parseFloat(availableBalance || 0).toFixed(2)}</p>
            </div>
          </div>
          {errors.form && (
            <p className="text-red-500 text-sm mt-2">{errors.form.message}</p>
          )}
          <div className="mt-6 w-full flex justify-between gap-4">
            <Button
              type="button"
              onClick={onClose}
              className="h-[50px] w-full text-black px-4 bg-white border border-gray-300 hover:bg-gray-50 text-sm sm:text-base"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="h-[50px] w-full px-4 text-white bg-primary hover:bg-primary-dark text-sm sm:text-base"
              disabled={requesting}
            >
              {requesting ? "Processing..." : "Withdraw Funds"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WithdrawModal;
