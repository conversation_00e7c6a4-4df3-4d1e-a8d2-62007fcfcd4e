// SidebarLinks.jsx
import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import DashboardIcon from "@/assets/svgs/sidebar/dashboardIcon";
import LessonsIcon from "@/assets/svgs/sidebar/lessonsIcon";
import MessageIcon from "@/assets/svgs/sidebar/messageIcon";
import ReviewsIcon from "@/assets/svgs/sidebar/reviewsIcon";
import SettingsIcon from "@/assets/svgs/sidebar/settingsIcon";
import LogoutIcon from "@/assets/svgs/sidebar/logoutIcon";

const SidebarLinks = ({ setShowLogoutModal, toggleSidebar }) => {
	const navigate = useNavigate();
	const location = useLocation();

	const navItems = [
		{
			path: "/tutor/dashboard",
			name: "Dashboard",
			Icon: DashboardIcon,
		},
		{
			path: "/tutor/messages",
			name: "Messages",
			Icon: MessageIcon,
		},
		{
			path: "/tutor/my-lessons",
			name: "My Lessons",
			Icon: LessonsIcon,
		},
		{
			path: "/tutor/reviews",
			name: "Reviews",
			Icon: ReviewsIcon,
		},
		{
			path: "/tutor/settings",
			name: "Setting<PERSON>",
			Icon: SettingsIcon,
		},
		{
			path: "/logout",
			name: "Logout",
			Icon: LogoutIcon,
			isLogout: true,
		},
	];

	const isActive = (path) => {
		if (path === "/tutor/dashboard") {
			return location.pathname === path;
		}
		if (path === "/tutor/my-lessons") {
			return (
				location.pathname.startsWith("/tutor/my-lessons") ||
				location.pathname.startsWith("/tutor/students") ||
				location.pathname.startsWith("/tutor/calendar")
			);
		}
		return location.pathname.startsWith(path);
	};

	const handleLogoutClick = (e, item) => {
		e.preventDefault();
		if (item.isLogout) {
			setShowLogoutModal(true);
		} else {
			navigate(item.path);
			// Close sidebar after navigation (especially important for mobile)
			if (window.innerWidth < 1024) {
				// or your lg breakpoint
				// You'll need to pass the toggleSidebar function to Links component
				toggleSidebar && toggleSidebar();
			}
		}
	};

	return (
		<div className="flex flex-col space-y-2">
			{navItems.map((item, index) => (
				<div
					key={index}
					className={`p-2 flex flex-row items-center hover:bg-primary text-[#1A1A40] cursor-pointer hover:text-white transition rounded-md ${
						isActive(item.path) && !item.isLogout ? "bg-primary text-white" : ""
					}`}
					onClick={(e) => handleLogoutClick(e, item)}
				>
					<item.Icon
						stroke={
							isActive(item.path) && !item.isLogout
								? "#ffffff"
								: item.isLogout
								? "#D00416"
								: "#1A1A40"
						}
					/>

					<button
						className={`md:text-lg p-2 rounded-md font-bold ${
							isActive(item.path) && !item.isLogout
								? "text-white"
								: item.isLogout
								? "text-[#D00416]"
								: ""
						}`}
					>
						{item.name}
					</button>
				</div>
			))}
		</div>
	);
};

export default SidebarLinks;
