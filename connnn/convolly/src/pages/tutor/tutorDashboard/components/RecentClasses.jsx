import React, { useState } from "react";
import ClassSchedule from "./ClassSchedule";
import { capitalizeWords, formatDisplayTime } from "@/utils/utils";
const RecentClasses = ({ recentClasses, isLoading }) => {
	return (
		<div className="flex flex-col gap-4 xl:flex-row">
			{/* Left Table (expandable) */}
			<div className="w-full xl:w-[55%]">
				<div className="border bg-white p-4 rounded-lg shadow-sm">
					<table className="w-full text-sm sm:text-base">
						<thead className="text-left text-[#1A1A40] border-b text-sm sm:text-[18px] font-medium border-gray-200">
							<tr className="pb-2">
								<th className="pr-10">Student</th>
								<th>Industry</th>
								<th>Date</th>
								<th>Time</th>
							</tr>
						</thead>
						<tbody className="text-left">
							{isLoading ? (
								<tr>
									<td colSpan="4" className="py-4 text-center text-[#4B5563]">
										Loading...
									</td>
								</tr>
							) : recentClasses.length === 0 ? (
								<tr>
									<td colSpan="4" className="py-4 text-center text-[#4B5563]">
										No recent classes found.
									</td>
								</tr>
							) : (
								recentClasses.map((cls) => (
									<tr
										key={cls.id}
										className="border-b text-xs sm:text-[14px] text-[#4B5563] border-gray-100"
									>
										<td className="py-4">
											{capitalizeWords(cls.student.name)}
										</td>
										<td>{cls.formattedDate}</td>
										<td className="mx-2">
											{formatDisplayTime(cls.scheduledTime)}
										</td>
									</tr>
								))
							)}
						</tbody>
					</table>
				</div>
			</div>
			<div className="w-full xl:w-[45%]">
				{/* Right Sidebar */}
				<ClassSchedule />
			</div>
		</div>
	);
};

export default RecentClasses;
