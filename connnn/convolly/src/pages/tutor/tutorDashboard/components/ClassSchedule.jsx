import React, { useState } from "react";
import ClassScheduleStudentProfile from "./ClassScheduleStudentProfile";
import { useGetTutorClassesQuery } from "@/redux/slices/student/classesApiSlice";
import useGet from "@/hooks/useGet";
import { formatDisplayTime } from "@/utils/utils";

const ClassSchedule = () => {
	const [page, setPage] = React.useState(3);

	// Get current date and calculate the start of the current week (Sunday)
	const getStartOfWeek = (date) => {
		const day = date.getDay(); // 0 (Sunday) to 6 (Saturday)
		const diff = date.getDate() - day;
		return new Date(date.setDate(diff));
	};

	const today = new Date();
	const startOfWeek = getStartOfWeek(new Date(today));
	const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

	// Generate dates for the current week
	const dates = Array.from({ length: 7 }, (_, i) => {
		const date = new Date(startOfWeek);
		date.setDate(startOfWeek.getDate() + i);
		return {
			day: days[date.getDay()],
			date: date.getDate(),
			fullDate: date.toISOString().split("T")[0],
			month: date.getMonth(),
			year: date.getFullYear(),
		};
	});

	const [activeDate, setActiveDate] = useState(today.getDate());

	// Fetch bookings data
	const { data: bookings, isLoading } = useGet(useGetTutorClassesQuery, "");
	console.log(bookings);

	// Filter upcoming classes for the selected date
	// Filter upcoming classes for the selected date
	const upcomingClasses =
		bookings?.bookings?.filter((booking) => {
			const bookingDate = new Date(booking.scheduledTime);
			const selectedDate = dates.find((d) => d.date === activeDate);

			if (!selectedDate) return false;

			const now = new Date();

			// Match the day first
			const sameDay =
				bookingDate.getDate() === selectedDate.date &&
				bookingDate.getMonth() === selectedDate.month &&
				bookingDate.getFullYear() === selectedDate.year;

			// Only upcoming if it's in the future
			const inFuture = bookingDate > now;

			return sameDay && inFuture;
		}) || [];

	console.log(upcomingClasses);

	// Calculate end time by adding duration minutes
	const getEndTime = (startTime, duration) => {
		const start = new Date(startTime);
		// Add UTC offset to get correct local time display
		const localStart = new Date(
			start.getTime() + start.getTimezoneOffset() * 60000
		);
		const end = new Date(localStart.getTime() + duration * 60000);
		return end.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		});
	};

	return (
		<div className="w-full bg-white rounded-lg border shadow-sm p-4">
			<h2 className="text-[#1A1A40] text-md sm:text-[22px] font-semibold mb-3">
				Upcoming classes
			</h2>
			<div className="flex gap-2 justify-between text-center mt-4 mb-4">
				{dates.map((d) => (
					<div
						key={`${d.date}-${d.month}-${d.year}`}
						className="flex flex-col items-center cursor-pointer"
						onClick={() => setActiveDate(d.date)}
					>
						<span className="text-xs text-gray-500">{d.day}</span>
						<div
							className={`w-8 h-8 rounded-full flex items-center justify-center mt-1 text-sm ${
								d.date === activeDate
									? "bg-green-100 text-green-700 font-semibold"
									: "text-gray-500"
							}`}
						>
							{d.date}
						</div>
					</div>
				))}
			</div>
			<hr />
			{/* Time slots */}
			<div className="mt-6">
				<div className="gap-5 space-y-2">
					{upcomingClasses && upcomingClasses.length > 0 ? (
						upcomingClasses.map((classItem) => (
							<ClassScheduleStudentProfile
								key={classItem.id}
								upcomingClasses={classItem}
							/>
						))
					) : (
						<p className="text-center text-md text-gray-600">
							No classes scheduled for this day
						</p>
					)}
				</div>
			</div>
		</div>
	);
};

export default ClassSchedule;
