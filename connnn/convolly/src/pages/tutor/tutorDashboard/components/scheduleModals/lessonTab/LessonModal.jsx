import { CustomSelect } from "@/components/select/select";
import { ChevronDown, Home } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import StudentistsModal from "./StudentistsModal";
import { Button } from "@/components/button/button";
import studentIcon from "@/assets/images/tutorDashboard/user-group-03.png";
import calenderIcon from "@/assets/images/tutorDashboard/calender.png";
import weeklyIcon from "@/assets/images/tutorDashboard/repeat.png";

const LessonModal = () => {
	const [showStudentsListModal, setsShowStudentsListModal] = useState(false);
	const [lessonFrequency, setLessonFrequency] = useState("single"); // 'single' or 'weekly'
	const { control, register } = useForm();

	const showModal = () => {
		setsShowStudentsListModal(true);
	};

	const closeModal = () => {
		setsShowStudentsListModal(false);
	};

	// Sample data arrays
	const durationOptions = [
		{ value: "30", label: "30 minutes" },
		{ value: "45", label: "45 minutes" },
		{ value: "60", label: "60 minutes" },
		{ value: "90", label: "90 minutes" },
	];

	return (
		<div>
			<form action="#">
				{/* students add  */}
				<div>
					<label className="mb-6 text-[#1A1A40] text-md" htmlFor="Student">
						Student
					</label>
					<div
						onClick={showModal}
						className="border mt-4 mb-4 h-[50px] px-4 py-3 text-gray-600 text-md w-full rounded-md flex justify-between"
					>
						<div className="flex">
							<img
								src={studentIcon}
								alt="studentIcon"
								className="w-7 h-6 pr-2 text-black fill-black"
							/>
							<p>Add Student</p>
						</div>
						<ChevronDown className=" text-[#A4A4A4]" size={18} />
					</div>

					{/* Lesson Type Section - Now Radio Button Options */}
					<div className="grid-1">
						<label htmlFor="" className="mt-4 text-[#1A1A40] text-md">
							Lesson Type
						</label>

						<div className="grid grid-cols-2 gap-4 mt-4">
							{/* Single Lesson Option */}
							<div
								className={`flex items-center justify-between border rounded-md px-4 py-2 cursor-pointer ${
									lessonFrequency === "single"
										? "border-primary bg-primary bg-opacity-10"
										: "border-[#E8E8E8]"
								}`}
								onClick={() => setLessonFrequency("single")}
							>
								<div className="flex items-center">
									<img
										src={calenderIcon}
										alt="studentIcon"
										className="w-5 h-5 mr-2 text-black fill-black"
									/>
									<span>Single Lesson</span>
								</div>
								<div
									className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
										lessonFrequency === "single"
											? "border-primary bg-primary"
											: "border-gray-300 bg-white"
									}`}
								>
									{lessonFrequency === "single" && (
										<div className="w-2 h-2 rounded-full bg-white"></div>
									)}
								</div>
							</div>

							{/* Weekly Lesson Option */}
							<div
								className={`flex items-center justify-between border rounded-md px-4 py-2 cursor-pointer ${
									lessonFrequency === "weekly"
										? "border-primary bg-primary bg-opacity-10"
										: "border-[#E8E8E8]"
								}`}
								onClick={() => setLessonFrequency("weekly")}
							>
								<div className="flex items-center">
									<img
										src={weeklyIcon}
										alt="studentIcon"
										className="w-5 h-5 mr-2 text-black fill-black"
									/>
									<span>Weekly Lesson</span>
								</div>
								<div
									className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
										lessonFrequency === "weekly"
											? "border-primary bg-primary"
											: "border-gray-300 bg-white"
									}`}
								>
									{lessonFrequency === "weekly" && (
										<div className="w-2 h-2 rounded-full bg-white"></div>
									)}
								</div>
							</div>
						</div>
					</div>

					{/* Date and Time Section */}
					<div className="mt-4">
						<label htmlFor="" className="text-[#1A1A40] text-md mt-4">
							Date and Time
						</label>
						<input
							type="datetime-local"
							className="w-full text-gray-600 h-[50px] py-2 px-3 border rounded-md mt-2"
						/>

						<div className="grid grid-cols-2 gap-4 mt-4">
							<CustomSelect
								control={control}
								name="duration"
								placeholder="Select duration"
								options={durationOptions}
								className="w-full h-[50px]"
							/>
							<input
								type="text"
								placeholder="Location"
								className="w-full text-gray-600 h-[50px] py-2 px-3 border rounded-md"
							/>
						</div>
					</div>
				</div>
				<Button className="h-[50px] w-full mt-6">Schedule lesson</Button>
			</form>
			{showStudentsListModal && (
				<div
					className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30 animate-fadeIn"
					onClick={closeModal}
				>
					<div className="animate-slideUp" onClick={(e) => e.stopPropagation()}>
						<StudentistsModal onClose={closeModal} />
					</div>
				</div>
			)}
		</div>
	);
};

export default LessonModal;
