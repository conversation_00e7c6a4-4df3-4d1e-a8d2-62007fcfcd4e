import { CustomSelect } from "@/components/select/select";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/button/button";
import TextInput from "@/components/inputs/textInput";

const ExtraSlotsModal = () => {
	const { control, register } = useForm();

	// Sample data arrays
	const lessonTypes = [
		{ value: "private", label: "Private" },
		{ value: "group", label: "Group" },
		{ value: "workshop", label: "Workshop" },
	];

	const frequencyOptions = [
		{ value: "weekly", label: "Weekly" },
		{ value: "biweekly", label: "Bi-weekly" },
		{ value: "monthly", label: "Monthly" },
		{ value: "once", label: "One-time" },
	];

	const durationOptions = [
		{ value: "30", label: "30 minutes" },
		{ value: "45", label: "45 minutes" },
		{ value: "60", label: "60 minutes" },
		{ value: "90", label: "90 minutes" },
	];

	return (
		<div>
			<form action="#">
				{/* students add  */}
				<div className="text-[#1A1A40]">
					{/* Lesson Type Section */}
					<h1 className="text-lg text-bold">Add extra slots</h1>
					<p className="text-sm text-[#4B5563] mt-1">
						Choose time slots up to 24 hours long.
					</p>
					<div className="mt-4">
						<label htmlFor="" className="">
							Start
						</label>

						<div className="grid grid-cols-2 gap-4 mt-2">
							<CustomSelect
								control={control}
								name="lessonType"
								placeholder="Select type"
								options={lessonTypes}
								className="w-full h-[50px]"
							/>
							<CustomSelect
								control={control}
								name="frequency"
								placeholder="Select frequency"
								options={frequencyOptions}
								className="w-full h-[50px]"
							/>
						</div>
					</div>

					{/* Date and Time Section */}
					<div className="mt-4">
						<label htmlFor="">End</label>

						<div className="grid grid-cols-2 gap-4 mt-4">
							<CustomSelect
								control={control}
								name="duration"
								placeholder="Select duration"
								options={durationOptions}
								className="w-full h-[50px]"
							/>
							<CustomSelect
								control={control}
								name="duration"
								placeholder="Select duration"
								options={durationOptions}
								className="w-full h-[50px]"
							/>
						</div>
					</div>
				</div>
				<Button className="h-[50px] w-full mt-6">Add </Button>
			</form>
		</div>
	);
};

export default ExtraSlotsModal;
