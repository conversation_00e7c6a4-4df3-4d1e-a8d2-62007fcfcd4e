import React from "react";
import { useForm } from "react-hook-form";

const TutorNotificatonSettings = () => {
	const { register, handleSubmit, watch } = useForm({
		defaultValues: {
			lessonUpdates: true,
			lessonReminders: false,
			messageUpdates: true,
		},
	});

	const onSubmit = (data) => {
		console.log("Notification Settings:", data);
		alert("Notification settings saved successfully!");
	};

	return (
		<form onSubmit={handleSubmit(onSubmit)} className="flex flex-col w-[516px]">
			<h2 className="text-xl text-[#1A1A40] font-semibold mb-6">
				Email Notifications
			</h2>

			<div className="text-[#4B5563] text-[18px] space-y-4">
				<div className="flex items-center">
					<input
						type="checkbox"
						id="lessonUpdates"
						{...register("lessonUpdates")}
						className="w-4 h-4 text-primary rounded focus:ring-primary"
					/>
					<label
						htmlFor="lessonUpdates"
						className="ml-2 text-[18px] font-medium"
					>
						Get updates about your lessons
					</label>
				</div>

				<div className="flex items-center">
					<input
						type="checkbox"
						id="lessonReminders"
						{...register("lessonReminders")}
						className="w-4 h-4 text-primary rounded focus:ring-primary"
					/>
					<label
						htmlFor="lessonReminders"
						className="ml-2 text-[18px] font-medium"
					>
						Reminder emails - 5 minutes before lesson
					</label>
				</div>

				<div className="flex items-center">
					<input
						type="checkbox"
						id="messageUpdates"
						{...register("messageUpdates")}
						className="w-4 h-4 text-primary rounded focus:ring-primary"
					/>
					<label
						htmlFor="messageUpdates"
						className="ml-2 text-[18px] font-medium"
					>
						Get updates on messages
					</label>
				</div>
			</div>
		</form>
	);
};

export default TutorNotificatonSettings;
