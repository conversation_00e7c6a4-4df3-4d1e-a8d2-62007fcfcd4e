import React, { useRef, useState, useEffect } from "react";
import greenTick from "@/assets/svgs/greenTick.svg";
import { Button } from "@/components/button/button";
import { convertToBase64 } from "@/utils/utils";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";

const TutorVideoSettings = ({ setActiveTab, instructorDetails }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

	const [youtubeURL, setYoutubeURL] = useState("");
	const [videoBase64, setVideoBase64] = useState(null);
	const [isRecording, setIsRecording] = useState(false);
	const [recordedVideoURL, setRecordedVideoURL] = useState(null);
	const videoRef = useRef(null);
	const mediaRecorderRef = useRef(null);
	const streamRef = useRef(null);
	const recordedChunks = useRef([]);

	const startRecording = async () => {
		try {
			const stream = await navigator.mediaDevices.getUserMedia({
				video: true,
				audio: true,
			});

			streamRef.current = stream;

			// SAFELY attach stream to video element
			if (videoRef.current) {
				videoRef.current.srcObject = stream;
			}

			const mediaRecorder = new MediaRecorder(stream);
			mediaRecorderRef.current = mediaRecorder;
			recordedChunks.current = [];

			mediaRecorder.ondataavailable = (event) => {
				if (event.data.size > 0) {
					recordedChunks.current.push(event.data);
				}
			};

			// mediaRecorder.onstop = () => {
			//   const blob = new Blob(recordedChunks.current, { type: "video/webm" });
			//   const url = URL.createObjectURL(blob);
			//   setRecordedVideoURL(url);

			//   // Stop the camera stream
			//   if (streamRef.current) {
			//     streamRef.current.getTracks().forEach((track) => track.stop());
			//   }
			// };

			mediaRecorder.onstop = async () => {
				const blob = new Blob(recordedChunks.current, { type: "video/webm" });
				const url = URL.createObjectURL(blob);
				setRecordedVideoURL(url);

				// Convert Blob to Base64
				const base64 = await convertToBase64(blob);
				console.log("Base64 Recorded Video:", base64);

				setVideoBase64(base64);

				// Stop the camera stream
				if (streamRef.current) {
					streamRef.current.getTracks().forEach((track) => track.stop());
				}
			};

			mediaRecorder.start();
			setIsRecording(true);
		} catch (error) {
			console.error("Error accessing camera/microphone:", error);
			alert(
				"Camera or microphone access failed. Please allow access in your browser."
			);
		}
	};

	const stopRecording = () => {
		mediaRecorderRef.current.stop();
		setIsRecording(false);
	};

	const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	const updateVideo = async () => {
		const res = await handleUpdateTutor({
			introVideo: videoBase64 || youtubeURL,
			userId: tutorId,
			role: "tutor",
		});

		if (res) {
			setActiveTab("Availability");
		}
	};

	console.log(mediaRecorderRef);
	console.log(recordedVideoURL);
	// console.log(convertToBase64(mediaRecorderRef));
	// console.log(convertToBase64(recordedVideoURL));

	useEffect(() => {
		if (instructorDetails) {
			setVideoBase64(instructorDetails?.introVideo);
		}
	}, [instructorDetails]);

	return (
		<>
			{updating && <Loader />}

			<div className="md:max-w-[528px] w-auto">
				{youtubeURL && !recordedVideoURL && (
					<div className="mb-5 border rounded-lg overflow-hidden bg-[#F5F5F5]">
						<iframe
							width="100%"
							height="315"
							src={youtubeURL.replace("watch?v=", "embed/")}
							title="YouTube video player"
							frameBorder="0"
							allowFullScreen
							className="w-full h-[315px]"
						/>
					</div>
				)}

				{!youtubeURL && (
					<div className="mb-5 border rounded-lg overflow-hidden">
						{recordedVideoURL ? (
							<video
								controls
								className="w-full h-[315px] object-cover bg-[#F5F5F5]"
							>
								<source src={recordedVideoURL} type="video/webm" />
								Your browser does not support the video tag.
							</video>
						) : (
							<video
								ref={videoRef}
								autoPlay
								muted
								className="w-full h-[315px] bg-[#F5F5F5]"
							/>
						)}
					</div>
				)}

				<div className="flex gap-3 mb-7">
					{!isRecording ? (
						<Button
							onClick={startRecording}
							className="w-full bg-white border border-primary h-[40px] text-primary hover:text-white"
						>
							Re-record
						</Button>
					) : (
						<Button
							onClick={stopRecording}
							className="w-full h-[40px] bg-red-600 hover:bg-red-600 text-white"
						>
							Stop Recording
						</Button>
					)}
				</div>

				<h3 className="mb-3 sm:text-xl text-lg font-bold">
					Or paste a link to your video
				</h3>

				<input
					type="text"
					placeholder="https://www.youtube.com/watch?v=zWh3CShX_do"
					value={youtubeURL}
					onChange={(e) => setYoutubeURL(e.target.value)}
					className="border border-[#D2D2D2] rounded-lg px-4 py-2 mb-10 w-full"
				/>

				<p className="sm:text-xl text-lg mb-3 font-bold">Video requirements</p>
				{[
					"You should be facing forward",
					"You should be centered and upright",
					"You should be the only person in the photo",
					"Use a color photo with high resolution and no filters",
					"Frame your head and shoulders",
				].map((text, idx) => (
					<div key={idx} className="flex gap-3 items-center mb-3">
						<img src={greenTick} alt="green tick" />
						<p className="text-[#4B5563] max-sm:text-sm">{text}</p>
					</div>
				))}

				{/* Navigation Buttons */}
				<div className="sm:flex gap-5 mt-10">
					<Button className="w-full h-[50px]" onClick={updateVideo}>
						Save changes
					</Button>
				</div>
			</div>
		</>
	);
};

export default TutorVideoSettings;
