import React, { useState } from "react";
import Navbar from "@/components/navbar/navbar";
import tutor from "@/assets/images/tutorProfileImage.png";
import students from "@/assets/svgs/students.svg";
import fullStar from "@/assets/svgs/fullStar.svg";
import usa from "@/assets/svgs/usa.svg";
import lessons from "@/assets/svgs/lessons.svg";
import favorite from "@/assets/svgs/favorite.svg";
import greenCheck from "@/assets/svgs/greencheck.svg";
import { Button } from "@/components/button/button";
import Schedule from "./components/schedule/schedule";
import Footer from "@/components/footer/footer";

const TutorProfile = () => {
	const certificationTypeOptions = ["Education", "Certificate"];
	const [activeCertOption, setActiveCertOption] = useState("Education");

	return (
		<div>
			<Navbar />

			<section className="w-full py-[50px] sm:px-8 px-3">
				<div className="lg:flex gap-5 mb-5 justify-between">
					<div className="w-full max-w-[856px]">
						<div className="lg:flex hidden gap-5 items-stretch">
							<img
								src={tutor}
								alt="tutor image"
								className="w-full max-w-[290px] max-h-[200px] object-cover rounded-xl"
							/>

							<div className="flex w-full gap-5">
								<div className="w-full">
									<div className="flex gap-2 items-center mb-2">
										<h3 className="text-[26px] font-bold">Steve Gilbert D.</h3>
										<img src={greenCheck} alt="verified icon" />
										<img src={usa} alt="country icon" />
									</div>

									<p className="text-[#4B5563] mb-2">
										Nigeria . 16:0(GMT+02:00)
									</p>

									<p className="text-[#4B5563] mb-2">
										Teaches: English Language
									</p>

									<div className="flex items-center gap-1 mb-2">
										<img src={students} alt="students icon" />
										<span className="text-[#4B5563] mr-5">300 students</span>

										<img src={lessons} alt="lessons icon" />
										<span className="text-[#4B5563] mr-5">400 lessons</span>
									</div>

									<div className="flex items-center gap-1">
										<span className="text-[#4B5563]">English: </span>
										<span className="text-black mr-5">Native</span>

										<span className="text-[#4B5563]">French: </span>
										<span className="text-black">Intermediate</span>
									</div>
								</div>

								<img src={favorite} alt="star icon" className="w-6 h-6" />
							</div>
						</div>

						{/* mobile view */}
						<div className="lg:hidden">
							<div className="flex gap-3 mb-3">
								<img
									src={tutor}
									alt="tutor image"
									className="w-full max-w-[100px] max-h-[120px] object-cover rounded-lg"
								/>

								<div className="w-full">
									<div className="flex items-center mb-2">
										<h3 className="text-lg font-bold">Steve Gilbert D.</h3>
										<img
											src={greenCheck}
											alt="verified icon"
											className="w-5 h-5"
										/>
										<img src={usa} alt="country icon" className="w-5 h-5" />
										<img
											src={favorite}
											alt="favorite icon"
											className="w-5 h-5 ml-auto"
										/>
									</div>

									<div className="flex gap-3">
										<div className="flex flex-col">
											<div className="flex gap-2 items-center">
												<img
													src={fullStar}
													alt="star icon"
													className="w-5 h-5"
												/>
												<h2 className="font-bold text-lg">4.5</h2>
											</div>
											<p className="text-[#4B5563] shrink-0">60 reviews</p>
										</div>

										<div className="flex flex-col">
											<h2 className="font-bold text-lg">US$50</h2>
											<p className="text-[#4B5563] shrink-0">50-min lesson</p>
										</div>
									</div>
								</div>
							</div>

							<p className="text-[#4B5563] mb-2">Nigeria . 16:0(GMT+02:00)</p>

							<p className="text-[#4B5563] mb-2">Teaches: English Language</p>

							<div className="flex items-center gap-1 mb-2">
								<img src={students} alt="students icon" />
								<span className="text-[#4B5563] mr-5">300 students</span>

								<img src={lessons} alt="lessons icon" />
								<span className="text-[#4B5563] mr-5">400 lessons</span>
							</div>

							<div className="flex items-center gap-1">
								<span className="text-[#4B5563]">English: </span>
								<span className="text-black mr-5">Native</span>

								<span className="text-[#4B5563]">French: </span>
								<span className="text-black">Intermediate</span>
							</div>
						</div>

						<h3 className="sm:text-xl text-secondary font-bold mb-3 mt-5">
							About
						</h3>

						<p className="text-[#4B5563] sm:text-lg mb-3">
							Lorem ipsum dolor sit amet consectetur. Ullamcorper condimentum
							platea non pellentesque massa commodo faucibus dictum. Scelerisque
							felis in egestas eget porta posuere suscipit vitae volutpat.
							Convallis blandit imperdiet sit elementum. Eget viverra turpis sit
							integer lectus eu lectus diam. Feugiat viverra donec amet pharetra
							a velit ut lorem.
						</p>

						<a href="#" className="underline block mb-5 text-primary font-bold">
							Read more
						</a>
					</div>

					<div className="lg:max-w-[475px] w-full bg-white md:p-5 max-md:my-10 rounded-lg shadow-light-around p-3 grow flex flex-col gap-4">
						<img
							src={tutor}
							alt="tutor image"
							className="w-full h-full max-h-[250px] object-cover rounded-lg mb-2"
						/>

						<div className="flex sm:gap-7 gap-4 mb-3">
							<div className="flex flex-col">
								<div className="flex gap-2 items-center">
									<img src={fullStar} alt="star icon" className="w-6 h-6" />
									<h2 className="font-bold text-2xl">4.5</h2>
								</div>
								<p className="text-[#4B5563] shrink-0">60 reviews</p>
							</div>

							<div className="flex flex-col">
								<h2 className="font-bold text-2xl">US$50</h2>
								<p className="text-[#4B5563] shrink-0">50-min lesson</p>
							</div>
						</div>

						<Button className="h-[50px]">Book free lesson</Button>
						<Button className="h-[50px] text-primary bg-white border-2 border-primary hover:border-secondary hover:text-white">
							Send message
						</Button>
					</div>
				</div>

				<div className="max-w-[856px]">
					<h3 className="font-bold text-xl mb-3 text-secondary">
						Teaching Expertise
					</h3>

					<p className="text-secondary mb-2">Experience level</p>

					<div className="bg-[#EBEDF0] p-5 rounded-lg mb-5 grid grid-cols-2">
						<p className="text-secondary mb-2">Upper Beginner</p>
						<p className="text-secondary mb-2">Upper Intermediate</p>
						<p className="text-secondary mb-2">Intermediate</p>
						<p className="text-secondary mb-2">Advanced</p>
					</div>

					<p className="text-secondary mb-2">Specialities</p>

					<div className="bg-[#EBEDF0] p-5 rounded-lg mb-5 grid grid-cols-2">
						<p className="text-secondary mb-2">Accent reduction</p>
						<p className="text-secondary mb-2">Writing</p>
						<p className="text-secondary mb-2">Phonetics</p>
						<p className="text-secondary mb-2">Conversion fluency</p>
					</div>

					<p className="text-secondary mb-2">Qualities</p>

					<div className="bg-[#EBEDF0] p-5 rounded-lg grid grid-cols-2">
						<p className="text-secondary mb-2">Cultural insights</p>
						<p className="text-secondary mb-2">Excellent materials</p>
						<p className="text-secondary mb-2">Motivational guru</p>
						<p className="text-secondary mb-2">Tech savy</p>
					</div>
				</div>

				<div className="max-w-[856px] mt-10">
					<h3 className="font-bold text-xl text-secondary mb-5">Schedule</h3>

					<Schedule />
				</div>

				<div className="max-w-[856px] mt-10">
					<div className="border-b border-[#EBEDF0] flex mb-5">
						{certificationTypeOptions?.map((cert) => (
							<h3
								key={cert}
								className={`font-bold sm:text-2xl text-xl border-b transition-all cursor-pointer sm:min-w-[120px] sm:mr-7 mr-3 ${
									cert == activeCertOption
										? "text-secondary border-primary"
										: "text-[#A4A4A4]"
								}`}
								onClick={() => setActiveCertOption(cert)}
							>
								{cert}
							</h3>
						))}
					</div>

					<div className="flex sm:flex mt-7 sm:gap-7 gap-3">
						<p className="text-[#4B5563] sm:text-lg sm:min-w-[120px]">
							2003-2005
						</p>

						<div>
							<div className="flex gap-2 mb-2">
								<h2 className="sm:text-xl text-lg font-bold text-secondary">
									CELTA
								</h2>
								<img src={greenCheck} alt="greenCheck" />
								<span className="text-[#4D00FF] sm:text-lg">
									Certificate verified
								</span>
							</div>

							<p className="sm:text-lg text-[#4B5563] mb-1">
								University of Sheffield - Sheffield, Yorkshire
							</p>

							<p className="sm:text-lg text-[#4B5563] mb-1">
								Certificate of English Language Teaching for Adults
							</p>
						</div>
					</div>

					<div className="flex sm:flex mt-5 sm:gap-7 gap-3">
						<p className="text-[#4B5563] sm:text-lg sm:min-w-[120px]">
							2003-2005
						</p>

						<div>
							<div className="flex gap-2 mb-2">
								<h2 className="sm:text-xl text-lg font-bold text-secondary">
									CELTA
								</h2>
								<img src={greenCheck} alt="greenCheck" />
								<span className="text-[#4D00FF] sm:text-lg">
									Certificate verified
								</span>
							</div>

							<p className="sm:text-lg text-[#4B5563] mb-1">
								University of Sheffield - Sheffield, Yorkshire
							</p>

							<p className="sm:text-lg text-[#4B5563] mb-1">
								Certificate of English Language Teaching for Adults
							</p>
						</div>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
};

export default TutorProfile;
