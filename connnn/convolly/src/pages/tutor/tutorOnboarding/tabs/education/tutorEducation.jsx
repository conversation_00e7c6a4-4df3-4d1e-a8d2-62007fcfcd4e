import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/button/button";
import { CustomSelect } from "@/components/select/select";
import InputField from "@/components/inputs";
import { useForm, useFieldArray } from "react-hook-form";
import uploadIcon from "@/assets/svgs/uploadIcon.svg";
import CheckboxInput from "@/components/inputs/checkboxInput";
import { Trash2 } from "lucide-react";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { convertToBase64 } from "@/utils/utils";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";

const TutorEducation = ({ setActiveTab, instructorDetails }) => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      academics: [
        {
          university: "",
          startDate: "",
          degree: "",
          degreeType: "",
          endDate: "",
          fileUpload: ""
        }
      ]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "academics"
  });

  const [uploadedFiles, setUploadedFiles] = useState({});

  const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  // Initialize form and uploadedFiles with instructorDetails.academics
  useEffect(() => {
    if (instructorDetails?.academics?.length > 0) {
      const formattedAcademics = instructorDetails.academics.map((edu) => ({
        university: edu.university || "",
        startDate: edu.startDate ? edu.startDate.slice(0, 4) : "",
        degree: edu.degree || "",
        degreeType: edu.degreeType || "",
        endDate: edu.endDate ? edu.endDate.slice(0, 4) : "",
        fileUpload: edu.file
          ? {
              name:
                edu.file.name ||
                edu.file.url?.split("/").pop() ||
                "certificate",
              base64: edu.file.base64 || edu.file.url || ""
            }
          : ""
      }));

      // Set form values
      setValue("academics", formattedAcademics);

      // Initialize uploadedFiles for UI display
      const initialUploadedFiles = instructorDetails.academics.reduce(
        (acc, edu, index) => {
          if (edu.file) {
            acc[index] = {
              name:
                edu.file.name ||
                edu.file.url?.split("/").pop() ||
                "certificate",
              base64: edu.file.base64 || edu.file.url || ""
            };
          }
          return acc;
        },
        {}
      );
      setUploadedFiles(initialUploadedFiles);
    }
  }, [instructorDetails, setValue]);

  const handleAddCertificate = () => {
    append({
      university: "",
      startDate: "",
      degree: "",
      degreeType: "",
      endDate: "",
      fileUpload: ""
    });
  };

  const handleFileChange = async (e, index) => {
    const file = e.target.files[0];
    if (file) {
      const base64 = await convertToBase64(file);
      setUploadedFiles((prev) => ({
        ...prev,
        [index]: { name: file.name, base64 }
      }));
      setValue(`academics[${index}].fileUpload`, {
        name: file.name,
        base64
      });
    }
  };


  // Generate years from 1970 to current year (2025)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: currentYear - 1960 + 1 }, (_, i) => {
    const year = currentYear - i;
    return { value: year.toString(), label: year.toString() };
  });

  const updateEducation = async (data) => {
    const formattedEducation = data.academics.map((edu) => ({
      university: edu.university || "",
      startDate: edu.startDate ? `${edu.startDate}-01-01T00:00:00.000Z` : "",
      degree: edu.degree || "",
      degreeType: edu.degreeType || "",
      endDate: edu.endDate ? `${edu.endDate}-01-01T00:00:00.000Z` : "",
      fileUpload: edu.fileUpload?.base64 || ""
    }));

    const res = await handleUpdateTutor({
      academics: formattedEducation,
      userId: tutorId,
      role: "tutor"
    });

    if (res) {
      setActiveTab("Description");
    }
  };

  return (
    <>
      {updating && <Loader />}

      <form
        className="max-w-[528px] w-[93%] mx-auto"
        onSubmit={handleSubmit(updateEducation)}
      >
        <h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
          Education (Optional)
        </h2>
        <p className="text-[#4B5563] sm:text-lg mb-8">
          Tell students more about the higher education that you've completed or
          are working on
        </p>

        {fields?.map((edu, index) => (
          <div key={edu.id}>
            {index > 0 && (
              <button
                type="button"
                onClick={() => {
                  remove(index);
                  setUploadedFiles((prev) => {
                    const updated = { ...prev };
                    delete updated[index];
                    return updated;
                  });
                }}
                className="p-1 rounded-full h-8 w-8 mt-auto mb-[10px] ml-auto flex justify-center items-center shrink-0 bg-red-100 hover:bg-red-200"
                title="Remove education"
              >
                <Trash2 size={16} className="text-red-500" />
              </button>
            )}

            <InputField
              register={register}
              fieldName={`academics.${index}.university`}
              placeHolder="Eg Bristol University"
              label="University"
              isRequired={true}
              error={errors?.academics?.[index]?.university?.message}
              rules={{ required: "University is required" }}
            />

            <InputField
              register={register}
              fieldName={`academics.${index}.degree`}
              placeHolder="Eg Bachelor's degree in English Language"
              label="Degree"
              isRequired={true}
              error={errors?.academics?.[index]?.degree?.message}
              rules={{ required: "Degree is required" }}
            />

            <CustomSelect
              placeholder="Choose a degree type"
              label="Degree type"
              options={[
                { value: "Bachelor's Degrees", label: "Bachelor's Degrees" },
                { value: "Master's Degrees", label: "Master's Degrees" },
                { value: "Phd", label: "Phd" }
              ]}
              name={`academics.${index}.degreeType`}
              control={control}
              isRequired={true}
              error={errors?.academics?.[index]?.degreeType?.message}
              rules={{ required: "Degree type is required" }}
              className="p-5 py-[22px]"
              parentClassName="mb-7"
            />

            <div className="flex sm:gap-5 gap-2">
              <CustomSelect
                placeholder="Select year"
                label="Start year"
                options={yearOptions}
                name={`academics.${index}.startDate`}
                control={control}
                isRequired={true}
                error={errors?.academics?.[index]?.startDate?.message}
                rules={{ required: "Start year is required" }}
                className="p-5 py-[22px]"
                parentClassName="mb-7"
              />

              <CustomSelect
                placeholder="Select year"
                label="End year"
                options={yearOptions}
                name={`academics.${index}.endDate`}
                control={control}
                isRequired={true}
                error={errors?.academics?.[index]?.endDate?.message}
                rules={{ required: "End year is required" }}
                className="p-5 py-[22px]"
                parentClassName="mb-7"
              />
            </div>

            <p className="text-secondary mb-3 max-sm:text-sm">
              Upload your certificate here
            </p>

            <label
              className="w-full border border-dashed border-[#D2D2D2] sm:p-5 p-3 flex flex-col items-center rounded-lg cursor-pointer mb-7"
              htmlFor={`upload-cert-${index}`}
            >
              <img src={uploadIcon} alt="upload icon" />
              <p className="text-[#4B5563] sm:text-lg mb-2">
                Choose a file or drag and drop it here
              </p>
              <p className="text-[#4B5563] sm:text-lg mb-5">
                Jpeg, Png, Pdf Max 5mb
              </p>

              <span className="text-primary font-bold sm:text-xl text-lg">
                Browse
              </span>

              <input
                type="file"
                id={`upload-cert-${index}`}
                className="hidden"
                aria-label="upload a certificate"
                onChange={(e) => handleFileChange(e, index)}
                accept="image/jpeg,image/png,application/pdf"
              />
            </label>

            {uploadedFiles[index] && (
              <div className="flex items-center gap-2 mb-6 text-[#4B5563] text-sm mt-[-14px]">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5 text-primary"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M19.5 14.25v3.375A2.625 2.625 0 0116.875 20.25H7.125A2.625 2.625 0 014.5 17.625V6.375A2.625 2.625 0 017.125 3.75h5.25L19.5 9v2.25"
                  />
                </svg>
                <span>{uploadedFiles[index].name}</span>
              </div>
            )}

            {index === fields.length - 1 && (
              <p
                onClick={handleAddCertificate}
                className="text-sm mt-[-20px] font-medium text-secondary underline mb-10 cursor-pointer"
              >
                Add another education
              </p>
            )}
          </div>
        ))}

        <div className="sm:flex gap-5">
          <Button
            className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
            onClick={() => setActiveTab("Certification")}
          >
            Back
          </Button>

          <Button className="w-full h-[50px]" disabled={updating} type="submit">
            Save And Continue
          </Button>
        </div>
      </form>
    </>
  );
};

export default TutorEducation;
