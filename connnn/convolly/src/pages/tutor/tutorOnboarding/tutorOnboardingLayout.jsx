import Navbar from "../../../components/navbar/navbar";
import React, { useState } from "react";
import AboutTutor from "./tabs/about/aboutTutor";
import TutorPhoto from "./tabs/photo/tutorPhoto";
import TutorProficiency from "./tabs/proficiency/tutorProficiency";
import TutorCertification from "./tabs/certification/tutorCertification";
import TutorEducation from "./tabs/education/tutorEducation";
import TutorDescription from "./tabs/description/tutorDescription";
import TutorVideo from "./tabs/video/tutorVideo";
import TutorAvailability from "./tabs/availability/tutorAvailability";
import TutorPrice from "./tabs/price/tutorPrice";
import useGet from "@/hooks/useGet";
import { useGetTutorDetailsQuery } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";
import OnboardingCompleted from "./tabs/completed/onboardingCompleted";
import Loader from "@/components/loader/loader";

const TutorOnboarding = () => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const [activeTab, setActiveTab] = useState("About");
  const { data: instructorDetails, isLoading } = useGet(
    useGetTutorDetailsQuery,
    tutorId,
    !!tutorId
  );

  const tabs = [
    { name: "About" },
    { name: "Photo" },
    { name: "Proficiency" },
    { name: "Certification" },
    { name: "Education" },
    { name: "Description" },
    { name: "Video" },
    { name: "Availability" },
    { name: "Price" }
  ];

  return (
    <div className="text-secondary sm:pb-14 pb-10 relative">
      <Navbar />
      {isLoading && <Loader />}

      <div className="hidden lg:flex justify-around bg-[#1FC16B1A] p-3 mt-3 mb-14">
        {tabs?.map(({ name }, index) => (
          <div
            key={index}
            className={`flex gap-2 items-center`}
            // onClick={activeTab == "Complete" ? null : () => setActiveTab(name)}
          >
            <span
              className={`block w-8 h-8 ${
                activeTab == name
                  ? "bg-primary text-white"
                  : "bg-white text-primary"
              } flex justify-center items-center text-xl font-bold rounded-full border border-primary transition-all`}
            >
              {index + 1}
            </span>

            <span className="text-lg text-secondary">{name}</span>
          </div>
        ))}
      </div>

      {activeTab === "About" && (
        <AboutTutor
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Photo" && (
        <TutorPhoto
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Proficiency" && (
        <TutorProficiency
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Certification" && (
        <TutorCertification
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Education" && (
        <TutorEducation
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Description" && (
        <TutorDescription
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Video" && (
        <TutorVideo
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Availability" && (
        <TutorAvailability
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Price" && (
        <TutorPrice
          setActiveTab={setActiveTab}
          instructorDetails={instructorDetails}
        />
      )}
      {activeTab === "Complete" && <OnboardingCompleted />}
    </div>
  );
};

export default TutorOnboarding;
