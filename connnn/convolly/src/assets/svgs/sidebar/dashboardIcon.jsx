import React from "react";

const DashboardIcon = ({ stroke = "#54C68A" }) => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21 7.25H13.5"
        stroke={stroke}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.6903 19.9567C13.5 19.4973 13.5 18.9149 13.5 17.75C13.5 16.5851 13.5 16.0027 13.6903 15.5433C13.944 14.9307 14.4307 14.444 15.0433 14.1903C15.5027 14 16.0851 14 17.25 14C18.4149 14 18.9973 14 19.4567 14.1903C20.0693 14.444 20.556 14.9307 20.8097 15.5433C21 16.0027 21 16.5851 21 17.75C21 18.9149 21 19.4973 20.8097 19.9567C20.556 20.5693 20.0693 21.056 19.4567 21.3097C18.9973 21.5 18.4149 21.5 17.25 21.5C16.0851 21.5 15.5027 21.5 15.0433 21.3097C14.4307 21.056 13.944 20.5693 13.6903 19.9567Z"
        stroke={stroke}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.1903 19.9567C3 19.4973 3 18.9149 3 17.75C3 16.5851 3 16.0027 3.1903 15.5433C3.44404 14.9307 3.93072 14.444 4.54329 14.1903C5.00272 14 5.58515 14 6.75 14C7.91485 14 8.49728 14 8.95671 14.1903C9.56928 14.444 10.056 14.9307 10.3097 15.5433C10.5 16.0027 10.5 16.5851 10.5 17.75C10.5 18.9149 10.5 19.4973 10.3097 19.9567C10.056 20.5693 9.56928 21.056 8.95671 21.3097C8.49728 21.5 7.91485 21.5 6.75 21.5C5.58515 21.5 5.00272 21.5 4.54329 21.3097C3.93072 21.056 3.44404 20.5693 3.1903 19.9567Z"
        stroke={stroke}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.1903 9.45671C3 8.99728 3 8.41485 3 7.25C3 6.08515 3 5.50272 3.1903 5.04329C3.44404 4.43072 3.93072 3.94404 4.54329 3.6903C5.00272 3.5 5.58515 3.5 6.75 3.5C7.91485 3.5 8.49728 3.5 8.95671 3.6903C9.56928 3.94404 10.056 4.43072 10.3097 5.04329C10.5 5.50272 10.5 6.08515 10.5 7.25C10.5 8.41485 10.5 8.99728 10.3097 9.45671C10.056 10.0693 9.56928 10.556 8.95671 10.8097C8.49728 11 7.91485 11 6.75 11C5.58515 11 5.00272 11 4.54329 10.8097C3.93072 10.556 3.44404 10.0693 3.1903 9.45671Z"
        stroke={stroke}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default DashboardIcon;
