import React from "react";

const applicationIcon = () => {
	return (
		<svg
			width="24"
			height="25"
			viewBox="0 0 24 25"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M13.9999 4.75C16.8111 5.47356 19.0243 7.68675 19.7479 10.498M9.99992 4.75C7.1887 5.47356 4.97551 7.68675 4.25195 10.498M4.25195 14.4979C4.97551 17.3091 7.1887 19.5224 9.99992 20.246M13.9999 20.246C16.8111 19.5224 19.0243 17.3091 19.7479 14.4979"
				stroke="#1A1A40"
				stroke-width="1.5"
				stroke-linecap="round"
			/>
			<path
				d="M10.2929 6.20711C10.5858 6.5 11.0572 6.5 12 6.5C12.9428 6.5 13.4142 6.5 13.7071 6.20711M10.2929 6.20711C10 5.91421 10 5.44281 10 4.5C10 3.55719 10 3.08579 10.2929 2.79289M13.7071 6.20711C14 5.91421 14 5.44281 14 4.5C14 3.55719 14 3.08579 13.7071 2.79289M13.7071 2.79289C13.4142 2.5 12.9428 2.5 12 2.5C11.0572 2.5 10.5858 2.5 10.2929 2.79289"
				stroke="#1A1A40"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
			<path
				d="M10.2929 22.2071C10.5858 22.5 11.0572 22.5 12 22.5C12.9428 22.5 13.4142 22.5 13.7071 22.2071M10.2929 22.2071C10 21.9142 10 21.4428 10 20.5C10 19.5572 10 19.0858 10.2929 18.7929M13.7071 22.2071C14 21.9142 14 21.4428 14 20.5C14 19.5572 14 19.0858 13.7071 18.7929M13.7071 18.7929C13.4142 18.5 12.9428 18.5 12 18.5C11.0572 18.5 10.5858 18.5 10.2929 18.7929"
				stroke="#1A1A40"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
			<path
				d="M18.2929 10.7929C18 11.0858 18 11.5572 18 12.5C18 13.4428 18 13.9142 18.2929 14.2071M18.2929 10.7929C18.5858 10.5 19.0572 10.5 20 10.5C20.9428 10.5 21.4142 10.5 21.7071 10.7929M18.2929 14.2071C18.5858 14.5 19.0572 14.5 20 14.5C20.9428 14.5 21.4142 14.5 21.7071 14.2071M21.7071 14.2071C22 13.9142 22 13.4428 22 12.5C22 11.5572 22 11.0858 21.7071 10.7929"
				stroke="#1A1A40"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
			<path
				d="M2.29289 10.7929C2 11.0858 2 11.5572 2 12.5C2 13.4428 2 13.9142 2.29289 14.2071M2.29289 10.7929C2.58579 10.5 3.05719 10.5 4 10.5C4.94281 10.5 5.41421 10.5 5.70711 10.7929M2.29289 14.2071C2.58579 14.5 3.05719 14.5 4 14.5C4.94281 14.5 5.41421 14.5 5.70711 14.2071M5.70711 14.2071C6 13.9142 6 13.4428 6 12.5C6 11.5572 6 11.0858 5.70711 10.7929"
				stroke="#1A1A40"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</svg>
	);
};

export default applicationIcon;
