import React from "react";

const Morning = ({active = false}) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.9999 2.3999C12.3182 2.3999 12.6234 2.52633 12.8484 2.75137C13.0735 2.97642 13.1999 3.28164 13.1999 3.5999V4.7999C13.1999 5.11816 13.0735 5.42339 12.8484 5.64843C12.6234 5.87347 12.3182 5.9999 11.9999 5.9999C11.6816 5.9999 11.3764 5.87347 11.1514 5.64843C10.9263 5.42339 10.7999 5.11816 10.7999 4.7999V3.5999C10.7999 3.28164 10.9263 2.97642 11.1514 2.75137C11.3764 2.52633 11.6816 2.3999 11.9999 2.3999ZM16.7999 11.9999C16.7999 13.2729 16.2942 14.4938 15.394 15.394C14.4938 16.2942 13.2729 16.7999 11.9999 16.7999C10.7269 16.7999 9.50596 16.2942 8.60579 15.394C7.70562 14.4938 7.1999 13.2729 7.1999 11.9999C7.1999 10.7269 7.70562 9.50596 8.60579 8.60579C9.50596 7.70562 10.7269 7.1999 11.9999 7.1999C13.2729 7.1999 14.4938 7.70562 15.394 8.60579C16.2942 9.50596 16.7999 10.7269 16.7999 11.9999ZM16.2431 17.9399L17.0915 18.7883C17.3178 19.0069 17.6209 19.1278 17.9356 19.1251C18.2502 19.1224 18.5512 18.9962 18.7737 18.7737C18.9962 18.5512 19.1224 18.2502 19.1251 17.9356C19.1278 17.6209 19.0069 17.3178 18.7883 17.0915L17.9399 16.2431C17.7136 16.0245 17.4105 15.9036 17.0958 15.9063C16.7812 15.909 16.4802 16.0352 16.2577 16.2577C16.0352 16.4802 15.909 16.7812 15.9063 17.0958C15.9036 17.4105 16.0245 17.7136 16.2431 17.9399ZM18.7871 5.2115C19.0121 5.43654 19.1384 5.74171 19.1384 6.0599C19.1384 6.3781 19.0121 6.68327 18.7871 6.9083L17.9399 7.7567C17.8292 7.87132 17.6968 7.96273 17.5504 8.02562C17.404 8.08851 17.2465 8.12162 17.0872 8.123C16.9278 8.12439 16.7698 8.09403 16.6224 8.03369C16.4749 7.97335 16.3409 7.88425 16.2282 7.77158C16.1156 7.65891 16.0265 7.52492 15.9661 7.37745C15.9058 7.22997 15.8754 7.07196 15.8768 6.91262C15.8782 6.75329 15.9113 6.59582 15.9742 6.44942C16.0371 6.30301 16.1285 6.1706 16.2431 6.0599L17.0915 5.2115C17.3165 4.98654 17.6217 4.86016 17.9399 4.86016C18.2581 4.86016 18.5621 4.98654 18.7871 5.2115ZM20.3999 13.1999C20.7182 13.1999 21.0234 13.0735 21.2484 12.8484C21.4735 12.6234 21.5999 12.3182 21.5999 11.9999C21.5999 11.6816 21.4735 11.3764 21.2484 11.1514C21.0234 10.9263 20.7182 10.7999 20.3999 10.7999H19.1999C18.8816 10.7999 18.5764 10.9263 18.3514 11.1514C18.1263 11.3764 17.9999 11.6816 17.9999 11.9999C17.9999 12.3182 18.1263 12.6234 18.3514 12.8484C18.5764 13.0735 18.8816 13.1999 19.1999 13.1999H20.3999ZM11.9999 17.9999C12.3182 17.9999 12.6234 18.1263 12.8484 18.3514C13.0735 18.5764 13.1999 18.8816 13.1999 19.1999V20.3999C13.1999 20.7182 13.0735 21.0234 12.8484 21.2484C12.6234 21.4735 12.3182 21.5999 11.9999 21.5999C11.6816 21.5999 11.3764 21.4735 11.1514 21.2484C10.9263 21.0234 10.7999 20.7182 10.7999 20.3999V19.1999C10.7999 18.8816 10.9263 18.5764 11.1514 18.3514C11.3764 18.1263 11.6816 17.9999 11.9999 17.9999ZM6.0599 7.7567C6.17132 7.8682 6.3036 7.95665 6.4492 8.01702C6.5948 8.07739 6.75086 8.10849 6.90848 8.10855C7.0661 8.1086 7.22218 8.07761 7.36782 8.01734C7.51347 7.95708 7.64581 7.86872 7.7573 7.7573C7.86879 7.64589 7.95725 7.51361 8.01762 7.36801C8.07799 7.22241 8.10909 7.06635 8.10915 6.90873C8.1092 6.75111 8.07821 6.59502 8.01795 6.44938C7.95768 6.30374 7.86932 6.1714 7.7579 6.0599L6.9083 5.2115C6.68198 4.99291 6.37886 4.87196 6.06422 4.87469C5.74958 4.87743 5.44861 5.00363 5.22612 5.22612C5.00363 5.44861 4.87743 5.74958 4.87469 6.06422C4.87196 6.37886 4.99291 6.68198 5.2115 6.9083L6.0599 7.7567ZM7.7567 17.9399L6.9083 18.7883C6.68198 19.0069 6.37886 19.1278 6.06422 19.1251C5.74958 19.1224 5.44861 18.9962 5.22612 18.7737C5.00363 18.5512 4.87743 18.2502 4.87469 17.9356C4.87196 17.6209 4.99291 17.3178 5.2115 17.0915L6.0599 16.2431C6.28623 16.0245 6.58935 15.9036 6.90398 15.9063C7.21862 15.909 7.5196 16.0352 7.74208 16.2577C7.96457 16.4802 8.09078 16.7812 8.09351 17.0958C8.09625 17.4105 7.97529 17.7136 7.7567 17.9399ZM4.7999 13.1999C5.11816 13.1999 5.42339 13.0735 5.64843 12.8484C5.87347 12.6234 5.9999 12.3182 5.9999 11.9999C5.9999 11.6816 5.87347 11.3764 5.64843 11.1514C5.42339 10.9263 5.11816 10.7999 4.7999 10.7999H3.5999C3.28164 10.7999 2.97642 10.9263 2.75137 11.1514C2.52633 11.3764 2.3999 11.6816 2.3999 11.9999C2.3999 12.3182 2.52633 12.6234 2.75137 12.8484C2.97642 13.0735 3.28164 13.1999 3.5999 13.1999H4.7999Z"
        fill={active ? "#3bb273" : "#A4A4A4"}
      />
    </svg>
  );
};

export default Morning;
