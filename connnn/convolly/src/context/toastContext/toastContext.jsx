import React from 'react'
import { ToastContainer, toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

export const ToastProvider = ({ children }) => {
  return (
    <>
      <ToastContainer />
      {children}
    </>
  )
}

export const useToast = () => {
  const showToast = (message, type = 'info') => {
    switch (type) {
      case 'success':
        return toast.success(message)
      case 'error':
        return toast.error(message)
      case 'warning':
        return toast.warning(message)
      case 'loading':
        return toast.loading(message)
      default:
        return toast.info(message)
    }
  }

  const showSuccess = message => toast.success(message)
  const showError = message => toast.error(message)
  const showLoading = message => toast.loading(message)
  const hideLoading = message => toast.dismiss(message)

  return {
    showToast,
    showSuccess,
    showError,
    showLoading,
    hideLoading
  }
}
