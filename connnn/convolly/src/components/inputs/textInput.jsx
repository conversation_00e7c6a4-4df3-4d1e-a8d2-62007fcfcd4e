import React from "react";

const TextInput = ({
	register,
	fieldName,
	placeHolder,
	defaultValue,
	autoComplete,
	disabled,
	registerOptions,
}) => {
	return (
		<input
			className={`text-sm sm:text-base border border-[#E8E8E8] bg-white p-3 px-4 rounded-lg w-full focus:outline-none ${
				disabled ? "cursor-not-allowed" : ""
			}`}
			type="text"
			placeholder={placeHolder}
			defaultValue={defaultValue}
			{...register(fieldName, registerOptions)}
			autoComplete={autoComplete}
			disabled={disabled}
		/>
	);
};

export default TextInput;
