import React from "react";

const NumberInput = ({ register, fieldName, placeHolder, defaultValue, autoComplete, disabled, sizeClass, isRequired, registerOptions }) => (
  <input
    className={`text-sm sm:text-base border ${sizeClass} border-[#E8E8E8] bg-white p-3 px-4 rounded-lg w-full satoshi focus:outline-none ${disabled ? "cursor-not-allowed" : ""}`}
    type="number"
    defaultValue={defaultValue || ""}
    placeholder={placeHolder || "Enter amount"}
    {...register(fieldName, registerOptions || { required: isRequired ? `This field is required` : null, min: 0 })}
    inputMode={fieldName === "amount" ? "decimal" : "numeric"}
    pattern={fieldName === "amount" ? "^\\d*\\.?\\d*$" : "\\d*"}
    step={fieldName === "amount" ? "0.01" : "1"}
    onKeyDown={(e) => (e.key === "ArrowUp" || e.key === "ArrowDown") && e.preventDefault()}
    onWheel={(e) => e.preventDefault()}
    autoComplete={autoComplete}
    disabled={disabled}
  />
);

export default NumberInput;
