import React from "react";

const CheckboxInput = ({ register, fieldName, defaultValue, disabled, isRequired, sizeClass, registerOptions }) => (
  <input
    type="checkbox"
    defaultChecked={defaultValue}
    {...register(fieldName, registerOptions || { required: isRequired ? `This field is required` : null })}
    disabled={disabled}
    className={`custom-checkbox text-sm border ${sizeClass} border-[#E8E8E8] bg-white rounded-xl satoshi focus:outline-none ${disabled ? "cursor-not-allowed" : ""}`}
  />
);

export default CheckboxInput;
