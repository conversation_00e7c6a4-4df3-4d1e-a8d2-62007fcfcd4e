import React from "react";
import { X } from "lucide-react";
import clsx from "clsx";

const CustomModal = ({
  isOpen,
  onClose,
  title,
  children,
  size = "md", // sm, md, lg
  hideCloseIcon = false,
  className = "",
}) => {
  if (!isOpen) return null;



  const sizeClass = {
    sm: "max-w-sm",
    md: "max-w-lg",
    lg: "max-w-3xl",
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div
        className={clsx(
          "relative w-full mx-4 bg-white rounded-xl shadow-xl",
          sizeClass[size],
          className
        )}
      >
        <div className="flex items-center justify-end px-6 py-4">
          {!hideCloseIcon && (
            <button onClick={onClose} className="text-gray-500 hover:text-black">
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        <div className="p-6 sm:p-10 z-30">{children}</div>
      </div>
    </div>
  );
};

export default CustomModal;
