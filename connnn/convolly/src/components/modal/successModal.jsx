import React from "react";
import CustomModal from "./modal";
import { Button } from "@/components/button/button";
import successGreenCheck from "@/assets/svgs/successGreenCheck.svg";
import confetti from "@/assets/svgs/confetti.svg";

const SuccessModal = ({
  isOpen,
  onClose,
  title = "",
  message = "",
  buttonText = "",
  onButtonClick
}) => {

  return (
    <CustomModal isOpen={isOpen} onClose={onClose} title="">
      <div className="flex flex-col items-center text-center relative">
        <img src={confetti} alt="confetti vector" className="absolute top-[-30px]" />
        <img src={successGreenCheck} alt="green success icon" className="mt-24 relative z-50" />

        {title && <h3 className="mt-6 text-xl font-semibold relative z-50">{title}</h3>}
        {message && <p className="mt-2 text-gray-600 relative z-50">{message}</p>}

        <Button className="mt-6 relative z-50 w-full h-[50px]" onClick={onButtonClick || onClose}>
          {buttonText}
        </Button>
      </div>
    </CustomModal>
  );
};

export default SuccessModal;
