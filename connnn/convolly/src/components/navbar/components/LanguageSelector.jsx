import React, { useEffect, useState } from "react";
import { Globe } from "lucide-react";

const LanguageSelector = () => {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedLanguage, setSelectedLanguage] = useState("en");
	const [isGoogleTranslateLoaded, setIsGoogleTranslateLoaded] = useState(false);

	const languages = [
		{ code: "en", name: "English" },
		{ code: "es", name: "Español" },
		{ code: "fr", name: "Français" },
		{ code: "de", name: "<PERSON><PERSON><PERSON>" },
		{ code: "it", name: "Italiano" },
		{ code: "pt", name: "Portug<PERSON><PERSON><PERSON>" },
		{ code: "ru", name: "Русский" },
		{ code: "zh-CN", name: "中文" },
		{ code: "ja", name: "日本語" },
		{ code: "ko", name: "한국어" },
		{ code: "ar", name: "العربية" },
		{ code: "hi", name: "हिन्दी" },
	];

	const changeLanguage = (lang) => {
		setSelectedLanguage(lang);
		setIsOpen(false);

		const select = document.querySelector(".goog-te-combo");
		if (select) {
			select.value = lang;
			select.dispatchEvent(new Event("change"));
		}
	};

	useEffect(() => {
		const interval = setInterval(() => {
			if (window.google && window.google.translate) {
				setIsGoogleTranslateLoaded(true);
				clearInterval(interval);
			}
		}, 100);

		return () => clearInterval(interval);
	}, []);

	return (
		<div className="relative">
			{/* Mobile-friendly dropdown button */}
			<button
				onClick={() => setIsOpen(!isOpen)}
				className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-100 transition-colors"
				aria-label="Language selector"
			>
				<Globe size={20} />
				<span className="hidden sm:inline">
					{languages.find((lang) => lang.code === selectedLanguage)?.name ||
						selectedLanguage}
				</span>
				<span className="sm:hidden">{selectedLanguage}</span>
			</button>

			{/* Dropdown menu */}
			{isOpen && (
				<div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
					{languages.map((language) => (
						<button
							key={language.code}
							onClick={() => changeLanguage(language.code)}
							className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
								selectedLanguage === language.code
									? "bg-blue-50 text-primary"
									: "text-gray-700"
							}`}
						>
							{language.name}
						</button>
					))}
				</div>
			)}
		</div>
	);
};

export default LanguageSelector;
