import Navbar from "@/components/navbar/navbar";
import AdminSidebar from "@/pages/admin/components/sidebar/AdminSidebar";
import React, { useState } from "react";
import { Outlet } from "react-router-dom";

const AdminLayoutWrapper = () => {
	const [sidebarOpen, setSidebarOpen] = useState(false);

	const toggleSidebar = () => {
		setSidebarOpen(!sidebarOpen);
	};
	return (
		<div className="h-screen overflow-hidden flex flex-col">
			<Navbar toggleSidebar={toggleSidebar} />

			<div className="flex overflow-hidden flex-1 h-full">
				{/* Pass the toggle function and state to Sidebar */}
				<AdminSidebar isOpen={sidebarOpen} toggleSidebar={toggleSidebar} />

				<div className="lg:p-10 md:p-8 p-3 w-full overflow-y-auto">
					{/* Pass the toggle function to Outlet's context */}
					<Outlet />
				</div>
			</div>
		</div>
	);
};

export default AdminLayoutWrapper;
