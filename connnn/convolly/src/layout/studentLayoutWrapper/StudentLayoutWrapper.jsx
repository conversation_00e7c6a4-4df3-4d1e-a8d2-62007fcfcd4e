import React, { useState } from "react";
import Navbar from "@/components/navbar/navbar";
import Sidebar from "@/pages/student/studentDashboard/components/Sidebar";
import { Outlet } from "react-router-dom";
import LogoutModal from "@/pages/auth/LogoutModal";

const StudentLayoutWrapper = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="h-screen overflow-hidden flex flex-col">
      <Navbar toggleSidebar={toggleSidebar} />

      <div className="flex overflow-hidden flex-1 h-full">
        <Sidebar
          isOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          setShowLogoutModal={setShowLogoutModal}
        />

        <div className="lg:p-10 md:p-8 p-3 w-full overflow-y-auto">
          <Outlet context={{ setShowLogoutModal }} />
        </div>
      </div>

      {showLogoutModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <LogoutModal onCancel={() => setShowLogoutModal(false)} />
        </div>
      )}
    </div>
  );
};

export default StudentLayoutWrapper;
