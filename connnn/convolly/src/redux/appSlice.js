import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  userInfo: localStorage.getItem("userInfo")
    ? JSON?.parse(localStorage.getItem("userInfo"))
    : null
};

const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    setUserInfo: (state, action) => {
      state.userInfo = {
        ...state.userInfo,
        ...action.payload
      };
      localStorage.setItem(
        "userInfo",
        JSON.stringify({
          ...state.userInfo,
          ...action.payload
        })
      );
    },
    logOut: (state) => {
      state.userInfo = null;
      localStorage.removeItem("userInfo");
      window.location.href = "/signin";
    }
  }
});

export default appSlice.reducer;
export const { setUserInfo, logOut } = appSlice.actions;
