import { generalApiSlice } from "../apiSlice";

const accountApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		updateAccountPassword: builder.mutation({
			query: (data) => ({
				url: `/api/profile/${data.userId}`,
				method: "PUT",
				body: {
					currentPassword: data.currentPassword,
					newPassword: data.newPassword,
				},
			}),
		}),
		deleteStudentAccount: builder.mutation({
			query: (userId) => ({
				url: `/api/profile/students/${userId}`,
				method: "DELETE",
			}),
		}),

		deleteTutorAccount: builder.mutation({
			query: (userId) => ({
				url: `/api/profile/tutors/${userId}`,
				method: "DELETE",
			}),
		}),
	}),

	overrideExisting: false,
});

export const {
	useUpdateAccountPasswordMutation,
	useDeleteStudentAccountMutation,
	useDeleteTutorAccountMutation,
} = accountApiSlice;
