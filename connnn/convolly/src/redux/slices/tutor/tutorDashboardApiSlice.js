import { generalApiSlice } from "../../apiSlice";

const tutorDashboardApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getEarnings: builder.query({
      query: () => ({
        url: `/api/profile/tutors/earnings`,
        method: "GET"
      })
    }),

    getActiveStudents: builder.query({
      query: () => ({
        url: `/api/tutor/my-students`,
        method: "GET"
      })
    }),

    requestWithdrawal: builder.mutation({
      query: (body) => ({
        url: `/api/withdrawals/request`,
        method: "POST",
        body
      })
    })
  }),

  overrideExisting: false
});

export const {
  useGetEarningsQuery,
  useRequestWithdrawalMutation,
  useGetActiveStudentsQuery
} = tutorDashboardApiSlice;
