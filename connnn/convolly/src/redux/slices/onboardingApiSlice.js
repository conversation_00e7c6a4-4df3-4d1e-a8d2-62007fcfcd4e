import { generalApiSlice } from "../apiSlice";

const tutorOnboardingApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // update  info
    updateProfile: builder.mutation({
      query: (body) => ({
        url: `/api/profile/onboard`,
        method: "PUT",
        body
      })
    }),

    // get tutor details
    getTutorDetails: builder.query({
      query: (id) => ({
        url: `/api/profile/tutors/${id}`,
        method: "GET"
      })
    }),

    // get student details
    getStudentDetails: builder.query({
      query: (id) => ({
        url: `/api/profile/students/${id}`,
        method: "GET"
      })
    })
  }),

  overrideExisting: false
});

export const { useUpdateProfileMutation, useGetTutorDetailsQuery, useGetStudentDetailsQuery } =
  tutorOnboardingApiSlice;
