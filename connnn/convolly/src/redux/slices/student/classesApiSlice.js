import { generalApiSlice } from "../../apiSlice";

export const classesApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		getClasses: builder.query({
			query: () => ({
				url: "/api/bookings/student",
				method: "GET",
			}),
		}),

		getTutorClasses: builder.query({
			query: (id) => ({
				url: `/api/bookings/tutor-detailed/${id}`,
				method: "GET",
			}),
		}),
	}),
	overrideExisting: false,
});

export const { useGetClassesQuery, useGetTutorClassesQuery } = classesApiSlice;
