import { generalApiSlice } from "../../apiSlice";

const subscriptionApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		subscribeToTutor: builder.mutation({
			query: (body) => ({
				url: `/api/subscription/subscribe`,
				method: "POST",
				body,
			}),
		}),

		getStudentSubscriptions: builder.query({
			query: (id) => ({
				url: `/api/subscription/${id}`,
				method: "GET",
			}),
		}),

		pauseStudentSubscription: builder.mutation({
			query: (id) => ({
				url: `/api/subscription/${id}/pause`,
				method: "PATCH",
			}),
		}),

		cancelStudentSubscription: builder.mutation({
			query: (id) => ({
				url: `/api/subscription/${id}/cancel`,
				method: "DELETE",
			}),
		}),
	}),

	overrideExisting: false,
});

export const {
	useSubscribeToTutorMutation,
	useGetStudentSubscriptionsQuery,
	usePauseStudentSubscriptionMutation,
	useCancelStudentSubscriptionMutation,
} = subscriptionApiSlice;
