import { generalApiSlice } from "../../apiSlice";

const studentSettingsApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		getUpdateStudentProfile: builder.mutation({
			query: ({ userId, ...body }) => ({
				url: `/api/profile/students/${userId}`,
				method: "PUT",
				body,
			}),
		}),
	}),

	overrideExisting: false,
});

export const { useGetUpdateStudentProfileMutation } = studentSettingsApiSlice;
