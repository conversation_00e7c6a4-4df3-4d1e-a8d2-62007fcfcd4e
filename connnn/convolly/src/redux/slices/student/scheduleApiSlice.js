import { generalApiSlice } from "../../apiSlice";

const scheduleApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		scheduleClass: builder.mutation({
			query: (body) => ({
				url: `/api/bookings/enhanced`,
				method: "POST",
				body,
			}),
		}),
		reScheduleClass: builder.mutation({
			query: ({ id, ...body }) => ({
				url: `/api/bookings/reschedule/${id}`,
				method: "PUT",
				body,
			}),
		}),

		cancelClassBooking: builder.mutation({
			query: ({ id, ...body }) => ({
				url: `/api/bookings/cancel/${id}`,
				method: "PUT",
				body,
			}),
		}),

		getTutorCalendar: builder.query({
			query: (id) => ({
				url: `/api/bookings/tutor-availability/${id}`,
				method: "GET",
			}),
		}),
	}),

	overrideExisting: false,
});

export const {
	useScheduleClassMutation,
	useGetTutorCalendarQuery,
	useReScheduleClassMutation,
	useCancelClassBookingMutation,
} = scheduleApiSlice;
