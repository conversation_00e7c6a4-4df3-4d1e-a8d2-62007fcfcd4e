import { generalApiSlice } from "../../apiSlice";

export const profileApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		updateStudentProfile: builder.mutation({
			query: ({ id, payload }) => ({
				url: `/api/profile/student/${id}`,
				method: "PUT",
				body: payload,
			}),
		}),
		updateTutorProfile: builder.mutation({
			query: ({ id, payload }) => ({
				url: `/api/profile/tutors/${id}`,
				method: "PUT",
				body: payload,
			}),
		}),
	}),

	overrideExisting: false,
});

export const {
	useUpdateStudentProfileMutation,
	useUpdateTutorProfileMutation,
} = profileApiSlice;
