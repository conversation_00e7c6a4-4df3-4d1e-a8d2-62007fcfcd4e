import { generalApiSlice } from "../../apiSlice";

const profileImageApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Upload profile image
    uploadProfileImage: builder.mutation({
      query: (body) => ({
        url: `/api/profile-image/upload`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["ProfileImage"],
    }),

    // Get profile image URL
    getProfileImage: builder.query({
      query: () => ({
        url: `/api/profile-image`,
        method: "GET",
      }),
      providesTags: ["ProfileImage"],
    }),

    // Delete profile image
    deleteProfileImage: builder.mutation({
      query: () => ({
        url: `/api/profile-image`,
        method: "DELETE",
      }),
      invalidatesTags: ["ProfileImage"],
    }),
  }),

  overrideExisting: false,
});

export const {
  useUploadProfileImageMutation,
  useGetProfileImageQuery,
  useDeleteProfileImageMutation,
} = profileImageApiSlice;
