import { generalApiSlice } from "../../apiSlice";

const adminStudentApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAllStudents: builder.query({
      query: () => ({
        url: `/api/admin/students`,
        method: "GET"
      })
    }),

    getStudentDetails: builder.query({
      query: (id) => ({
        url: `/api/admin/students/${id}`,
        method: "GET"
      })
    })
  }),

  overrideExisting: false
});

export const { useGetAllStudentsQuery, useGetStudentDetailsQuery } =
  adminStudentApiSlice;
