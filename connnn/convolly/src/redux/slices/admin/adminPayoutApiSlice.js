import { generalApiSlice } from "../../apiSlice";

const adminPayoutApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getPayouts: builder.query({
      query: () => ({
        url: `/api/admin/financial/tutor-payouts`,
        method: "GET"
      })
    }),

    getPayoutStats: builder.query({
      query: () => ({
        url: `/api/admin/financial/payment-analytics`,
        method: "GET"
      })
    }),
  }),

  overrideExisting: false
});

export const { useGetPayoutsQuery, useGetPayoutStatsQuery } =
  adminPayoutApiSlice;
