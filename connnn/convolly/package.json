{"name": "convolly", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@reduxjs/toolkit": "^2.7.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "agora-chat": "^1.3.1", "agora-rtc-sdk-ng": "^4.23.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-flag-icons": "^1.5.19", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "jwt-decode": "^4.0.0", "livekit-client": "^2.13.3", "livekit-server-sdk": "^2.13.0", "lucide-react": "^0.507.0", "posthog-js": "^1.255.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-date-range": "^2.0.1", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "react-youtube": "^10.1.0", "recharts": "^3.0.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.8", "tailwind-merge": "^3.2.0", "white-web-sdk": "^2.16.53"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.19", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "vite": "^6.3.1", "vite-plugin-svgr": "^4.3.0"}}