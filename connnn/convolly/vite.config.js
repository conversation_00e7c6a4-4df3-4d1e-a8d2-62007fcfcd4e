import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";
import path from 'path';


export default defineConfig({
  plugins: [react(), svgr()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src")
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@radix-ui/react-dropdown-menu', '@radix-ui/react-navigation-menu', '@radix-ui/react-select', '@radix-ui/react-slider', '@radix-ui/react-slot'],
          'chart-vendor': ['chart.js', 'react-chartjs-2', 'recharts'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'react-select', 'react-datepicker', 'react-date-range'],
          'agora-vendor': ['agora-rtc-sdk-ng', 'agora-chat'],
          'livekit-vendor': ['livekit-client', 'livekit-server-sdk'],
          'socket-vendor': ['socket.io-client'],
          'payment-vendor': ['@stripe/stripe-js', '@stripe/react-stripe-js'],
          'utility-vendor': ['date-fns', 'dayjs', 'jwt-decode', 'clsx', 'class-variance-authority', 'tailwind-merge'],

          // Feature chunks
          'admin-pages': [
            '/src/pages/admin/adminDashboard/AdminDashboard',
            '/src/pages/admin/adminDashboard/AdminStudentsPage',
            '/src/pages/admin/adminDashboard/AdminStudentProfile',
            '/src/pages/admin/adminDashboard/AdminTutorProfile',
            '/src/pages/admin/adminDashboard/AdminTutorsPage',
            '/src/pages/admin/adminDashboard/AdminHistory',
            '/src/pages/admin/adminDashboard/AdminBookings',
            '/src/pages/admin/adminDashboard/AdminPayouts',
            '/src/pages/admin/adminDashboard/AdminSubAccounts',
            '/src/pages/admin/adminDashboard/AdminRefundApproval',
            '/src/pages/admin/adminDashboard/SystemSsettings',
            '/src/pages/admin/adminDashboard/AdminReviews',
            '/src/pages/admin/adminDashboard/AdminReport',
            '/src/pages/admin/adminDashboard/AdminApplications'
          ],
          'tutor-dashboard': [
            '/src/pages/tutor/tutorDashboard/tutorDashboard',
            '/src/pages/tutor/tutorDashboard/mylessons/TutorLessons',
            '/src/pages/tutor/tutorDashboard/reviews/TutorReviews',
            '/src/pages/tutor/tutorDashboard/settings/TutorSettings'
          ],
          'tutor-onboarding': ['/src/pages/tutor/tutorOnboarding/tutorOnboardingLayout'],
          'tutor-profile': ['/src/pages/tutor/tutorProfile/tutorProfile'],
          'student-pages': [
            '/src/pages/student/studentDashboard/studentDashboard',
            '/src/pages/student/studentDashboard/mylessons/MyLessons',
            '/src/pages/student/studentDashboard/mylessons/tutors/Tutors',
            '/src/pages/student/studentDashboard/mylessons/calendar/Calender',
            '/src/pages/student/studentDashboard/reviews/StudentReviews',
            '/src/pages/student/studentDashboard/settings/StudentSettings',
            '/src/pages/student/studentDashboard/mylessons/tutors/TutorDetails',
            '/src/pages/student/studentDashboard/mylessons/tutors/SubscriptionPage',
            '/src/pages/student/studentDashboard/subscription/Subscription',
            '/src/pages/student/studentOnboarding/studentOnboardingLayout'
          ],
          'classroom': ['/src/pages/classroom/classroomLayout'],
          'messaging': ['/src/pages/messaging/Chats'],
          'payments': ['/src/pages/payments/Payments', '/src/pages/payments/PostPayment'],
          'whiteboard': ['white-web-sdk']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
});
