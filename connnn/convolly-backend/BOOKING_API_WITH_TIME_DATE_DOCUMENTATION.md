# Booking API with Time and Date Information

This document describes the enhanced booking API endpoints that provide detailed time and date information for both students and tutors.

## 📚 **New Endpoints Overview**

### 1. **Get Student Bookings**
- **URL**: `GET /api/bookings/student`
- **URL (Admin)**: `GET /api/bookings/student/:studentId`
- **Description**: Get all bookings for a student with detailed time and date information

### 2. **Get Tutor Bookings**
- **URL**: `GET /api/bookings/tutor-detailed`
- **URL (Admin)**: `GET /api/bookings/tutor-detailed/:tutorId`
- **Description**: Get all bookings for a tutor with detailed time and date information

### 3. **Get Upcoming Bookings**
- **URL**: `GET /api/bookings/upcoming`
- **Description**: Get upcoming bookings for the authenticated user (student or tutor)

---

## 🔐 **Authentication & Authorization**

All endpoints require authentication via JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

**Access Control:**
- **Students**: Can only view their own bookings
- **Tutors**: Can only view their own bookings
- **Admins**: Can view any student's or tutor's bookings using the ID parameter

---

## 📋 **Query Parameters**

### **Common Parameters for Student/Tutor Bookings:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `status` | string | - | Filter by status: `scheduled`, `completed`, `cancelled`, `no-show`, `confirmed` |
| `startDate` | string (ISO) | - | Filter bookings from this date |
| `endDate` | string (ISO) | - | Filter bookings until this date |
| `page` | number | 1 | Page number for pagination |
| `limit` | number | 10 | Number of results per page |
| `sortBy` | string | `scheduledTime` | Sort field |
| `sortOrder` | string | `desc` | Sort order: `asc` or `desc` |

### **Upcoming Bookings Parameters:**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `limit` | number | 5 | Number of upcoming bookings to return |

---

## 📊 **Response Format**

### **Student Bookings Response:**

```json
{
  "success": true,
  "data": {
    "bookings": [
      {
        "id": "lesson_id",
        "title": "Math Tutoring Session",
        "scheduledTime": "2024-01-15T14:00:00.000Z",
        "duration": 60,
        "status": "scheduled",
        "isFreeTrial": false,
        "notes": "Focus on algebra",
        "confirmedAt": null,
        "cancelledAt": null,
        "cancellationReason": null,
        "createdAt": "2024-01-10T10:00:00.000Z",
        "tutor": {
          "id": "tutor_id",
          "name": "John Smith",
          "email": "<EMAIL>",
          "avatar": "avatar_url",
          "subjects": ["Mathematics", "Physics"],
          "basePrice": 25
        },
        "subscription": {
          "id": "subscription_id",
          "planType": "3_lessons_weekly",
          "lessonsPerWeek": 3,
          "monthlyPrice": 330,
          "status": "active"
        },
        "endTime": "2024-01-15T15:00:00.000Z",
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedTime": "02:00 PM"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    },
    "summary": {
      "totalBookings": 25,
      "upcomingBookings": 5,
      "completedBookings": 18,
      "cancelledBookings": 2,
      "freeTrialBookings": 2
    }
  }
}
```

### **Tutor Bookings Response:**

```json
{
  "success": true,
  "data": {
    "bookings": [
      {
        "id": "lesson_id",
        "title": "Math Tutoring Session",
        "scheduledTime": "2024-01-15T14:00:00.000Z",
        "duration": 60,
        "status": "scheduled",
        "isFreeTrial": false,
        "notes": "Student needs help with calculus",
        "student": {
          "id": "student_id",
          "name": "Jane Doe",
          "email": "<EMAIL>",
          "avatar": "avatar_url",
          "learningReasons": ["Academic improvement", "Test preparation"],
          "skillsToImprove": ["Problem solving", "Mathematical reasoning"]
        },
        "subscription": {
          "id": "subscription_id",
          "planType": "2_lessons_weekly",
          "lessonsPerWeek": 2,
          "monthlyPrice": 220,
          "status": "active"
        },
        "endTime": "2024-01-15T15:00:00.000Z",
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedTime": "02:00 PM"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 45,
      "pages": 5
    },
    "summary": {
      "totalBookings": 45,
      "upcomingBookings": 8,
      "completedBookings": 35,
      "cancelledBookings": 2,
      "freeTrialBookings": 5
    }
  }
}
```

### **Upcoming Bookings Response:**

```json
{
  "success": true,
  "data": {
    "upcomingBookings": [
      {
        "id": "lesson_id",
        "title": "Physics Tutoring",
        "scheduledTime": "2024-01-15T16:00:00.000Z",
        "duration": 50,
        "status": "confirmed",
        "isFreeTrial": false,
        "endTime": "2024-01-15T16:50:00.000Z",
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedTime": "04:00 PM",
        "timeUntilSession": "2 hours, 30 minutes",
        "tutor": {
          "id": "tutor_id",
          "name": "Dr. Smith",
          "email": "<EMAIL>",
          "avatar": "avatar_url",
          "subjects": ["Physics", "Mathematics"],
          "basePrice": 40
        }
      }
    ],
    "count": 1,
    "userRole": "student"
  }
}
```

---

## 🚀 **Usage Examples**

### **Get Student's Own Bookings:**
```bash
curl -X GET "http://localhost:3000/api/bookings/student?status=scheduled&page=1&limit=5" \
  -H "Authorization: Bearer your-jwt-token"
```

### **Get Tutor's Bookings for Specific Date Range:**
```bash
curl -X GET "http://localhost:3000/api/bookings/tutor-detailed?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer your-jwt-token"
```

### **Get Upcoming Bookings:**
```bash
curl -X GET "http://localhost:3000/api/bookings/upcoming?limit=3" \
  -H "Authorization: Bearer your-jwt-token"
```

### **Admin Get Specific Student's Bookings:**
```bash
curl -X GET "http://localhost:3000/api/bookings/student/student_id_here" \
  -H "Authorization: Bearer admin-jwt-token"
```

---

## ⚡ **Key Features**

### **📅 Time & Date Information:**
- **scheduledTime**: ISO 8601 timestamp
- **endTime**: Calculated end time based on duration
- **dayOfWeek**: Human-readable day name
- **formattedDate**: Formatted date string
- **formattedTime**: 12-hour formatted time
- **timeUntilSession**: Human-readable countdown (upcoming only)

### **📊 Summary Statistics:**
- Total bookings count
- Upcoming bookings count
- Completed bookings count
- Cancelled bookings count
- Free trial bookings count

### **🔍 Advanced Filtering:**
- Filter by status, date range
- Pagination support
- Flexible sorting options

### **👥 Detailed User Information:**
- **For Students**: Tutor details, subjects, pricing
- **For Tutors**: Student details, learning goals
- **Subscription Information**: Plan details, pricing

---

## 🛡️ **Error Handling**

### **Common Error Responses:**

```json
{
  "success": false,
  "message": "Authentication required"
}
```

```json
{
  "success": false,
  "message": "Access denied. Students can only view their own bookings."
}
```

```json
{
  "success": false,
  "message": "Invalid student ID format"
}
```

---

## 📝 **Notes**

1. **Time Zones**: All times are stored and returned in UTC. Frontend should handle timezone conversion.

2. **Pagination**: Use pagination for large datasets to improve performance.

3. **Caching**: Consider implementing caching for frequently accessed booking data.

4. **Real-time Updates**: For real-time booking updates, consider implementing WebSocket connections.

5. **Performance**: The endpoints include database population for related data. Monitor performance with large datasets.
