# Enhanced Booking API Documentation

## Overview

The enhanced booking system automatically creates calendars for users upon registration and provides flexible booking endpoints that add sessions to both tutor and student calendars.

## Calendar Creation

### Automatic Calendar Creation
When users register (tutors or students), calendars are automatically created using their unique user ID:

**For Tutors:**
- Main Teaching Calendar (shared)
- Subject-specific calendars (if teaching subjects provided)
- Personal Calendar (private)

**For Students:**
- Learning Schedule (private)
- Personal Schedule (private)

## Booking Endpoints

### 1. Enhanced Booking (Recommended)
**POST** `/api/bookings/enhanced`

Creates sessions in both tutor and student calendars automatically.

#### Request Body:
```json
{
  "tutorId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "studentId": "64f8a1b2c3d4e5f6a7b8c9d1",   // optional
  "title": "Math Lesson",
  "description": "Algebra and geometry review",
  "startDateTime": "2024-01-20T14:00:00.000Z",
  "endDateTime": "2024-01-20T15:00:00.000Z",
  "subject": "Mathematics"
}
```

#### Features:
- **Automatic Calendar Selection**: Uses tutor's first available calendar
- **Dual Calendar Events**: Creates events in both tutor and student calendars
- **Conflict Detection**: Checks for conflicts in both calendars
- **Subscription Validation**: Ensures student has active subscription or free trial
- **Flexible Student ID**: If `studentId` not provided, uses authenticated user

#### Response:
```json
{
  "success": true,
  "message": "Session booked successfully in both calendars",
  "data": {
    "tutorEvent": {
      "_id": "...",
      "calendarId": "...",
      "title": "Math Lesson - John Doe",
      "description": "Algebra and geometry review\nStudent: <EMAIL>\nSubject: Mathematics\nType: Paid Lesson",
      "startDateTime": "2024-01-20T14:00:00.000Z",
      "endDateTime": "2024-01-20T15:00:00.000Z",
      "status": "confirmed"
    },
    "studentEvent": {
      "_id": "...",
      "calendarId": "...",
      "title": "Math Lesson - Jane Smith",
      "description": "Algebra and geometry review\nTutor: <EMAIL>\nSubject: Mathematics\nType: Paid Lesson",
      "startDateTime": "2024-01-20T14:00:00.000Z",
      "endDateTime": "2024-01-20T15:00:00.000Z",
      "status": "confirmed"
    },
    "isFreeTrial": false,
    "subscriptionInfo": {
      "remainingLessons": 7,
      "freeTrialLessonsRemaining": 0
    },
    "consumeMessage": "Lesson consumed successfully"
  }
}
```

### 2. Enhanced Schedule Booking
**POST** `/api/schedule/book-session`

Updated to support the new structure with optional `studentId` and `calendarId`.

#### Request Body:
```json
{
  "tutorId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "calendarId": "64f8a1b2c3d4e5f6a7b8c9d1",  // optional
  "studentId": "64f8a1b2c3d4e5f6a7b8c9d2",   // optional
  "title": "Math Lesson",
  "description": "Algebra and geometry review",
  "startDateTime": "2024-01-20T14:00:00.000Z",
  "endDateTime": "2024-01-20T15:00:00.000Z",
  "subject": "Mathematics"
}
```

#### Features:
- **Optional Calendar ID**: If not provided, uses tutor's first calendar
- **Optional Student ID**: If not provided, uses authenticated user
- **Dual Calendar Events**: Creates events in both calendars when possible
- **Enhanced Conflict Detection**: Checks both tutor and student calendars

## Calendar Management

### Get User's Calendars
**GET** `/api/calendars/my-calendars`

Returns all calendars owned by the authenticated user.

### Get Tutor's Shared Calendars
**GET** `/api/calendars/tutor/:tutorId`

Returns shared calendars for a specific tutor (for students to view availability).

### Create Additional Calendar
**POST** `/api/calendars/`

```json
{
  "name": "Special Sessions",
  "description": "Calendar for special tutoring sessions",
  "color": "#FF5733",
  "isShared": true
}
```

## Authentication

All booking endpoints require authentication:
```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

Common error responses:

### 400 Bad Request
```json
{
  "message": "tutorId, title, startDateTime, and endDateTime are required."
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Cannot book session",
  "reason": "No active subscription or free trial",
  "subscriptionStatus": "inactive"
}
```

### 409 Conflict
```json
{
  "message": "Time slot is not available in tutor's calendar"
}
```

## Usage Examples

### Student Booking a Session
```javascript
// Student books a session (studentId automatically determined from auth)
const response = await fetch('/api/bookings/enhanced', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    tutorId: "64f8a1b2c3d4e5f6a7b8c9d0",
    title: "Physics Lesson",
    description: "Mechanics and thermodynamics",
    startDateTime: "2024-01-25T16:00:00.000Z",
    endDateTime: "2024-01-25T17:00:00.000Z",
    subject: "Physics"
  })
});
```

### Admin/Tutor Booking for Specific Student
```javascript
// Admin or tutor books a session for a specific student
const response = await fetch('/api/bookings/enhanced', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    tutorId: "64f8a1b2c3d4e5f6a7b8c9d0",
    studentId: "64f8a1b2c3d4e5f6a7b8c9d1",
    title: "Chemistry Lab Session",
    description: "Organic chemistry experiments",
    startDateTime: "2024-01-26T14:00:00.000Z",
    endDateTime: "2024-01-26T16:00:00.000Z",
    subject: "Chemistry"
  })
});
```

## Benefits

1. **Automatic Calendar Creation**: No manual setup required for new users
2. **Dual Calendar Visibility**: Both tutor and student see the session
3. **Flexible Booking**: Support for different user roles and scenarios
4. **Conflict Prevention**: Automatic conflict detection across calendars
5. **Subscription Integration**: Built-in subscription and free trial validation
6. **Comprehensive Error Handling**: Clear error messages for troubleshooting
