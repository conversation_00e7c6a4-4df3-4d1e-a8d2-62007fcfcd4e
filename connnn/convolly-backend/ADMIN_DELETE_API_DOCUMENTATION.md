# Admin Delete API Documentation

## Overview

The admin controller now includes comprehensive delete functionality for both tutors and students. These endpoints provide safe deletion with proper cleanup of related data including subscriptions, escrow transactions, withdrawal requests, and lessons.

## Tutor Delete Endpoint

### **DELETE** `/api/admin/tutors/:tutorId`

Safely deletes a tutor account with comprehensive cleanup of related data.

#### **Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_TOKEN",
  "Content-Type": "application/json"
}
```

#### **Request Body:**
```json
{
  "deleteReason": "Violation of terms of service",
  "transferSubscriptions": true
}
```

#### **Parameters:**
- **deleteReason** (required): Reason for deletion (for audit purposes)
- **transferSubscriptions** (optional, default: false): Whether to handle active subscriptions

#### **What happens during tutor deletion:**

1. **Subscription Handling:**
   - If `transferSubscriptions: true`: Cancels all active subscriptions and creates refund transactions
   - If `transferSubscriptions: false` and active subscriptions exist: Returns error

2. **Withdrawal Requests:**
   - Rejects all pending withdrawal requests
   - Sets rejection reason to deletion reason

3. **Escrow Transactions:**
   - Refunds all held escrow amounts to students
   - Creates refund transaction records

4. **Account Cleanup:**
   - Soft deletes the tutor (marks as deleted, doesn't remove from database)
   - Modifies email to prevent conflicts
   - Records deletion metadata

#### **Response:**
```json
{
  "success": true,
  "message": "Tutor deleted successfully",
  "data": {
    "tutorId": "64f8a1b2c3d4e5f6a7b8c9d0",
    "activeSubscriptionsCancelled": 3,
    "escrowTransactionsRefunded": 2,
    "deletionReason": "Violation of terms of service"
  }
}
```

#### **Error Responses:**

**400 Bad Request - Active Subscriptions:**
```json
{
  "message": "Cannot delete tutor with 3 active subscription(s). Set transferSubscriptions to true to handle them."
}
```

**400 Bad Request - Missing Reason:**
```json
{
  "message": "Deletion reason is required"
}
```

**404 Not Found:**
```json
{
  "message": "Tutor not found"
}
```

## Student Delete Endpoint

### **DELETE** `/api/admin/students/:studentId`

Safely deletes a student account with comprehensive cleanup of related data.

#### **Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_TOKEN",
  "Content-Type": "application/json"
}
```

#### **Request Body:**
```json
{
  "deleteReason": "Account closure requested by user",
  "refundSubscriptions": true
}
```

#### **Parameters:**
- **deleteReason** (required): Reason for deletion (for audit purposes)
- **refundSubscriptions** (optional, default: true): Whether to process prorated refunds

#### **What happens during student deletion:**

1. **Subscription Handling:**
   - Cancels all active, paused, and incomplete subscriptions
   - If `refundSubscriptions: true`: Calculates prorated refunds for active subscriptions
   - Creates refund transaction records for remaining subscription periods

2. **Escrow Transactions:**
   - Refunds all held escrow amounts back to student
   - Creates refund transaction records

3. **Lesson Management:**
   - Cancels all scheduled and confirmed lessons
   - Records cancellation reason and metadata

4. **Account Cleanup:**
   - Soft deletes the student (marks as deleted, doesn't remove from database)
   - Modifies email to prevent conflicts
   - Records deletion metadata

#### **Response:**
```json
{
  "success": true,
  "message": "Student deleted successfully",
  "data": {
    "studentId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "subscriptionsCancelled": 2,
    "escrowTransactionsRefunded": 1,
    "refundSubscriptions": true,
    "deletionReason": "Account closure requested by user"
  }
}
```

#### **Error Responses:**

**400 Bad Request - Missing Reason:**
```json
{
  "message": "Deletion reason is required"
}
```

**404 Not Found:**
```json
{
  "message": "Student not found"
}
```

## Postman Testing Examples

### Delete Tutor (with subscription handling)
```json
{
  "deleteReason": "Repeated policy violations",
  "transferSubscriptions": true
}
```

### Delete Tutor (without subscription handling)
```json
{
  "deleteReason": "Account verification failed"
}
```

### Delete Student (with refunds)
```json
{
  "deleteReason": "User requested account deletion",
  "refundSubscriptions": true
}
```

### Delete Student (without refunds)
```json
{
  "deleteReason": "Fraudulent activity detected",
  "refundSubscriptions": false
}
```

## Safety Features

### **Soft Delete**
- Accounts are marked as deleted rather than permanently removed
- Original data is preserved for audit purposes
- Email addresses are modified to prevent conflicts

### **Comprehensive Cleanup**
- All related financial transactions are handled
- Pending operations are cancelled or completed appropriately
- Audit trail is maintained for all actions

### **Error Prevention**
- Validates all required parameters
- Checks for active subscriptions before deletion
- Provides clear error messages for troubleshooting

### **Transaction Safety**
- All database operations are wrapped in try-catch blocks
- Refund transactions are created for proper financial tracking
- Escrow amounts are properly handled

## Admin Logging

All delete operations are automatically logged with:
- Admin user who performed the action
- Timestamp of the action
- Reason for deletion
- Number of affected records
- Success/failure status

## Best Practices

1. **Always provide a clear deletion reason** for audit purposes
2. **Review active subscriptions** before deleting tutors
3. **Consider refund implications** when deleting students
4. **Monitor transaction logs** after deletions
5. **Verify escrow balances** are properly handled

## Related Endpoints

- `GET /api/admin/tutors/:tutorId` - Get tutor details before deletion
- `GET /api/admin/students/:studentId` - Get student details before deletion
- `GET /api/admin/financial/transactions` - Monitor refund transactions
- `GET /api/admin/financial/escrow` - Monitor escrow status

## Security Notes

- Requires admin authentication with appropriate permissions
- All actions are logged for audit purposes
- Soft delete preserves data for compliance requirements
- Financial transactions are handled securely
