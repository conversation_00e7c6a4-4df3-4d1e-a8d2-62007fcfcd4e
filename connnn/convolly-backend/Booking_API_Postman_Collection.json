{"info": {"name": "Booking API Collection", "description": "Complete collection for testing booking endpoints including tutor availability and enhanced booking creation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000", "type": "string"}, {"key": "authToken", "value": "your-jwt-token-here", "type": "string"}, {"key": "tutorId", "value": "507f1f77bcf86cd799439011", "type": "string"}, {"key": "studentId", "value": "507f1f77bcf86cd799439012", "type": "string"}], "item": [{"name": "1. Get Tutor Availability", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/bookings/tutor-availability/{{tutorId}}?duration=60&startDate=2024-01-15T00:00:00.000Z&endDate=2024-01-22T23:59:59.999Z&timezone=UTC", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "tutor-availability", "{{tutorId}}"], "query": [{"key": "duration", "value": "60", "description": "Session duration in minutes"}, {"key": "startDate", "value": "2024-01-15T00:00:00.000Z", "description": "Start date for availability search"}, {"key": "endDate", "value": "2024-01-22T23:59:59.999Z", "description": "End date for availability search"}, {"key": "timezone", "value": "UTC", "description": "Timezone for formatting"}]}, "description": "Get available time slots for a tutor. Students only."}}, {"name": "2. Create Enhanced Booking", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutorId}}\",\n  \"title\": \"Mathematics Tutoring Session\",\n  \"description\": \"Advanced calculus lesson covering derivatives and integrals\",\n  \"startDateTime\": \"2024-01-15T10:00:00.000Z\",\n  \"endDateTime\": \"2024-01-15T11:00:00.000Z\",\n  \"subject\": \"Mathematics\"\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/enhanced", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Create a new booking session. Creates events in both tutor and student calendars."}}, {"name": "3. Create Booking with Student ID", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutorId}}\",\n  \"studentId\": \"{{studentId}}\",\n  \"title\": \"Physics Tutoring Session\",\n  \"description\": \"Quantum mechanics fundamentals\",\n  \"startDateTime\": \"2024-01-16T14:00:00.000Z\",\n  \"endDateTime\": \"2024-01-16T15:30:00.000Z\",\n  \"subject\": \"Physics\"\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/enhanced", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Create booking with specific student ID (admin/tutor use case)."}}, {"name": "4. Get Student Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/bookings/student?status=scheduled&page=1&limit=10&sortBy=scheduledTime&sortOrder=desc", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "student"], "query": [{"key": "status", "value": "scheduled", "description": "Filter by booking status"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "sortBy", "value": "scheduledTime", "description": "Sort field"}, {"key": "sortOrder", "value": "desc", "description": "Sort order"}]}, "description": "Get bookings for authenticated student."}}, {"name": "5. <PERSON> Tutor Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/bookings/tutor-detailed?status=confirmed&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "tutor-detailed"], "query": [{"key": "status", "value": "confirmed", "description": "Filter by booking status"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}]}, "description": "Get bookings for authenticated tutor."}}, {"name": "6. Get Upcoming Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/bookings/upcoming?limit=5", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "upcoming"], "query": [{"key": "limit", "value": "5", "description": "Number of upcoming bookings to return"}]}, "description": "Get upcoming bookings for authenticated user (student or tutor)."}}, {"name": "7. Legacy Create Booking", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"eventId\": \"507f1f77bcf86cd799439013\"\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings", "host": ["{{baseUrl}}"], "path": ["api", "bookings"]}, "description": "Legacy booking creation (for backward compatibility)."}}, {"name": "8. Update Booking Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/507f1f77bcf86cd799439014/status", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "507f1f77bcf86cd799439014", "status"]}, "description": "Update booking status (tutor only). Status: pending, confirmed, cancelled."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate future dates for testing", "const now = new Date();", "const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);", "const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 1000);", "", "// Set dynamic dates", "pm.collectionVariables.set('startDate', tomorrow.toISOString());", "pm.collectionVariables.set('endDate', nextWeek.toISOString());", "", "// Generate session times (tomorrow at 10 AM and 11 AM)", "const sessionStart = new Date(tomorrow);", "sessionStart.setHours(10, 0, 0, 0);", "const sessionEnd = new Date(tomorrow);", "sessionEnd.setHours(11, 0, 0, 0);", "", "pm.collectionVariables.set('sessionStart', sessionStart.toISOString());", "pm.collectionVariables.set('sessionEnd', sessionEnd.toISOString());"]}}]}