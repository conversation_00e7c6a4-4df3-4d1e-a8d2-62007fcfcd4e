# Tutor Availability API Documentation

## Overview
This API allows students to view tutor availability by checking their calendar schedules and finding available time slots for booking sessions.

## Endpoint

### Get Tutor Availability
**GET** `/api/bookings/tutor-availability/:tutorId`

Retrieves available time slots for a specific tutor based on their calendar events.

#### Authentication
- **Required**: Yes
- **Role**: Student only

#### Parameters

##### Path Parameters
- `tutorId` (string, required): The MongoDB ObjectId of the tutor

##### Query Parameters
- `startDate` (string, optional): Start date for availability search (ISO 8601 format)
  - Default: Current date/time
  - Example: `2024-01-15T00:00:00.000Z`

- `endDate` (string, optional): End date for availability search (ISO 8601 format)
  - Default: 7 days from current date
  - Example: `2024-01-22T23:59:59.999Z`

- `duration` (number, optional): Session duration in minutes
  - Default: 60
  - Example: `90`

- `timezone` (string, optional): Timezone for time formatting
  - Default: 'UTC'
  - Example: 'America/New_York'

#### Example Request
```bash
GET /api/bookings/tutor-availability/507f1f77bcf86cd799439011?startDate=2024-01-15T00:00:00.000Z&endDate=2024-01-22T23:59:59.999Z&duration=60&timezone=UTC
Authorization: Bearer <your-jwt-token>
```

#### Response Format

##### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "tutor": {
      "id": "507f1f77bcf86cd799439011",
      "name": "John Smith",
      "email": "<EMAIL>",
      "image": "https://example.com/avatar.jpg",
      "subjects": [
        {
          "name": "Mathematics",
          "level": "Advanced"
        }
      ],
      "basePrice": 25
    },
    "searchPeriod": {
      "startDate": "2024-01-15T00:00:00.000Z",
      "endDate": "2024-01-22T23:59:59.999Z",
      "duration": 60,
      "timezone": "UTC"
    },
    "calendars": [
      {
        "id": "507f1f77bcf86cd799439012",
        "name": "Teaching Schedule",
        "description": "Main teaching calendar",
        "color": "#4CAF50"
      }
    ],
    "bookedSlots": [
      {
        "id": "507f1f77bcf86cd799439013",
        "title": "Math Session - Alice Johnson",
        "startDateTime": "2024-01-15T10:00:00.000Z",
        "endDateTime": "2024-01-15T11:00:00.000Z",
        "status": "confirmed"
      }
    ],
    "availableSlots": [
      {
        "startDateTime": "2024-01-15T09:00:00.000Z",
        "endDateTime": "2024-01-15T10:00:00.000Z",
        "duration": 60,
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedTime": "09:00 AM",
        "timezone": "UTC"
      },
      {
        "startDateTime": "2024-01-15T11:00:00.000Z",
        "endDateTime": "2024-01-15T12:00:00.000Z",
        "duration": 60,
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedTime": "11:00 AM",
        "timezone": "UTC"
      }
    ],
    "availabilityByDate": {
      "2024-01-15": [
        {
          "startDateTime": "2024-01-15T09:00:00.000Z",
          "endDateTime": "2024-01-15T10:00:00.000Z",
          "duration": 60,
          "dayOfWeek": "Monday",
          "formattedDate": "January 15, 2024",
          "formattedTime": "09:00 AM",
          "timezone": "UTC"
        }
      ],
      "2024-01-16": [
        {
          "startDateTime": "2024-01-16T14:00:00.000Z",
          "endDateTime": "2024-01-16T15:00:00.000Z",
          "duration": 60,
          "dayOfWeek": "Tuesday",
          "formattedDate": "January 16, 2024",
          "formattedTime": "02:00 PM",
          "timezone": "UTC"
        }
      ]
    },
    "summary": {
      "totalAvailableSlots": 15,
      "daysWithAvailability": 5,
      "nextAvailableSlot": {
        "startDateTime": "2024-01-15T09:00:00.000Z",
        "endDateTime": "2024-01-15T10:00:00.000Z",
        "duration": 60,
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedTime": "09:00 AM",
        "timezone": "UTC"
      }
    }
  }
}
```

##### Error Responses

**401 Unauthorized**
```json
{
  "success": false,
  "message": "Authentication required"
}
```

**403 Forbidden**
```json
{
  "success": false,
  "message": "Only students can view tutor availability"
}
```

**400 Bad Request**
```json
{
  "success": false,
  "message": "Invalid tutor ID format"
}
```

**404 Not Found**
```json
{
  "success": false,
  "message": "Tutor not found"
}
```

**404 Not Found (No Calendars)**
```json
{
  "success": false,
  "message": "No shared calendars found for this tutor"
}
```

**500 Internal Server Error**
```json
{
  "success": false,
  "message": "Failed to fetch tutor availability",
  "error": "Detailed error message"
}
```

## Business Logic

### Working Hours
- **Default Hours**: 9:00 AM to 6:00 PM
- **Days**: Monday to Friday (weekends are excluded)
- **Time Slots**: Generated in 30-minute intervals

### Availability Calculation
1. **Shared Calendars Only**: Only calendars marked as `isShared: true` are considered
2. **Conflict Detection**: Time slots that overlap with existing events are excluded
3. **Future Slots Only**: Past time slots are automatically filtered out
4. **Minimum Duration**: Slots must accommodate the requested session duration

### Time Slot Generation
- Slots are generated in 30-minute intervals
- Each slot must fit the requested duration
- Conflicts with existing bookings are automatically excluded
- Only future time slots are included

## Usage Examples

### Basic Availability Check
```javascript
// Get next 7 days of availability for 1-hour sessions
const response = await fetch('/api/bookings/tutor-availability/507f1f77bcf86cd799439011', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

### Custom Date Range
```javascript
// Get availability for specific date range
const startDate = '2024-01-15T00:00:00.000Z';
const endDate = '2024-01-20T23:59:59.999Z';
const response = await fetch(`/api/bookings/tutor-availability/507f1f77bcf86cd799439011?startDate=${startDate}&endDate=${endDate}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

### Custom Session Duration
```javascript
// Get availability for 90-minute sessions
const response = await fetch('/api/bookings/tutor-availability/507f1f77bcf86cd799439011?duration=90', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

## Integration Notes

1. **Frontend Integration**: Use the `availabilityByDate` object to easily display availability in a calendar view
2. **Booking Integration**: Use the `startDateTime` and `endDateTime` from available slots when calling the enhanced booking API
3. **Real-time Updates**: Consider implementing polling or WebSocket connections for real-time availability updates
4. **Timezone Handling**: Always handle timezone conversions on the frontend for user's local time display

## Related APIs

- **Enhanced Booking**: `POST /api/bookings/enhanced` - Use available slots to create bookings
- **Student Bookings**: `GET /api/bookings/student` - View existing bookings
- **Upcoming Bookings**: `GET /api/bookings/upcoming` - View upcoming sessions
