# Session Completion API Documentation

## Overview

The session completion system automatically processes completed tutoring sessions to:
- Decrease student's `remainingLessons` by 1
- Increase tutor's earnings by adding `pricePerClass` to their `availableBalance`
- Create proper transaction and escrow records
- Update lesson status and completion tracking

## Automatic Processing

### **Scheduled Job**
- Runs every 30 minutes automatically
- Processes all sessions where `endDateTime` has passed
- Only processes sessions with status `'confirmed'` that haven't been processed yet

### **Conditions for Processing**
1. Session end time has passed
2. Session status is `'confirmed'` (not cancelled or tentative)
3. Session hasn't been processed yet (`processed: false`)
4. Valid tutor and student can be identified from the session

## Admin Endpoints

### 1. Process All Completed Sessions
**POST** `/api/admin/sessions/process-all`

Manually trigger processing of all completed sessions.

#### **Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_TOKEN"
}
```

#### **Response:**
```json
{
  "success": true,
  "message": "Processed 5 sessions successfully",
  "data": {
    "lessonsProcessed": 5,
    "earningsUpdated": 5,
    "errors": []
  }
}
```

### 2. Process Specific Session
**POST** `/api/admin/sessions/process/:eventId`

Manually process a specific session by event ID.

#### **Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_TOKEN"
}
```

#### **Response:**
```json
{
  "success": true,
  "message": "Session processed successfully",
  "data": {
    "lessonsProcessed": 1,
    "earningsUpdated": 1,
    "errors": []
  }
}
```

### 3. Get Session Completion Statistics
**GET** `/api/admin/sessions/stats`

Get comprehensive statistics about session completion processing.

#### **Response:**
```json
{
  "success": true,
  "data": {
    "totalCompletedEvents": 150,
    "unprocessedEvents": 3,
    "completedLessons": {
      "today": 12,
      "thisWeek": 45,
      "thisMonth": 180
    },
    "pendingProcessing": {
      "count": 3,
      "sessions": [
        {
          "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
          "title": "Math Lesson - John Doe",
          "startDateTime": "2024-01-20T14:00:00.000Z",
          "endDateTime": "2024-01-20T15:00:00.000Z"
        }
      ]
    }
  }
}
```

### 4. Health Check
**GET** `/api/admin/sessions/health`

Check the health of the session processing service.

#### **Response:**
```json
{
  "success": true,
  "message": "Session processing service is healthy",
  "data": {
    "overdueSessionsCount": 2,
    "threshold": 10,
    "isHealthy": true
  }
}
```

## What Happens During Session Processing

### **For Each Completed Session:**

1. **Identify Participants**
   - Extract tutor ID from calendar ownership
   - Extract student ID from event description
   - Determine if it's a free trial or paid lesson

2. **Create/Update Lesson Record**
   - Create lesson record with status `'completed'`
   - Link to subscription (if paid lesson)
   - Record session duration and details

3. **Update Student Subscription**
   - Decrease `remainingLessons` by 1 (only for paid lessons)
   - Update subscription record
   - Skip for free trial lessons

4. **Update Tutor Earnings**
   - Calculate earnings: `basePrice * (1 - platformFeeRate)`
   - Add earnings to tutor's `availableBalance`
   - Increment tutor's `totalLessons` count
   - Create transaction record

5. **Create Financial Records**
   - Create transaction record for tutor earnings
   - Create escrow record (marked as released)
   - Track platform fees

6. **Update Tracking**
   - Mark event as `processed: true`
   - Update student's `totalLessons` count
   - Record completion timestamp

## Financial Calculations

### **Platform Fee Structure**
- Platform fee: 20% of lesson price
- Tutor earnings: 80% of lesson price

### **Example Calculation**
```
Lesson Price: $50.00
Platform Fee: $10.00 (20%)
Tutor Earnings: $40.00 (80%)
```

### **Currency Handling**
- All amounts stored in cents (multiply by 100)
- Display amounts divided by 100

## Booking Integration

### **Enhanced Booking**
When sessions are booked via `/api/bookings/enhanced`, the system:
1. Creates events in both tutor and student calendars
2. Creates lesson record with status `'scheduled'`
3. Links lesson to subscription (if paid)
4. Tracks free trial vs paid lesson status

### **Schedule Booking**
When sessions are booked via `/api/schedule/book-session`, the system:
1. Creates events in available calendars
2. Creates lesson record for tracking
3. Handles subscription validation
4. Processes lesson consumption

## Error Handling

### **Common Errors**
- **Participant Identification Failed**: Cannot determine tutor or student from event
- **Subscription Not Found**: Active subscription not found for paid lesson
- **Already Processed**: Session has already been processed
- **Invalid Session Status**: Session is cancelled or tentative

### **Error Recovery**
- Errors are logged but don't stop processing of other sessions
- Failed sessions can be manually reprocessed
- Detailed error messages for troubleshooting

## Monitoring and Maintenance

### **Health Monitoring**
- Check `/api/admin/sessions/health` regularly
- Monitor for sessions overdue for processing (>1 hour)
- Alert if more than 10 sessions are overdue

### **Manual Intervention**
- Use `/api/admin/sessions/process-all` for bulk processing
- Use `/api/admin/sessions/process/:eventId` for specific sessions
- Check `/api/admin/sessions/stats` for processing statistics

## Database Schema Updates

### **Event Model**
Added `processed` field:
```typescript
processed?: boolean; // Track if session completion has been processed
```

### **Student Model**
Added `totalLessons` field:
```typescript
totalLessons?: number; // Track total completed lessons
```

## Postman Testing Examples

### Process All Sessions
```
POST /api/admin/sessions/process-all
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN"
}
```

### Process Specific Session
```
POST /api/admin/sessions/process/64f8a1b2c3d4e5f6a7b8c9d0
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN"
}
```

### Get Statistics
```
GET /api/admin/sessions/stats
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN"
}
```

### Health Check
```
GET /api/admin/sessions/health
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN"
}
```

## Scheduled Job Configuration

### **Cron Schedule**
- Frequency: Every 30 minutes
- Pattern: `*/30 * * * *`
- Timezone: UTC

### **Job Management**
```typescript
// Start the job
import { startSessionProcessingJob } from './jobs/sessionProcessingJob';
startSessionProcessingJob();

// Manual trigger (for testing)
import { triggerSessionProcessing } from './jobs/sessionProcessingJob';
await triggerSessionProcessing();

// Stop the job (graceful shutdown)
import { stopSessionProcessingJob } from './jobs/sessionProcessingJob';
stopSessionProcessingJob();
```

## Security and Permissions

- All endpoints require admin authentication
- Session processing is logged for audit purposes
- Financial transactions are tracked and recorded
- Escrow records maintain transaction history

## Best Practices

1. **Monitor regularly** using health check endpoint
2. **Review statistics** to ensure processing is working
3. **Handle errors promptly** using manual processing endpoints
4. **Backup data** before bulk processing operations
5. **Test thoroughly** in staging environment before production deployment
