import express from "express";
import cors from "cors";
import helmet from "helmet";
import rateLimiter from "./middlewares/rateLimiter";
import mongoSanitize from "express-mongo-sanitize";
import xss from "xss-clean";
import errorHandler from "./middlewares/errorHandler";
import authRoutes from "./routes/auth";
import { CLIENT_ORIGIN, IS_PROD, SERVER_ORIGIN } from "./config/constants";
import { patchReadonlyExpressFields } from "./middlewares/patch";
import { handle404 } from "./middlewares/misc";
import classRoomRouter from "./routes/classroom";
import tutorRouter from "./routes/tutor";
import { handleStripeWebhook } from "../src/webhook/stripe_webhook";
import calendarRoutes from "./routes/calendarRoutes";
import eventRoutes from "./routes/eventRoutes";
import bookingRoutes from "./routes/bookingRoutes";
import scheduleRoutes from "./routes/scheduleRoutes";
import withdrawalRoutes from "./routes/withdrawalRoutes";
import subscriptionStatusRoutes from "./routes/subscriptionStatusRoutes";
import profileRouter from "./routes/profile";
import miscRouter from "./routes/misc";
import adminRouter from "./routes/adminRouter";

import Subscription from "./routes/Subscriptions";

import { CORS_CONFIG } from "./config/misc";
import analyticsRouter from "./routes/analytics";
import chatRouter from "./routes/chat";
import reviewRouter from "./routes/review";
import paymentMethodRouter from "./routes/paymentMethodRoutes";
import notificationSettingsRouter from "./routes/notificationSettingsRoutes";
import profileImageRouter from "./routes/profileImageRoutes";

const app = express();

// Middleware Setup

// allow for header forwarding from reverse proxies
app.set("trust proxy", IS_PROD ? true : "loopback");

app.use(helmet()); // Protects headers
app.use(cors(CORS_CONFIG)); // Cross-origin
app.use(rateLimiter); // Protect from brute force
app.use(express.json({ limit: "10mb" })); // Body parser
app.use(patchReadonlyExpressFields(mongoSanitize())); // No $query injections
app.use(patchReadonlyExpressFields(xss())); // Clean inputs

// Serve static files for uploaded images
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Routes

app.use("/api/profile", profileRouter);
app.use("/api/classroom", classRoomRouter);
app.use("/api/auth", authRoutes);
app.use("/api/tutor", tutorRouter);
app.use("/api/admin", adminRouter);

app.use("/api/calendars", calendarRoutes);
app.use("/api/events", eventRoutes);
app.use("/api/bookings", bookingRoutes);
app.use("/api/schedule", scheduleRoutes);
app.use("/api/withdrawals", withdrawalRoutes);
app.use("/api/subscription-status", subscriptionStatusRoutes);
app.use("/api/subscription", Subscription);
app.use("/api/reviews", reviewRouter);
app.use("/api/chat", chatRouter);
app.use("/api/payment-methods", paymentMethodRouter);
app.use("/api/notification-settings", notificationSettingsRouter);
app.use("/api/profile-image", profileImageRouter);

app.post(
  "/webhook/stripe",
  express.raw({ type: "application/json" }),
  handleStripeWebhook
);

app.use("/api/analytics", analyticsRouter);
app.use("/api", miscRouter);

app.use(handle404);

// Error Handling

app.use(errorHandler);

export default app;
