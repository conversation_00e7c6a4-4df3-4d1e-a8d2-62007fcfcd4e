// models/Admin.ts
import mongoose, { Schema, Document, Types } from "mongoose";
import { IProfile, ProfileSchema } from "./profile";

// Admin permission levels
export enum AdminPermission {
  // User Management
  MANAGE_USERS = "manage_users",
  VIEW_USERS = "view_users",
  SUSPEND_USERS = "suspend_users",
  DELETE_USERS = "delete_users",

  // Tutor Management
  APPROVE_TUTORS = "approve_tutors",
  REJECT_TUTORS = "reject_tutors",
  MANAGE_TUTORS = "manage_tutors",
  VIEW_TUTOR_DETAILS = "view_tutor_details",

  // Content Moderation
  MODERATE_CONTENT = "moderate_content",
  FLAG_CONTENT = "flag_content",
  REMOVE_CONTENT = "remove_content",

  // Financial Management
  MANAGE_PAYMENTS = "manage_payments",
  VIEW_FINANCIAL_REPORTS = "view_financial_reports",
  PROCESS_REFUNDS = "process_refunds",
  <PERSON><PERSON>GE_SUBSCRIPTIONS = "manage_subscriptions",

  // System Administration
  SYSTEM_SETTINGS = "system_settings",
  VIEW_ANALYTICS = "view_analytics",
  MANAGE_ADMINS = "manage_admins",
  AUDIT_LOGS = "audit_logs",

  // Support & Communication
  MANAGE_SUPPORT = "manage_support",
  SEND_NOTIFICATIONS = "send_notifications",
  MANAGE_ANNOUNCEMENTS = "manage_announcements",

  // Super Admin (all permissions)
  SUPER_ADMIN = "super_admin"
}

export interface IAdminActivity {
  action: string;
  targetType: 'user' | 'tutor' | 'student' | 'subscription' | 'lesson' | 'system';
  targetId?: Types.ObjectId;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export interface IAdminNotification {
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  isRead: boolean;
  createdAt: Date;
  actionUrl?: string;
}

export interface IAdminSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  emailNotifications: {
    newTutorApplications: boolean;
    flaggedContent: boolean;
    systemAlerts: boolean;
    financialReports: boolean;
  };
  dashboardPreferences: {
    defaultView: string;
    widgetOrder: string[];
    autoRefresh: boolean;
    refreshInterval: number; // in seconds
  };
}

export interface IAdmin extends Document, IProfile {
  role: "admin";
  permissions: AdminPermission[];
  isSuperAdmin: boolean;
  adminLevel: 1 | 2 | 3 | 4 | 5; // 1 = lowest, 5 = highest
  department?: string;
  employeeId?: string;

  // Activity tracking
  lastActiveAt: Date;
  activityLog: IAdminActivity[];

  // Notifications
  notifications: IAdminNotification[];
  unreadNotificationCount: number;

  // Settings & Preferences
  settings: IAdminSettings;

  // Security
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  loginAttempts: number;
  lockedUntil?: Date;
  passwordChangedAt: Date;

  // Audit trail
  createdBy?: Types.ObjectId;
  modifiedBy?: Types.ObjectId;
  modifiedAt?: Date;

  // Status
  isActive: boolean;
  suspendedAt?: Date;
  suspendedBy?: Types.ObjectId;
  suspensionReason?: string;

  // Methods
  hasPermission(permission: AdminPermission): boolean;
  addActivity(activity: Omit<IAdminActivity, 'timestamp'>): void;
  markNotificationAsRead(notificationId: string): void;
  addNotification(notification: Omit<IAdminNotification, 'isRead' | 'createdAt'>): void;
}

// Activity Log Schema
const AdminActivitySchema = new Schema<IAdminActivity>({
  action: { type: String, required: true },
  targetType: {
    type: String,
    enum: ['user', 'tutor', 'student', 'subscription', 'lesson', 'system'],
    required: true
  },
  targetId: { type: Schema.Types.ObjectId },
  details: { type: Schema.Types.Mixed, required: true },
  ipAddress: { type: String },
  userAgent: { type: String },
  timestamp: { type: Date, default: Date.now }
}, { _id: true });

// Notification Schema
const AdminNotificationSchema = new Schema<IAdminNotification>({
  title: { type: String, required: true },
  message: { type: String, required: true },
  type: {
    type: String,
    enum: ['info', 'warning', 'error', 'success'],
    default: 'info'
  },
  isRead: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  actionUrl: { type: String }
}, { _id: true });

// Settings Schema
const AdminSettingsSchema = new Schema<IAdminSettings>({
  theme: {
    type: String,
    enum: ['light', 'dark', 'auto'],
    default: 'light'
  },
  language: { type: String, default: 'en' },
  timezone: { type: String, default: 'UTC' },
  emailNotifications: {
    newTutorApplications: { type: Boolean, default: true },
    flaggedContent: { type: Boolean, default: true },
    systemAlerts: { type: Boolean, default: true },
    financialReports: { type: Boolean, default: false }
  },
  dashboardPreferences: {
    defaultView: { type: String, default: 'overview' },
    widgetOrder: [{ type: String }],
    autoRefresh: { type: Boolean, default: false },
    refreshInterval: { type: Number, default: 300 } // 5 minutes
  }
}, { _id: false });

// Main Admin Schema
const AdminSchema: Schema = new Schema({
  permissions: [{
    type: String,
    enum: Object.values(AdminPermission),
    default: []
  }],
  isSuperAdmin: { type: Boolean, default: false },
  adminLevel: {
    type: Number,
    enum: [1, 2, 3, 4, 5],
    default: 1
  },
  department: { type: String },
  employeeId: { type: String, unique: true, sparse: true },

  // Activity tracking
  lastActiveAt: { type: Date, default: Date.now },
  activityLog: [AdminActivitySchema],

  // Notifications
  notifications: [AdminNotificationSchema],
  unreadNotificationCount: { type: Number, default: 0 },

  // Settings
  settings: { type: AdminSettingsSchema, default: () => ({}) },

  // Security
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorSecret: { type: String },
  loginAttempts: { type: Number, default: 0 },
  lockedUntil: { type: Date },
  passwordChangedAt: { type: Date, default: Date.now },

  // Audit trail
  createdBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
  modifiedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
  modifiedAt: { type: Date },

  // Status
  isActive: { type: Boolean, default: true },
  suspendedAt: { type: Date },
  suspendedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
  suspensionReason: { type: String }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add profile schema
AdminSchema.add(ProfileSchema);

// Instance Methods
AdminSchema.methods.hasPermission = function(permission: AdminPermission): boolean {
  if (this.isSuperAdmin) return true;
  return this.permissions.includes(permission) || this.permissions.includes(AdminPermission.SUPER_ADMIN);
};

AdminSchema.methods.addActivity = function(activity: Omit<IAdminActivity, 'timestamp'>): void {
  this.activityLog.push({
    ...activity,
    timestamp: new Date()
  });

  // Keep only last 1000 activities
  if (this.activityLog.length > 1000) {
    this.activityLog = this.activityLog.slice(-1000);
  }

  this.lastActiveAt = new Date();
};

AdminSchema.methods.addNotification = function(notification: Omit<IAdminNotification, 'isRead' | 'createdAt'>): void {
  this.notifications.push({
    ...notification,
    isRead: false,
    createdAt: new Date()
  });

  this.unreadNotificationCount += 1;

  // Keep only last 100 notifications
  if (this.notifications.length > 100) {
    this.notifications = this.notifications.slice(-100);
  }
};

AdminSchema.methods.markNotificationAsRead = function(notificationId: string): void {
  const notification = this.notifications.id(notificationId);
  if (notification && !notification.isRead) {
    notification.isRead = true;
    this.unreadNotificationCount = Math.max(0, this.unreadNotificationCount - 1);
  }
};

// Static Methods
AdminSchema.statics.getPermissionsByLevel = function(level: number): AdminPermission[] {
  const permissionsByLevel: { [key: number]: AdminPermission[] } = {
    1: [AdminPermission.VIEW_USERS, AdminPermission.VIEW_TUTOR_DETAILS, AdminPermission.VIEW_ANALYTICS],
    2: [AdminPermission.MANAGE_USERS, AdminPermission.APPROVE_TUTORS, AdminPermission.FLAG_CONTENT],
    3: [AdminPermission.SUSPEND_USERS, AdminPermission.MODERATE_CONTENT, AdminPermission.MANAGE_SUPPORT],
    4: [AdminPermission.MANAGE_PAYMENTS, AdminPermission.PROCESS_REFUNDS, AdminPermission.MANAGE_SUBSCRIPTIONS],
    5: [AdminPermission.SUPER_ADMIN]
  };

  let permissions: AdminPermission[] = [];
  for (let i = 1; i <= level; i++) {
    permissions = [...permissions, ...(permissionsByLevel[i] || [])];
  }

  return Array.from(new Set(permissions)); // Remove duplicates
};

// Indexes
AdminSchema.index({ email: 1 });
AdminSchema.index({ employeeId: 1 });
AdminSchema.index({ isActive: 1 });
AdminSchema.index({ adminLevel: 1 });
AdminSchema.index({ 'activityLog.timestamp': -1 });

const Admin = mongoose.model<IAdmin>("Admin", AdminSchema);

export default Admin;
