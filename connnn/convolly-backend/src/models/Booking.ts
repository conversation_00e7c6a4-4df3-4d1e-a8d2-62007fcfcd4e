import { Schema, model, Document, Types } from 'mongoose';

export interface IBooking extends Document {
  eventId: Types.ObjectId;       // The tutor's event/session
  learnerId: Types.ObjectId;     // The learner booking the session
  status: 'pending' | 'confirmed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

const bookingSchema = new Schema<IBooking>({
  eventId: { type: Schema.Types.ObjectId, ref: 'Event', required: true },
  learnerId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  status: { type: String, enum: ['pending', 'confirmed', 'cancelled'], default: 'pending' },
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

export const Booking = model<IBooking>('Booking', bookingSchema);
