import mongoose, { Document, Schema } from "mongoose";

export interface Lesson extends Document {
  tutorId: mongoose.Types.ObjectId;
  studentId: mongoose.Types.ObjectId;
  subscriptionId?: mongoose.Types.ObjectId; // Optional for free trial lessons
  title?: string; // Lesson title/subject
  scheduledTime: Date;
  duration: number; // in minutes
  status: "scheduled" | "completed" | "cancelled" | "no-show" | "confirmed";
  isFreeTrial: boolean; // Track if this is a free trial lesson
  confirmedAt?: Date;
  cancelledAt?: Date;
  cancellationReason?: string;
  notes?: string;
  createdAt: Date;
}

const lessonSchema = new Schema<Lesson>({
  tutorId: { type: Schema.Types.ObjectId, ref: "Tutor", required: true },
  studentId: { type: Schema.Types.ObjectId, ref: "Student", required: true },
  subscriptionId: {
    type: Schema.Types.ObjectId,
    ref: "Subscription",
    required: false, // Not required for free trial lessons
  },
  title: { type: String }, // Lesson title/subject
  scheduledTime: { type: Date, required: true },
  duration: { type: Number, default: 50 }, // minutes
  status: {
    type: String,
    enum: ["scheduled", "completed", "cancelled", "no-show", "confirmed"],
    default: "scheduled",
  },
  isFreeTrial: { type: Boolean, default: false },
  confirmedAt: { type: Date },
  cancelledAt: { type: Date },
  cancellationReason: { type: String },
  notes: { type: String },
  createdAt: { type: Date, default: Date.now },
});

const LessonModel = mongoose.model<Lesson>("Lesson", lessonSchema);
export default LessonModel;
