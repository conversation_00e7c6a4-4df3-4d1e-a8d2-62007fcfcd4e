import mongoose, { Document, Schema } from 'mongoose';

export interface Transaction extends Document {
  userId: mongoose.Types.ObjectId;
  relatedUserId?: mongoose.Types.ObjectId; // For transactions involving two users (e.g., tutor and student)
  amount: number; // in cents
  currency: string;
  type: 'subscription_payment' | 'lesson_payment' | 'lesson_payout' | 'refund' | 'fee' | 'withdrawal' | 'platform_fee' | 'trial_conversion' | 'cancellation_fee' | 'bonus' | 'adjustment';
  category: 'payment' | 'payout' | 'refund' | 'fee' | 'adjustment';
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'processing';
  paymentMethod?: 'stripe' | 'paypal' | 'bank_transfer' | 'credit' | 'manual';

  // Related entities
  subscriptionId?: mongoose.Types.ObjectId;
  lessonId?: mongoose.Types.ObjectId;
  withdrawalRequestId?: mongoose.Types.ObjectId;
  refundRequestId?: mongoose.Types.ObjectId;

  // External payment provider details
  stripeTransactionId?: string;
  stripePaymentIntentId?: string;
  stripeInvoiceId?: string;
  stripeRefundId?: string;
  paypalTransactionId?: string;

  // Transaction metadata
  description?: string;
  adminNotes?: string;
  metadata?: Record<string, any>;

  // Fees and commissions
  platformFeeAmount?: number; // in cents
  processingFeeAmount?: number; // in cents
  netAmount?: number; // amount after fees, in cents

  // Timestamps
  processedAt?: Date;
  failedAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Methods
  markAsCompleted(processedAt?: Date): Promise<Transaction>;
  markAsFailed(reason?: string): Promise<Transaction>;
  addMetadata(key: string, value: any): Promise<Transaction>;
}

const transactionSchema = new Schema<Transaction>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  relatedUserId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'usd',
    enum: ['usd', 'eur', 'gbp', 'cad', 'aud']
  },
  type: {
    type: String,
    enum: [
      'subscription_payment',
      'lesson_payment',
      'lesson_payout',
      'refund',
      'fee',
      'withdrawal',
      'platform_fee',
      'trial_conversion',
      'cancellation_fee',
      'bonus',
      'adjustment'
    ],
    required: true,
    index: true
  },
  category: {
    type: String,
    enum: ['payment', 'payout', 'refund', 'fee', 'adjustment'],
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled', 'processing'],
    default: 'pending',
    index: true
  },
  paymentMethod: {
    type: String,
    enum: ['stripe', 'paypal', 'bank_transfer', 'credit', 'manual']
  },

  // Related entities
  subscriptionId: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription',
    index: true
  },
  lessonId: {
    type: Schema.Types.ObjectId,
    ref: 'Lesson',
    index: true
  },
  withdrawalRequestId: {
    type: Schema.Types.ObjectId,
    ref: 'WithdrawalRequest'
  },
  refundRequestId: {
    type: Schema.Types.ObjectId,
    ref: 'RefundRequest'
  },

  // External payment provider details
  stripeTransactionId: { type: String, index: true },
  stripePaymentIntentId: { type: String, index: true },
  stripeInvoiceId: { type: String },
  stripeRefundId: { type: String },
  paypalTransactionId: { type: String },

  // Transaction metadata
  description: { type: String },
  adminNotes: { type: String },
  metadata: { type: Schema.Types.Mixed },

  // Fees and commissions
  platformFeeAmount: { type: Number, min: 0 },
  processingFeeAmount: { type: Number, min: 0 },
  netAmount: { type: Number },

  // Timestamps
  processedAt: { type: Date },
  failedAt: { type: Date }
}, {
  timestamps: true
});

// Compound indexes for better query performance
transactionSchema.index({ userId: 1, type: 1 });
transactionSchema.index({ userId: 1, status: 1 });
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ type: 1, status: 1 });
transactionSchema.index({ category: 1, status: 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ subscriptionId: 1, type: 1 });
transactionSchema.index({ lessonId: 1, type: 1 });

// Virtual for related user details
transactionSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

transactionSchema.virtual('relatedUser', {
  ref: 'User',
  localField: 'relatedUserId',
  foreignField: '_id',
  justOne: true
});

// Virtual for related entities
transactionSchema.virtual('subscription', {
  ref: 'Subscription',
  localField: 'subscriptionId',
  foreignField: '_id',
  justOne: true
});

transactionSchema.virtual('lesson', {
  ref: 'Lesson',
  localField: 'lessonId',
  foreignField: '_id',
  justOne: true
});

transactionSchema.virtual('withdrawalRequest', {
  ref: 'WithdrawalRequest',
  localField: 'withdrawalRequestId',
  foreignField: '_id',
  justOne: true
});

transactionSchema.virtual('refundRequest', {
  ref: 'RefundRequest',
  localField: 'refundRequestId',
  foreignField: '_id',
  justOne: true
});

// Virtual for amount in dollars
transactionSchema.virtual('amountInDollars').get(function() {
  return this.amount / 100;
});

transactionSchema.virtual('netAmountInDollars').get(function() {
  return this.netAmount ? this.netAmount / 100 : (this.amount / 100);
});

// Instance methods
transactionSchema.methods.markAsCompleted = function(processedAt?: Date) {
  this.status = 'completed';
  this.processedAt = processedAt || new Date();
  return this.save();
};

transactionSchema.methods.markAsFailed = function(reason?: string) {
  this.status = 'failed';
  this.failedAt = new Date();
  if (reason) {
    this.adminNotes = `${this.adminNotes || ''}\nFailed: ${reason}`.trim();
  }
  return this.save();
};

transactionSchema.methods.addMetadata = function(key: string, value: any) {
  if (!this.metadata) {
    this.metadata = {};
  }
  this.metadata[key] = value;
  return this.save();
};

// Static methods
transactionSchema.statics.findByUser = function(userId: string, options: any = {}) {
  const query: any = { userId };
  if (options.type) query.type = options.type;
  if (options.status) query.status = options.status;
  if (options.category) query.category = options.category;

  return this.find(query)
    .populate('relatedUser', 'firstname lastname email')
    .populate('subscription', 'planType monthlyPrice')
    .populate('lesson', 'title scheduledAt')
    .sort({ createdAt: -1 });
};

transactionSchema.statics.findBySubscription = function(subscriptionId: string) {
  return this.find({ subscriptionId })
    .populate('userId', 'firstname lastname email')
    .sort({ createdAt: -1 });
};

transactionSchema.statics.findByLesson = function(lessonId: string) {
  return this.find({ lessonId })
    .populate('userId', 'firstname lastname email')
    .populate('relatedUserId', 'firstname lastname email')
    .sort({ createdAt: -1 });
};

transactionSchema.statics.getRevenueStats = function(startDate?: Date, endDate?: Date) {
  const matchStage: any = {
    status: 'completed',
    category: { $in: ['payment', 'fee'] }
  };

  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = startDate;
    if (endDate) matchStage.createdAt.$lte = endDate;
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$amount' },
        totalTransactions: { $sum: 1 },
        avgTransactionAmount: { $avg: '$amount' },
        totalPlatformFees: { $sum: '$platformFeeAmount' },
        totalProcessingFees: { $sum: '$processingFeeAmount' }
      }
    }
  ]);
};

// Pre-save middleware
transactionSchema.pre('save', function(next) {
  // Calculate net amount if not provided
  if (!this.netAmount && this.amount) {
    const platformFee = this.platformFeeAmount || 0;
    const processingFee = this.processingFeeAmount || 0;
    this.netAmount = this.amount - platformFee - processingFee;
  }

  // Set category based on type if not provided
  if (!this.category) {
    if (['subscription_payment', 'lesson_payment', 'trial_conversion'].includes(this.type)) {
      this.category = 'payment';
    } else if (['lesson_payout', 'withdrawal', 'bonus'].includes(this.type)) {
      this.category = 'payout';
    } else if (this.type === 'refund') {
      this.category = 'refund';
    } else if (['fee', 'platform_fee', 'cancellation_fee'].includes(this.type)) {
      this.category = 'fee';
    } else if (this.type === 'adjustment') {
      this.category = 'adjustment';
    }
  }

  next();
});

// Serialize virtuals
transactionSchema.set('toJSON', { virtuals: true });
transactionSchema.set('toObject', { virtuals: true });

const TransactionModel = mongoose.model<Transaction>('Transaction', transactionSchema);

export default TransactionModel;
