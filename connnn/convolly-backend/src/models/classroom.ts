import mongoose, { Document, Schema } from "mongoose";
import { DEFAULT_SCHEMA_OPTS } from "./utils";
import { ITutor } from "./tutor";

export type TChatGroup = {
  id: string;
};

type TChatUser = {
  id: string;
};

const DEFAULT_CLASSROOM_VISIBILITY = "students-only";

export const CLASSROOM_VISIBILITY = ["private", DEFAULT_CLASSROOM_VISIBILITY];

export interface IClassroom extends Document {
  tutor?: mongoose.Types.ObjectId | ITutor | null;
  chatGroup: TChatGroup;
  // chatUser: TChatUser;
  visibility: "private" | "students-only";
  expireAt: Date;
  students: string[];
  // channelId: string;
  // learner: mongoose.Types.ObjectId;
  // startedAt?: Date;
  // endedAt?: Date;
  // tutorJoinedAt?: Date;
  // tutorLeftAt?: Date;
  // learnerJoinedAt?: Date;
  // learnerLeftAt?: Date;
  // status: "scheduled" | "in_progress" | "completed" | "cancelled";
  // notes?: string;
  // rating?: number;
  // createdAt: Date;
  // updatedAt: Date;
}

const transform = function (doc: any, ret: any) {
  DEFAULT_SCHEMA_OPTS.toJSON.transform(doc, ret);

  delete ret.chatGroup._id;
  // delete ret.chatUser._id;

  return ret;
};

const ClassroomSchema = new Schema<IClassroom>(
  {
    tutor: { type: Schema.Types.ObjectId, ref: "Tutor", required: true },
    // channelId: { type: String, required: true },
    chatGroup: {
      type: {
        id: String,
      },
      required: [true, "Classroom chat group is required"],
    },
    // chatUser: {
    //   type: {
    //     id: String,
    //   },
    //   required: [true, "Classroom chat user is required"],
    // },
    visibility: {
      type: String,
      enum: CLASSROOM_VISIBILITY,
      default: DEFAULT_CLASSROOM_VISIBILITY,
    },
    expireAt: {
      type: Date,
      expires: 0,
      default: undefined,
      index: true,
    },
    students: [{ type: String, ref: "Student" }],
    // name: {
    //   type: String,
    //   required:[true, 'Classroom name is required']
    // }

    // learner: { type: Schema.Types.ObjectId, ref: "Profile", required: true },
    // tutorJoinedAt: Date,
    // tutorLeftAt: Date,
    // learnerJoinedAt: Date,
    // learnerLeftAt: Date,
    // startedAt: Date,
    // endedAt: Date,
    // status: {
    //   type: String,
    //   enum: ["scheduled", "in_progress", "completed", "cancelled"],
    //   default: "scheduled",
    // },
    // notes: String,
    // rating: Number,
  },
  {
    timestamps: true,
    toJSON: {
      transform,
    },
    toObject: {
      transform,
    },
  }
);

const Classroom = mongoose.model<IClassroom>("Classroom", ClassroomSchema);

export default Classroom;
