import mongoose, { Document, Schema } from 'mongoose';

export interface Escrow extends Document {
  lessonId: mongoose.Types.ObjectId;
  studentId: mongoose.Types.ObjectId;
  tutorId: mongoose.Types.ObjectId;
  amountHeld: number; // in cents
  platformFee: number; // 20% commission in cents
  tutorPayout: number; // amount after commission
  stripePaymentIntentId?: string;
  status: 'held' | 'released' | 'refunded' | 'disputed';
  releasedAt?: Date;
  createdAt: Date;
}

const escrowSchema = new Schema<Escrow>({
  lessonId: { type: Schema.Types.ObjectId, ref: 'Lesson', required: true },
  studentId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  amountHeld: { type: Number, required: true },
  platformFee: { type: Number, required: true },
  tutorPayout: { type: Number, required: true },
  stripePaymentIntentId: { type: String },
  status: {
    type: String,
    enum: ['held', 'released', 'refunded', 'disputed'],
    default: 'held'
  },
  releasedAt: { type: Date },
  createdAt: { type: Date, default: Date.now }
});

const EscrowModel = mongoose.model<Escrow>('Escrow', escrowSchema);
export default EscrowModel;
