{"post": [{"timestamp": "2025-06-25T04:58:27.540Z", "message": "Super Admin Bootstrap", "metadata": {"adminEmail": "<EMAIL>", "adminId": "685b81f3b4b5b121b2c968bf", "timestamp": "2025-06-25T04:58:27.540Z", "success": true}}, {"timestamp": "2025-06-25T05:12:24.825Z", "message": "Admin Activity: BULK_APPROVE_TUTORS on tutors", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "BULK_APPROVE_TUTORS", "resource": "tutors", "method": "POST", "path": "/bulk/approve-tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:12:24.825Z", "success": "NO", "errorMessage": "{\"success\":false,\"message\":\"tutorIds array is required\"}", "requestBody": {"tutorIds": "685b7ecee07b0c4854af3182"}, "responseStatus": 400, "duration": "1ms"}}, {"timestamp": "2025-06-25T05:13:36.420Z", "message": "Admin Activity: BULK_APPROVE_TUTORS on tutors", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "BULK_APPROVE_TUTORS", "resource": "tutors", "method": "POST", "path": "/bulk/approve-tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:13:36.420Z", "success": "YES", "requestBody": {"tutorIds": ["685b7ecee07b0c4854af3182"]}, "responseStatus": 200, "duration": "0ms"}}, {"timestamp": "2025-06-26T13:09:56.562Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/api/admin/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-26T13:09:56.562Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Cannot destructure property 'firstname' of 'req.body' as it is undefined.\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-06-26T13:09:56.560Z\"}", "responseStatus": 500, "duration": "49ms"}}, {"timestamp": "2025-06-26T13:10:42.252Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-26T13:10:42.252Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Failed to create admin\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-06-26T13:10:42.251Z\"}", "requestBody": {"firstname": "sam", "lastname": "oke", "email": "<EMAIL>", "department": "System Administration", "adminLevel": 5, "permissions": ["super_admin", "manage_users", "view_users"]}, "responseStatus": 500, "duration": "379ms"}}, {"timestamp": "2025-06-26T13:11:47.665Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-26T13:11:47.665Z", "success": "YES", "requestBody": {"firstname": "sam", "lastname": "oke", "email": "<EMAIL>", "password": "123456789", "department": "System Administration", "adminLevel": 5, "permissions": ["super_admin", "manage_users", "view_users"]}, "responseStatus": 201, "duration": "566ms"}}, {"timestamp": "2025-06-26T13:13:04.905Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-26T13:13:04.905Z", "success": "YES", "requestBody": {"firstname": "godwin peter", "lastname": "oke", "email": "<EMAIL>", "password": "123456789", "department": "System Administration", "adminLevel": 5, "permissions": ["manage_users", "view_users"]}, "responseStatus": 201, "duration": "335ms"}}, {"timestamp": "2025-07-02T11:16:18.989Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:16:26.060Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:20:26.996Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:21:15.006Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:28:07.749Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:29:53.750Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:47:25.788Z", "message": "Failed to create super admin", "metadata": {"error": "Admin validation failed: encryptedData: encryptedData is required", "stack": "ValidationError: Admin validation failed: encryptedData: encryptedData is required\n    at model.Document.invalidate (/home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3343:32)\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/document.js:3104:17\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schema/subdocument.js:269:14\n    at /home/<USER>/Desktop/convolly-backend/node_modules/mongoose/lib/schemaType.js:1407:9\n    at processTicksAndRejections (node:internal/process/task_queues:85:11)"}}, {"timestamp": "2025-07-02T11:51:49.279Z", "message": "Failed to create super admin", "metadata": {"error": "crypto_1.default.createCipher is not a function", "stack": "TypeError: crypto_1.default.createCipher is not a function\n    at generateEncryptionData (/home/<USER>/Desktop/convolly-backend/src/utils/hashing.ts:48:25)\n    at /home/<USER>/Desktop/convolly-backend/src/routes/adminRouter.ts:282:52\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}}, {"timestamp": "2025-07-02T11:52:54.727Z", "message": "Super Admin Bootstrap", "metadata": {"adminEmail": "<EMAIL>", "adminId": "68651d966e78cf6cafe8e4b4", "timestamp": "2025-07-02T11:52:54.727Z", "success": true}}, {"timestamp": "2025-07-03T14:04:43.571Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "68651d966e78cf6cafe8e4b4", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-07-03T14:04:43.571Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Failed to create admin\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-07-03T14:04:43.570Z\"}", "requestBody": {"firstname": "godwin peter", "lastname": "oke", "email": "<EMAIL>", "password": "123456789", "department": "System Administration", "adminLevel": 1, "permissions": ["manage_users", "view_users"]}, "responseStatus": 500, "duration": "336ms"}}, {"timestamp": "2025-07-03T14:15:59.311Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "68651d966e78cf6cafe8e4b4", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-07-03T14:15:59.311Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Failed to create admin\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-07-03T14:15:59.311Z\"}", "requestBody": {"firstname": "godwin peter", "lastname": "oke", "email": "<EMAIL>", "password": "123456789", "department": "System Administration", "adminLevel": 1, "permissions": ["manage_users", "view_users"]}, "responseStatus": 500, "duration": "309ms"}}, {"timestamp": "2025-07-03T14:16:11.203Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "68651d966e78cf6cafe8e4b4", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-07-03T14:16:11.203Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Failed to create admin\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-07-03T14:16:11.203Z\"}", "requestBody": {"firstname": "godwin peter", "lastname": "oke", "email": "<EMAIL>", "password": "123456789", "department": "System Administration", "adminLevel": 1, "permissions": ["manage_users", "view_users"]}, "responseStatus": 500, "duration": "327ms"}}, {"timestamp": "2025-07-03T14:23:17.305Z", "message": "Admin Activity: CREATE_ADMIN on admin", "metadata": {"adminId": "68651d966e78cf6cafe8e4b4", "adminEmail": "<EMAIL>", "action": "CREATE_ADMIN", "resource": "admin", "method": "POST", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-07-03T14:23:17.305Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Failed to create admin\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-07-03T14:23:17.305Z\"}", "requestBody": {"firstname": "godwin peter", "lastname": "oke", "email": "<EMAIL>", "password": "123456789", "department": "System Administration", "adminLevel": 1, "permissions": ["manage_users", "view_users"]}, "responseStatus": 500, "duration": "275ms"}}], "get": [{"timestamp": "2025-06-25T05:00:23.340Z", "message": "Admin Activity: VIEW_SYSTEM_HEALTH on system", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_SYSTEM_HEALTH", "resource": "system", "method": "GET", "path": "/system/health", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:00:23.340Z", "success": "YES", "responseStatus": 200, "duration": "1ms"}}, {"timestamp": "2025-06-25T05:17:47.750Z", "message": "Admin Activity: VIEW_TUTOR_PAYOUTS on payouts", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_TUTOR_PAYOUTS", "resource": "payouts", "method": "GET", "path": "/financial/tutor-payouts", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:17:47.750Z", "success": "YES", "responseStatus": 200, "duration": "380ms"}}, {"timestamp": "2025-06-25T05:19:09.912Z", "message": "Admin Activity: VIEW_PAYMENT_ANALYTICS on financial", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_PAYMENT_ANALYTICS", "resource": "financial", "method": "GET", "path": "/financial/payment-analytics", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:19:09.912Z", "success": "YES", "responseStatus": 200, "duration": "247ms"}}, {"timestamp": "2025-06-25T05:23:30.820Z", "message": "Admin Activity: VIEW_ALL_TRANSACTIONS on transactions", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_TRANSACTIONS", "resource": "transactions", "method": "GET", "path": "/financial/transactions", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:23:30.820Z", "success": "YES", "responseStatus": 200, "duration": "333ms"}}, {"timestamp": "2025-06-25T05:23:39.420Z", "message": "Admin Activity: VIEW_ALL_TRANSACTIONS on transactions", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_TRANSACTIONS", "resource": "transactions", "method": "GET", "path": "/financial/transactions", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:23:39.420Z", "success": "YES", "responseStatus": 200, "duration": "283ms"}}, {"timestamp": "2025-06-25T05:24:25.081Z", "message": "Admin Activity: VIEW_FINANCIAL_REPORTS on financial", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_FINANCIAL_REPORTS", "resource": "financial", "method": "GET", "path": "/financial/reports", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:24:25.081Z", "success": "YES", "responseStatus": 200, "duration": "392ms"}}, {"timestamp": "2025-06-25T05:25:38.995Z", "message": "Admin Activity: VIEW_FINANCIAL_OVERVIEW on financial", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_FINANCIAL_OVERVIEW", "resource": "financial", "method": "GET", "path": "/financial/overview", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:25:38.995Z", "success": "YES", "responseStatus": 200, "duration": "1969ms"}}, {"timestamp": "2025-06-25T05:27:11.099Z", "message": "Admin Activity: VIEW_ALL_ADMINS on admins", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_ADMINS", "resource": "admins", "method": "GET", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T05:27:11.099Z", "success": "YES", "responseStatus": 200, "duration": "196ms"}}, {"timestamp": "2025-06-25T08:51:48.210Z", "message": "Admin Activity: VIEW_FLAGGED_CONTENT on flagged_content", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_FLAGGED_CONTENT", "resource": "flagged_content", "method": "GET", "path": "/flagged", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T08:51:48.210Z", "success": "YES", "responseStatus": 200, "duration": "383ms"}}, {"timestamp": "2025-06-25T08:55:07.588Z", "message": "Admin Activity: VIEW_ALL_SUBSCRIPTIONS on subscriptions", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_SUBSCRIPTIONS", "resource": "subscriptions", "method": "GET", "path": "/subscriptions", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T08:55:07.588Z", "success": "YES", "responseStatus": 200, "duration": "472ms"}}, {"timestamp": "2025-06-25T08:55:40.177Z", "message": "Admin Activity: VIEW_SUBSCRIPTION_DETAILS on subscription", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_SUBSCRIPTION_DETAILS", "resource": "subscription", "resourceId": "685156efa5837715063a7eff", "method": "GET", "path": "/subscriptions/685156efa5837715063a7eff", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T08:55:40.177Z", "success": "YES", "responseStatus": 200, "duration": "3332ms"}}, {"timestamp": "2025-06-25T08:58:24.528Z", "message": "Admin Activity: VIEW_ALL_STUDENTS on students", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_STUDENTS", "resource": "students", "method": "GET", "path": "/students", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T08:58:24.528Z", "success": "YES", "responseStatus": 200, "duration": "175ms"}}, {"timestamp": "2025-06-25T08:59:00.916Z", "message": "Admin Activity: VIEW_STUDENT_DETAILS on student", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_STUDENT_DETAILS", "resource": "student", "resourceId": "6852b9e2dd99cb3d87303557", "method": "GET", "path": "/students/6852b9e2dd99cb3d87303557", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T08:59:00.916Z", "success": "YES", "responseStatus": 200, "duration": "348ms"}}, {"timestamp": "2025-06-25T09:03:49.691Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:03:49.691Z", "success": "YES", "responseStatus": 200, "duration": "212ms"}}, {"timestamp": "2025-06-25T09:04:54.406Z", "message": "Admin Activity: VIEW_PENDING_TUTORS on tutors", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_PENDING_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors/pending", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:04:54.406Z", "success": "YES", "responseStatus": 200, "duration": "320ms"}}, {"timestamp": "2025-06-25T09:06:07.758Z", "message": "Admin Activity: VIEW_TUTOR_DETAILS on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_TUTOR_DETAILS", "resource": "tutor", "resourceId": "685b7ecee07b0c4854af3182", "method": "GET", "path": "/tutors/685b7ecee07b0c4854af3182", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:06:07.758Z", "success": "YES", "responseStatus": 200, "duration": "583ms"}}, {"timestamp": "2025-06-25T09:20:39.040Z", "message": "Admin Activity: VIEW_DASHBOARD on dashboard", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_DASHBOARD", "resource": "dashboard", "method": "GET", "path": "/dashboard", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:20:39.040Z", "success": "YES", "responseStatus": 200, "duration": "2307ms"}}, {"timestamp": "2025-06-25T09:21:54.299Z", "message": "Admin Activity: VIEW_ANALYTICS on analytics", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ANALYTICS", "resource": "analytics", "method": "GET", "path": "/analytics", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:21:54.299Z", "success": "YES", "responseStatus": 200, "duration": "856ms"}}, {"timestamp": "2025-06-25T09:22:08.713Z", "message": "Admin Activity: VIEW_ANALYTICS on analytics", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ANALYTICS", "resource": "analytics", "method": "GET", "path": "/analytics", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:22:08.713Z", "success": "YES", "responseStatus": 200, "duration": "874ms"}}, {"timestamp": "2025-06-25T09:24:28.696Z", "message": "Admin Activity: VIEW_SYSTEM_HEALTH on system", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_SYSTEM_HEALTH", "resource": "system", "method": "GET", "path": "/system/health", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:24:28.696Z", "success": "YES", "responseStatus": 200, "duration": "1ms"}}, {"timestamp": "2025-06-25T09:25:25.283Z", "message": "Admin Activity: VIEW_ACTIVITY_LOGS on logs", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ACTIVITY_LOGS", "resource": "logs", "method": "GET", "path": "/logs/activity", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:25:25.283Z", "success": "YES", "responseStatus": 200, "duration": "0ms"}}, {"timestamp": "2025-06-25T10:18:36.579Z", "message": "Admin Activity: VIEW_TUTOR_PAYOUTS on payouts", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_TUTOR_PAYOUTS", "resource": "payouts", "method": "GET", "path": "/financial/tutor-payouts", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T10:18:36.579Z", "success": "YES", "responseStatus": 200, "duration": "640ms"}}, {"timestamp": "2025-06-25T10:18:39.349Z", "message": "Admin Activity: VIEW_TUTOR_PAYOUTS on payouts", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_TUTOR_PAYOUTS", "resource": "payouts", "method": "GET", "path": "/financial/tutor-payouts", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T10:18:39.349Z", "success": "YES", "responseStatus": 200, "duration": "543ms"}}, {"timestamp": "2025-06-25T10:33:44.296Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T10:33:44.296Z", "success": "YES", "responseStatus": 200, "duration": "187ms"}}, {"timestamp": "2025-06-25T10:35:12.676Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T10:35:12.676Z", "success": "YES", "responseStatus": 200, "duration": "176ms"}}, {"timestamp": "2025-06-25T13:06:47.674Z", "message": "Admin Activity: VIEW_ANALYTICS on analytics", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ANALYTICS", "resource": "analytics", "method": "GET", "path": "/analytics", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:06:47.673Z", "success": "YES", "responseStatus": 200, "duration": "735ms"}}, {"timestamp": "2025-06-25T13:09:13.815Z", "message": "Admin Activity: VIEW_ALL_STUDENTS on students", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_STUDENTS", "resource": "students", "method": "GET", "path": "/students", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:09:13.815Z", "success": "YES", "responseStatus": 200, "duration": "208ms"}}, {"timestamp": "2025-06-25T13:09:22.735Z", "message": "Admin Activity: VIEW_STUDENT_DETAILS on student", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_STUDENT_DETAILS", "resource": "student", "resourceId": "6852b9e2dd99cb3d87303557", "method": "GET", "path": "/students/6852b9e2dd99cb3d87303557", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:09:22.735Z", "success": "YES", "responseStatus": 200, "duration": "375ms"}}, {"timestamp": "2025-06-25T13:25:45.372Z", "message": "Admin Activity: VIEW_FINANCIAL_OVERVIEW on financial", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_FINANCIAL_OVERVIEW", "resource": "financial", "method": "GET", "path": "/financial/overview", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:25:45.372Z", "success": "YES", "responseStatus": 200, "duration": "1637ms"}}, {"timestamp": "2025-06-25T13:28:52.841Z", "message": "Admin Activity: VIEW_ALL_TRANSACTIONS on transactions", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_TRANSACTIONS", "resource": "transactions", "method": "GET", "path": "/financial/transactions", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:28:52.841Z", "success": "YES", "responseStatus": 200, "duration": "187ms"}}, {"timestamp": "2025-06-25T13:37:57.363Z", "message": "Admin Activity: VIEW_TUTOR_PAYOUTS on payouts", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_TUTOR_PAYOUTS", "resource": "payouts", "method": "GET", "path": "/financial/tutor-payouts", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:37:57.363Z", "success": "YES", "responseStatus": 200, "duration": "348ms"}}, {"timestamp": "2025-06-25T13:50:49.678Z", "message": "Admin Activity: VIEW_ALL_ADMINS on admins", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "VIEW_ALL_ADMINS", "resource": "admins", "method": "GET", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:50:49.678Z", "success": "YES", "responseStatus": 200, "duration": "184ms"}}, {"timestamp": "2025-06-26T13:12:32.765Z", "message": "Admin Activity: VIEW_ALL_ADMINS on admins", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_ADMINS", "resource": "admins", "method": "GET", "path": "/admins", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-26T13:12:32.765Z", "success": "YES", "responseStatus": 200, "duration": "206ms"}}, {"timestamp": "2025-06-26T13:14:50.432Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-26T13:14:50.432Z", "success": "YES", "responseStatus": 200, "duration": "174ms"}}, {"timestamp": "2025-06-27T10:27:11.623Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:27:11.623Z", "success": "YES", "responseStatus": 200, "duration": "1288ms"}}, {"timestamp": "2025-06-27T10:34:28.764Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:34:28.764Z", "success": "YES", "responseStatus": 200, "duration": "2717ms"}}, {"timestamp": "2025-06-27T10:36:23.672Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:36:23.672Z", "success": "YES", "responseStatus": 200, "duration": "4830ms"}}, {"timestamp": "2025-06-27T10:39:23.241Z", "message": "Admin Activity: VIEW_TUTOR_DETAILS on tutor", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_TUTOR_DETAILS", "resource": "tutor", "resourceId": "685b7ecee07b0c4854af3182", "method": "GET", "path": "/tutors/685b7ecee07b0c4854af3182", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:39:23.240Z", "success": "YES", "responseStatus": 200, "duration": "590ms"}}, {"timestamp": "2025-06-27T10:41:52.382Z", "message": "Admin Activity: VIEW_TUTOR_DETAILS on tutor", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_TUTOR_DETAILS", "resource": "tutor", "resourceId": "685b7ecee07b0c4854af3182", "method": "GET", "path": "/tutors/685b7ecee07b0c4854af3182", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:41:52.382Z", "success": "YES", "responseStatus": 200, "duration": "634ms"}}, {"timestamp": "2025-06-27T10:42:45.549Z", "message": "Admin Activity: VIEW_TUTOR_DETAILS on tutor", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_TUTOR_DETAILS", "resource": "tutor", "resourceId": "6850047b1d9de05f99af64e9", "method": "GET", "path": "/tutors/6850047b1d9de05f99af64e9", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:42:45.549Z", "success": "YES", "responseStatus": 200, "duration": "443ms"}}, {"timestamp": "2025-06-27T10:48:30.539Z", "message": "Admin Activity: VIEW_ALL_STUDENTS on students", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_STUDENTS", "resource": "students", "method": "GET", "path": "/students", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:48:30.539Z", "success": "YES", "responseStatus": 200, "duration": "541ms"}}, {"timestamp": "2025-06-27T10:49:41.922Z", "message": "Admin Activity: VIEW_STUDENT_DETAILS on student", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_STUDENT_DETAILS", "resource": "student", "resourceId": "685d402599203593f23e8ba5", "method": "GET", "path": "/students/685d402599203593f23e8ba5", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:49:41.922Z", "success": "YES", "responseStatus": 200, "duration": "504ms"}}, {"timestamp": "2025-06-27T10:50:10.055Z", "message": "Admin Activity: VIEW_STUDENT_DETAILS on student", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_STUDENT_DETAILS", "resource": "student", "resourceId": "684da02353108b07f6bb657d", "method": "GET", "path": "/students/684da02353108b07f6bb657d", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T10:50:10.055Z", "success": "YES", "responseStatus": 200, "duration": "351ms"}}, {"timestamp": "2025-06-27T13:24:30.345Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T13:24:30.345Z", "success": "YES", "responseStatus": 200, "duration": "3121ms"}}, {"timestamp": "2025-06-27T13:29:34.126Z", "message": "Admin Activity: VIEW_ALL_TUTORS on tutors", "metadata": {"adminId": "685d2018951c9ce614963c10", "adminEmail": "<EMAIL>", "action": "VIEW_ALL_TUTORS", "resource": "tutors", "method": "GET", "path": "/tutors", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-27T13:29:34.126Z", "success": "YES", "responseStatus": 200, "duration": "3043ms"}}, {"timestamp": "2025-07-02T11:54:06.599Z", "message": "Admin Activity: VIEW_DASHBOARD on dashboard", "metadata": {"adminId": "68651d966e78cf6cafe8e4b4", "adminEmail": "<EMAIL>", "action": "VIEW_DASHBOARD", "resource": "dashboard", "method": "GET", "path": "/dashboard", "userAgent": "curl/8.5.0", "ip": "::1", "timestamp": "2025-07-02T11:54:06.598Z", "success": "YES", "responseStatus": 200, "duration": "4094ms"}}], "patch": [{"timestamp": "2025-06-25T09:02:07.310Z", "message": "Admin Activity: SUSPEND_STUDENT on student", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "SUSPEND_STUDENT", "resource": "student", "resourceId": "6852b9e2dd99cb3d87303557", "method": "PATCH", "path": "/students/6852b9e2dd99cb3d87303557/suspend", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:02:07.310Z", "success": "YES", "requestBody": {"reason": "Attending a family emergency", "duration": "3 days"}, "responseStatus": 200, "duration": "213ms"}}, {"timestamp": "2025-06-25T09:10:19.956Z", "message": "Admin Activity: APPROVE_TUTOR on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "APPROVE_TUTOR", "resource": "tutor", "resourceId": "6851402d49b10451ea2280db", "method": "PATCH", "path": "/tutors/6851402d49b10451ea2280db/approve", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:10:19.956Z", "success": "YES", "responseStatus": 200, "duration": "306ms"}}, {"timestamp": "2025-06-25T09:12:12.776Z", "message": "Admin Activity: REJECT_TUTOR on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "REJECT_TUTOR", "resource": "tutor", "method": "PATCH", "path": "/api/admin/tutors/6851402d49b10451ea2280db/reject", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:12:12.776Z", "success": "NO", "errorMessage": "{\"code\":\"INTERNAL_SERVER\",\"name\":\"INTERNAL_SERVER\",\"message\":\"Cannot destructure property 'rejectionReason' of 'req.body' as it is undefined.\",\"status\":500,\"success\":false,\"details\":null,\"datetime\":\"2025-06-25T09:12:12.775Z\"}", "responseStatus": 500, "duration": "52ms"}}, {"timestamp": "2025-06-25T09:14:12.616Z", "message": "Admin Activity: REJECT_TUTOR on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "REJECT_TUTOR", "resource": "tutor", "resourceId": "6851402d49b10451ea2280db", "method": "PATCH", "path": "/tutors/6851402d49b10451ea2280db/reject", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:14:12.616Z", "success": "YES", "requestBody": {"rejectionReason": "Insufficient teaching experience"}, "responseStatus": 200, "duration": "260ms"}}, {"timestamp": "2025-06-25T09:15:41.304Z", "message": "Admin Activity: FLAG_TUTOR on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "FLAG_TUTOR", "resource": "tutor", "resourceId": "6851402d49b10451ea2280db", "method": "PATCH", "path": "/tutors/6851402d49b10451ea2280db/flag", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:15:41.304Z", "success": "YES", "responseStatus": 200, "duration": "1945ms"}}, {"timestamp": "2025-06-25T09:17:44.253Z", "message": "Admin Activity: UNFLAG_TUTOR on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "UNFLAG_TUTOR", "resource": "tutor", "resourceId": "6851402d49b10451ea2280db", "method": "PATCH", "path": "/tutors/6851402d49b10451ea2280db/unflag", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:17:44.253Z", "success": "YES", "responseStatus": 200, "duration": "258ms"}}, {"timestamp": "2025-06-25T09:19:11.711Z", "message": "Admin Activity: SUSPEND_TUTOR on tutor", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "SUSPEND_TUTOR", "resource": "tutor", "resourceId": "6851402d49b10451ea2280db", "method": "PATCH", "path": "/tutors/6851402d49b10451ea2280db/suspend", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T09:19:11.711Z", "success": "YES", "requestBody": {"reason": "Brief explanation here", "duration": "Time period here"}, "responseStatus": 200, "duration": "425ms"}}, {"timestamp": "2025-06-25T13:09:36.929Z", "message": "Admin Activity: SUSPEND_STUDENT on student", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "SUSPEND_STUDENT", "resource": "student", "resourceId": "6852b9e2dd99cb3d87303557", "method": "PATCH", "path": "/students/6852b9e2dd99cb3d87303557/suspend", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:09:36.929Z", "success": "YES", "requestBody": {"reason": "Attending a family emergency", "duration": "3 days"}, "responseStatus": 200, "duration": "191ms"}}, {"timestamp": "2025-06-25T13:15:50.637Z", "message": "Admin Activity: CANCEL_SUBSCRIPTION on subscription", "metadata": {"adminId": "unknown", "adminEmail": "unknown", "action": "CANCEL_SUBSCRIPTION", "resource": "subscription", "resourceId": "685156efa5837715063a7eff", "method": "PATCH", "path": "/subscriptions/685156efa5837715063a7eff/cancel", "userAgent": "PostmanRuntime/7.44.0", "ip": "::1", "timestamp": "2025-06-25T13:15:50.637Z", "success": "YES", "responseStatus": 200, "duration": "220ms"}}], "mail": [{"timestamp": "2025-07-09T10:43:30.597Z", "message": "Failed to send lesson confirmation email", "metadata": {}}, {"timestamp": "2025-07-10T11:38:19.461Z", "message": "Failed to send welcome email", "metadata": {"email": "<EMAIL>", "role": "student"}}, {"timestamp": "2025-07-11T17:41:33.539Z", "message": "Failed to send welcome email", "metadata": {"email": "<EMAIL>", "role": "tutor"}}, {"timestamp": "2025-07-11T18:11:28.045Z", "message": "Failed to send welcome email", "metadata": {"email": "<EMAIL>", "role": "tutor"}}], "review": [{"timestamp": "2025-07-10T15:15:32.186Z", "message": "Failed to update review target user", "metadata": {"targetId": "686fb0d678229a73630e4819", "targetRole": "tutor", "rating": 2.3, "comment": "Hello review", "lessonId": "682dbb713e79db09803c2c19"}}, {"timestamp": "2025-07-11T18:48:12.358Z", "message": "Failed to update review target user", "metadata": {"comment": "Learning with <PERSON><PERSON> was awesome!", "rating": 1}}, {"timestamp": "2025-07-11T18:49:00.598Z", "message": "Failed to update review target user", "metadata": {"comment": "Learning with <PERSON><PERSON> was awesome...", "rating": 1}}, {"timestamp": "2025-07-11T18:49:55.125Z", "message": "Failed to update review target user", "metadata": {"comment": "Learning with <PERSON><PERSON> was awesome...", "rating": 4}}, {"timestamp": "2025-07-11T18:59:17.938Z", "message": "Failed to update review target user", "metadata": {"comment": "Learning with <PERSON><PERSON> was awesome...", "rating": 4}}]}