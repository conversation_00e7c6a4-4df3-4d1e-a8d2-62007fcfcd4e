import { ScheduledEmailService } from './scheduledEmailService';
import { logError } from '../utils/logger';

/**
 * Email Service Initializer
 * 
 * This module handles the initialization of all email-related services
 * including scheduled email jobs and notification services.
 */

export class EmailServiceInitializer {
  private static isInitialized = false;

  /**
   * Initialize all email services
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('Email services already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing email services...');

      // Initialize scheduled email jobs
      ScheduledEmailService.initializeScheduledJobs();

      this.isInitialized = true;
      console.log('✅ Email services initialized successfully');

      // Log the scheduled jobs that are now running
      console.log('📅 Active scheduled email jobs:');
      console.log('   - Lesson reminders: Every hour');
      console.log('   - Low lesson balance alerts: Daily at 9 AM');
      console.log('   - Subscription reminders: Daily at 10 AM');
      console.log('   - Inactive student reminders: Weekly on Mondays at 11 AM');

    } catch (error) {
      logError('Failed to initialize email services', 'mail');
      throw new Error('Email service initialization failed');
    }
  }

  /**
   * Check if email services are initialized
   */
  static isEmailServicesInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Gracefully shutdown email services
   */
  static async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('🛑 Shutting down email services...');
      
      // Note: node-cron doesn't provide a direct way to stop all jobs
      // In a production environment, you might want to keep track of job references
      // and destroy them individually
      
      this.isInitialized = false;
      console.log('✅ Email services shut down successfully');
    } catch (error) {
      logError('Error during email services shutdown', 'mail');
    }
  }

  /**
   * Health check for email services
   */
  static async healthCheck(): Promise<{ status: string; services: any }> {
    return {
      status: this.isInitialized ? 'healthy' : 'not_initialized',
      services: {
        scheduledJobs: this.isInitialized,
        notificationService: true, // EmailNotificationService is stateless
        mailerService: true // Mailer utility is stateless
      }
    };
  }
}

/**
 * Export a simple initialization function for easy import
 */
export const initializeEmailServices = () => EmailServiceInitializer.initialize();

/**
 * Export health check function
 */
export const checkEmailServicesHealth = () => EmailServiceInitializer.healthCheck();
