import { Types } from 'mongoose';
import { Event } from '../models/Event';
import { Calendar } from '../models/calender';
import LessonModel from '../models/lesson.model';
import Subscription from '../models/subscription.model';
import Tutor from '../models/tutor';
import Student from '../models/student';
import TransactionModel from '../models/transaction.model';
import { TransactionService } from './transactionService';
import EscrowModel from '../models/escrow.model';

export interface SessionCompletionResult {
  success: boolean;
  message: string;
  data?: {
    lessonsProcessed: number;
    earningsUpdated: number;
    errors: string[];
  };
}

/**
 * Process completed sessions and update student lessons and tutor earnings
 */
export const processCompletedSessions = async (): Promise<SessionCompletionResult> => {
  try {
    const now = new Date();
    const errors: string[] = [];
    let lessonsProcessed = 0;
    let earningsUpdated = 0;

    // Find all events that have ended and haven't been processed yet
    const completedEvents = await Event.find({
      endDateTime: { $lt: now },
      status: 'confirmed',
      // Add a processed flag to avoid reprocessing
      processed: { $ne: true }
    }).populate('calendarId');

    console.log(`Found ${completedEvents.length} completed sessions to process`);

    for (const event of completedEvents) {
      try {
        await processIndividualSession(event);
        lessonsProcessed++;
        
        // Mark event as processed
        await Event.findByIdAndUpdate(event._id, { processed: true });
      } catch (error: any) {
        console.error(`Error processing session ${event._id}:`, error);
        errors.push(`Session ${event._id}: ${error.message}`);
      }
    }

    return {
      success: true,
      message: `Processed ${lessonsProcessed} sessions successfully`,
      data: {
        lessonsProcessed,
        earningsUpdated: lessonsProcessed, // Assuming 1:1 ratio
        errors
      }
    };
  } catch (error: any) {
    console.error('Error in processCompletedSessions:', error);
    return {
      success: false,
      message: `Failed to process completed sessions: ${error.message}`
    };
  }
};

/**
 * Process an individual session
 */
const processIndividualSession = async (event: any): Promise<void> => {
  try {
    // Get calendar information to determine tutor and student
    const calendar = event.calendarId;
    if (!calendar) {
      throw new Error('Calendar not found for event');
    }

    // Extract tutor and student information from event description or title
    const { tutorId, studentId, isFreeTrial, subscriptionId } = await extractSessionParticipants(event, calendar);

    if (!tutorId || !studentId) {
      throw new Error('Could not determine session participants');
    }

    // Get tutor and student details
    const [tutor, student] = await Promise.all([
      Tutor.findById(tutorId),
      Student.findById(studentId)
    ]);

    if (!tutor || !student) {
      throw new Error('Tutor or student not found');
    }

    // Create or update lesson record
    const lesson = await createOrUpdateLessonRecord(event, tutorId, studentId, subscriptionId, isFreeTrial);

    // Process subscription and earnings
    await processSessionCompletion(lesson, tutor, student, subscriptionId, isFreeTrial);

    console.log(`Successfully processed session ${event._id} for tutor ${tutorId} and student ${studentId}`);
  } catch (error: any) {
    throw new Error(`Failed to process individual session: ${error.message}`);
  }
};

/**
 * Extract tutor and student information from event
 */
const extractSessionParticipants = async (event: any, calendar: any) => {
  const tutorId = calendar.ownerType === 'Tutor' ? calendar.ownerUserId : null;
  let studentId: Types.ObjectId | null = null;
  let isFreeTrial = false;
  let subscriptionId: Types.ObjectId | null = null;

  // Extract student information from event description
  const description = event.description || '';
  
  // Look for student email in description
  const emailMatch = description.match(/(?:Student|Booked by): ([^\n]+)/);
  if (emailMatch) {
    const email = emailMatch[1].trim();
    const student = await Student.findOne({ email });
    if (student) {
      studentId = student._id as Types.ObjectId;
    }
  }

  // Check if it's a free trial
  isFreeTrial = description.includes('Type: Free Trial');

  // If not free trial, find subscription
  if (!isFreeTrial && tutorId && studentId) {
    const subscription = await Subscription.findOne({
      tutorId,
      studentId,
      status: 'active'
    });
    if (subscription) {
      subscriptionId = subscription._id;
    }
  }

  return { tutorId, studentId, isFreeTrial, subscriptionId };
};

/**
 * Create or update lesson record
 */
const createOrUpdateLessonRecord = async (
  event: any,
  tutorId: Types.ObjectId,
  studentId: Types.ObjectId,
  subscriptionId: Types.ObjectId | null,
  isFreeTrial: boolean
) => {
  // Check if lesson record already exists
  let lesson = await LessonModel.findOne({
    tutorId,
    studentId,
    scheduledTime: event.startDateTime
  });

  if (!lesson) {
    // Create new lesson record
    lesson = new LessonModel({
      tutorId,
      studentId,
      subscriptionId,
      scheduledTime: event.startDateTime,
      duration: Math.round((event.endDateTime - event.startDateTime) / (1000 * 60)), // Convert to minutes
      status: 'completed',
      isFreeTrial,
      confirmedAt: new Date(),
      notes: `Auto-completed session: ${event.title}`
    });
  } else {
    // Update existing lesson
    lesson.status = 'completed';
    lesson.confirmedAt = new Date();
  }

  await lesson.save();
  return lesson;
};

/**
 * Process session completion - update subscription and tutor earnings
 */
const processSessionCompletion = async (
  lesson: any,
  tutor: any,
  student: any,
  subscriptionId: Types.ObjectId | null,
  isFreeTrial: boolean
) => {
  // Update subscription remaining lessons (only for paid lessons)
  if (!isFreeTrial && subscriptionId) {
    const subscription = await Subscription.findById(subscriptionId);
    if (subscription && subscription.remainingLessons > 0) {
      subscription.remainingLessons -= 1;
      await subscription.save();
      console.log(`Decreased remaining lessons for subscription ${subscriptionId}. New count: ${subscription.remainingLessons}`);
    }
  }

  // Update tutor earnings
  const lessonPrice = tutor.basePrice || 0;
  if (lessonPrice > 0) {
    // Calculate platform fee (20%)
    const platformFeeRate = 0.20;
    const platformFee = Math.round(lessonPrice * platformFeeRate * 100); // Convert to cents
    const tutorEarnings = Math.round(lessonPrice * (1 - platformFeeRate) * 100); // Convert to cents

    // Update tutor's available balance
    tutor.availableBalance += tutorEarnings;
    tutor.totalLessons += 1;
    await tutor.save();

    // Record lesson payment and payout using transaction service
    const transactionResult = await TransactionService.recordLessonPayment(
      lesson._id as Types.ObjectId,
      student._id as Types.ObjectId,
      tutor._id as Types.ObjectId,
      lessonPrice * 100, // Convert to cents
      isFreeTrial
    );

    // Create escrow record for tracking
    const escrow = new EscrowModel({
      lessonId: lesson._id,
      studentId: student._id,
      tutorId: tutor._id,
      amountHeld: lessonPrice * 100, // Convert to cents
      platformFee,
      tutorPayout: tutorEarnings,
      status: 'released',
      releasedAt: new Date()
    });
    await escrow.save();

    console.log(`Updated tutor ${tutor._id} earnings by $${tutorEarnings / 100}. New balance: $${tutor.availableBalance / 100}`);
  }

  // Update student's lesson count
  if (student.totalLessons !== undefined) {
    student.totalLessons += 1;
  } else {
    student.totalLessons = 1;
  }
  await student.save();
};

/**
 * Process a specific session by event ID (for manual processing)
 */
export const processSpecificSession = async (eventId: string): Promise<SessionCompletionResult> => {
  try {
    if (!Types.ObjectId.isValid(eventId)) {
      return {
        success: false,
        message: 'Invalid event ID format'
      };
    }

    const event = await Event.findById(eventId).populate('calendarId');
    if (!event) {
      return {
        success: false,
        message: 'Event not found'
      };
    }

    if (event.endDateTime > new Date()) {
      return {
        success: false,
        message: 'Session has not ended yet'
      };
    }

    if (event.status !== 'confirmed') {
      return {
        success: false,
        message: 'Session was not confirmed'
      };
    }

    await processIndividualSession(event);
    
    // Mark event as processed
    await Event.findByIdAndUpdate(event._id, { processed: true });

    return {
      success: true,
      message: 'Session processed successfully',
      data: {
        lessonsProcessed: 1,
        earningsUpdated: 1,
        errors: []
      }
    };
  } catch (error: any) {
    console.error('Error processing specific session:', error);
    return {
      success: false,
      message: `Failed to process session: ${error.message}`
    };
  }
};
