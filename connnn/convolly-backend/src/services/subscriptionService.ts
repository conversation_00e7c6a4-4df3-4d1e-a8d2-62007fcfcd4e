import Stripe from 'stripe';
import Subscription from '../models/subscription.model';
import Student from '../models/student';
import Tu<PERSON> from '../models/tutor';
import { TransactionService } from './transactionService';
import { Types } from 'mongoose';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export interface SubscriptionUpdateData {
  status?: 'active' | 'paused' | 'cancelled' | 'pending_transfer' | 'incomplete';
  currentPeriodStart?: Date;
  currentPeriodEnd?: Date;
  nextBillingDate?: Date;
  remainingLessons?: number;
  paymentHistory?: any[];
  cancellationReason?: string;
  cancellationDescription?: string;
  cancelledAt?: Date;
}

export class SubscriptionService {

  /**
   * Safely convert Stripe timestamp to Date
   */
  static safeTimestampToDate(timestamp: number | undefined | null, fallback?: Date): Date {
    if (!timestamp || isNaN(timestamp)) {
      return fallback || new Date();
    }
    const date = new Date(timestamp * 1000);
    return isNaN(date.getTime()) ? (fallback || new Date()) : date;
  }
  
  /**
   * Find subscription by Stripe subscription ID
   */
  static async findByStripeId(stripeSubscriptionId: string) {
    return await Subscription.findOne({ stripeSubscriptionId });
  }

  /**
   * Update subscription status and sync with Stripe
   */
  static async updateSubscriptionStatus(
    subscriptionId: string, 
    updateData: SubscriptionUpdateData,
    source: 'webhook' | 'controller' = 'controller'
  ) {
    try {
      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Update local subscription
      Object.assign(subscription, updateData);
      await subscription.save();

      console.log(`Subscription ${subscriptionId} updated via ${source}:`, updateData);
      return subscription;
    } catch (error) {
      console.error('Error updating subscription status:', error);
      throw error;
    }
  }

  /**
   * Add payment to subscription history
   */
  static async addPaymentToHistory(
    subscriptionId: string,
    paymentData: {
      amount: number;
      currency: string;
      status: string;
      stripePaymentIntentId: string;
      stripeInvoiceId: string;
      description: string;
    }
  ) {
    try {
      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const paymentHistory = {
        ...paymentData,
        paidAt: new Date()
      };

      if (!subscription.paymentHistory) {
        subscription.paymentHistory = [];
      }

      // Check if payment already exists (only if payment intent ID is provided)
      const existingPayment = paymentData.stripePaymentIntentId ?
        subscription.paymentHistory.find(p => p.stripePaymentIntentId === paymentData.stripePaymentIntentId) :
        null;

      if (!existingPayment) {
        subscription.paymentHistory.push(paymentHistory);
        await subscription.save();
        console.log(`Payment added to subscription ${subscriptionId}:`, paymentData);
      }

      return subscription;
    } catch (error) {
      console.error('Error adding payment to history:', error);
      throw error;
    }
  }

  /**
   * Activate incomplete subscription
   */
  static async activateIncompleteSubscription(stripeSubscriptionId: string) {
    try {
      const subscription = await this.findByStripeId(stripeSubscriptionId);
      if (!subscription) {
        console.log(`No subscription found for Stripe ID: ${stripeSubscriptionId}`);
        return null;
      }

      if (subscription.status === 'incomplete') {
        subscription.status = 'active';
        subscription.remainingLessons = subscription.lessonsPerWeek * 4;
        await subscription.save();
        
        console.log(`Subscription ${subscription._id} activated from incomplete status`);
        return subscription;
      }

      return subscription;
    } catch (error) {
      console.error('Error activating incomplete subscription:', error);
      throw error;
    }
  }

  /**
   * Handle subscription status change from Stripe
   */
  static async handleStripeStatusChange(stripeSubscription: Stripe.Subscription) {
    try {
      const subscription = await this.findByStripeId(stripeSubscription.id);
      if (!subscription) {
        console.log(`No local subscription found for Stripe subscription: ${stripeSubscription.id}`);
        return null;
      }

      // Map Stripe status to local status
      let newStatus: 'active' | 'paused' | 'cancelled' | 'pending_transfer' | 'incomplete';
      
      switch (stripeSubscription.status) {
        case 'active':
          newStatus = 'active';
          break;
        case 'paused':
          newStatus = 'paused';
          break;
        case 'canceled':
          newStatus = 'cancelled';
          break;
        case 'incomplete':
          newStatus = 'incomplete';
          break;
        case 'incomplete_expired':
          newStatus = 'cancelled';
          break;
        case 'trialing':
          newStatus = 'active';
          break;
        case 'past_due':
          newStatus = 'active'; // Keep as active but could add a past_due status
          break;
        default:
          newStatus = subscription.status;
      }

      const currentTime = new Date();
      const futureTime = new Date(currentTime.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

      const updateData: SubscriptionUpdateData = {
        status: newStatus,
        currentPeriodStart: this.safeTimestampToDate((stripeSubscription as any).current_period_start, currentTime),
        currentPeriodEnd: this.safeTimestampToDate((stripeSubscription as any).current_period_end, futureTime),
        nextBillingDate: this.safeTimestampToDate((stripeSubscription as any).current_period_end, futureTime)
      };

      // If subscription became active, ensure remaining lessons are set
      if (newStatus === 'active' && !subscription.remainingLessons) {
        updateData.remainingLessons = subscription.lessonsPerWeek * 4;
      }

      return await this.updateSubscriptionStatus(subscription._id.toString(), updateData, 'webhook');
    } catch (error) {
      console.error('Error handling Stripe status change:', error);
      throw error;
    }
  }

  /**
   * Create subscription confirmation response
   */
  static async createConfirmationResponse(subscription: any, paymentMethod?: any) {
    try {
      const populatedSubscription = await Subscription.findById(subscription._id)
        .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar')
        .populate('studentId', 'learningReasons skillsToImprove firstname lastname email');

      return {
        success: true,
        message: 'Subscription confirmed successfully',
        data: {
          subscription: populatedSubscription,
          paymentMethod,
          nextBillingDate: subscription.nextBillingDate,
          amountPaid: subscription.monthlyPrice
        }
      };
    } catch (error) {
      console.error('Error creating confirmation response:', error);
      throw error;
    }
  }

  /**
   * Notify subscription controller about webhook events
   */
  static async notifySubscriptionUpdate(
    stripeSubscriptionId: string,
    eventType: string,
    eventData: any
  ) {
    try {
      const subscription = await this.findByStripeId(stripeSubscriptionId);
      if (!subscription) return;

      // Log the event
      console.log(`Subscription ${subscription._id} received ${eventType} event:`, eventData);

      // You can add HTTP call to notify the controller endpoint if needed
      // This creates a bridge between webhook and controller for additional processing
      try {
        // Optional: Make internal API call to subscription controller
        // const axios = require('axios');
        // await axios.post(`${process.env.SERVER_URL}/api/subscription/webhook-notify/${subscription._id}`, {
        //   eventType,
        //   eventData,
        //   source: 'webhook',
        //   timestamp: new Date()
        // });
      } catch (notifyError) {
        console.error('Error notifying subscription controller:', notifyError);
        // Don't throw - this is optional notification
      }

      return subscription;
    } catch (error) {
      console.error('Error notifying subscription update:', error);
    }
  }

  /**
   * Validate subscription before processing
   */
  static async validateSubscription(subscriptionId: string) {
    try {
      if (!Types.ObjectId.isValid(subscriptionId)) {
        throw new Error('Invalid subscription ID format');
      }

      const subscription = await Subscription.findById(subscriptionId)
        .populate('studentId')
        .populate('tutorId');

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      return subscription;
    } catch (error) {
      console.error('Error validating subscription:', error);
      throw error;
    }
  }
}

export default SubscriptionService;
