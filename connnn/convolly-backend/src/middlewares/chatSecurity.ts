import { Request, Response, NextFunction } from "express";
import { createErrorResponse } from "./errorHandler";
import Conversation from "../models/conversation";
import { isValidObjectId } from "../utils/validation";

/**
 * Security middleware for chat operations
 */

/**
 * Rate limiting for chat operations (basic implementation)
 * In production, use Redis-based rate limiting
 */
const chatRateLimits = new Map<string, { count: number; resetTime: number }>();

export const chatRateLimit = (maxRequests = 100, windowMs = 60000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const clientId = req.user?.id || req.ip || 'anonymous';
      const now = Date.now();
      const windowStart = now - windowMs;

      // Clean up old entries
      for (const [key, value] of chatRateLimits.entries()) {
        if (value.resetTime < windowStart) {
          chatRateLimits.delete(key);
        }
      }

      const userLimit = chatRateLimits.get(clientId);
      
      if (!userLimit) {
        chatRateLimits.set(clientId, { count: 1, resetTime: now + windowMs });
        next();
        return;
      }

      if (userLimit.resetTime < now) {
        // Reset the window
        chatRateLimits.set(clientId, { count: 1, resetTime: now + windowMs });
        next();
        return;
      }

      if (userLimit.count >= maxRequests) {
        createErrorResponse(res, {
          message: "Too many chat requests",
          details: { 
            limit: maxRequests,
            windowMs,
            retryAfter: Math.ceil((userLimit.resetTime - now) / 1000)
          }
        }, 429);
        return;
      }

      userLimit.count++;
      next();
    } catch (err: any) {
      console.error("Chat rate limit error:", err);
      next(); // Don't block on rate limit errors
    }
  };
};

/**
 * Verify conversation access permissions
 * Checks if user is a participant in the conversation
 */
export const verifyConversationAccess = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { conversationId } = req.params;

    if (!req.user || !req.user.id) {
      createErrorResponse(res, {
        message: "Authentication required",
        details: { reason: "User not authenticated" }
      }, 401);
      return;
    }

    if (!isValidObjectId(conversationId)) {
      createErrorResponse(res, {
        message: "Invalid conversation ID",
        details: { conversationId }
      }, 400);
      return;
    }

    // Check if conversation exists and user is a participant
    const conversation = await Conversation.findOne({
      _id: conversationId,
      "participants.user": req.user.id,
    }).select("_id participants");

    if (!conversation) {
      createErrorResponse(res, {
        message: "Conversation not found or access denied",
        details: { 
          conversationId,
          reason: "User is not a participant or conversation doesn't exist"
        }
      }, 403);
      return;
    }

    // Add conversation to request for use in controller
    req.conversation = conversation;
    next();
  } catch (err: any) {
    console.error("Error verifying conversation access:", err);
    createErrorResponse(res, {
      message: "Error verifying conversation access",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Verify user profile access permissions
 * Users can only access their own data unless they're admin
 */
export const verifyUserProfileAccess = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;

    if (!req.user || !req.user.id) {
      createErrorResponse(res, {
        message: "Authentication required",
        details: { reason: "User not authenticated" }
      }, 401);
      return;
    }

    // Admin users can access any profile
    if (req.user.role === "admin") {
      next();
      return;
    }

    // Regular users can only access their own profile
    if (req.user.id !== userId) {
      createErrorResponse(res, {
        message: "Forbidden: You can only access your own data",
        details: { 
          requestedUserId: userId,
          authenticatedUserId: req.user.id,
          userRole: req.user.role
        }
      }, 403);
      return;
    }

    next();
  } catch (err: any) {
    console.error("Error verifying user profile access:", err);
    createErrorResponse(res, {
      message: "Error verifying user access",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Sanitize sensitive data from responses
 */
export const sanitizeChatResponse = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const originalJson = res.json;

    res.json = function(data: any) {
      // Remove sensitive fields from user objects in responses
      if (data && data.data) {
        if (Array.isArray(data.data)) {
          data.data = data.data.map(sanitizeUserData);
        } else if (typeof data.data === 'object') {
          data.data = sanitizeUserData(data.data);
        }
      }

      return originalJson.call(this, data);
    };

    next();
  } catch (err: any) {
    console.error("Error in response sanitization:", err);
    next(); // Don't block on sanitization errors
  }
};

/**
 * Helper function to sanitize user data
 */
function sanitizeUserData(obj: any): any {
  if (!obj || typeof obj !== 'object') return obj;

  // Create a copy to avoid mutating original
  const sanitized = { ...obj };

  // Remove sensitive fields
  const sensitiveFields = [
    'password',
    'resetCode',
    'resetCodeExpires',
    'stripeCustomerId',
    'stripeAccountId',
    'bankDetails',
    'paymentMethods'
  ];

  sensitiveFields.forEach(field => {
    delete sanitized[field];
  });

  // Recursively sanitize nested objects
  Object.keys(sanitized).forEach(key => {
    if (sanitized[key] && typeof sanitized[key] === 'object') {
      if (Array.isArray(sanitized[key])) {
        sanitized[key] = sanitized[key].map(sanitizeUserData);
      } else {
        sanitized[key] = sanitizeUserData(sanitized[key]);
      }
    }
  });

  return sanitized;
}

/**
 * Log chat operations for security monitoring
 */
export const logChatOperation = (operation: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const logData = {
        operation,
        userId: req.user?.id,
        userRole: req.user?.role,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString(),
        params: req.params,
        query: req.query,
      };

      // In production, send to proper logging service
      console.log(`[CHAT_OPERATION] ${JSON.stringify(logData)}`);

      next();
    } catch (err: any) {
      console.error("Error logging chat operation:", err);
      next(); // Don't block on logging errors
    }
  };
};

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      conversation?: any;
    }
  }
}
