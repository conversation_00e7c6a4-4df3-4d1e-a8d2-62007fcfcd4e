// Helper function to calculate subscription pricing
type PlanType = '1-lesson' | '2-lesson' | '3-lesson' | '5-lesson';

interface SubscriptionPlan {
  lessonsPerWeek: number;
  monthlyPrice: number;
}

// Legacy function for backward compatibility - uses default pricing
export const getSubscriptionPricing = (planType: PlanType): SubscriptionPlan | undefined => {
  const pricing: Record<PlanType, SubscriptionPlan> = {
    '1-lesson': { lessonsPerWeek: 1, monthlyPrice: 12000 },
    '2-lesson': { lessonsPerWeek: 2, monthlyPrice: 22000 },
    '3-lesson': { lessonsPerWeek: 3, monthlyPrice: 31000 },
    '5-lesson': { lessonsPerWeek: 5, monthlyPrice: 48000 },
  };
  return pricing[planType];
};

// New function to calculate pricing based on tutor's basePrice with 10% processing fee
export const calculateSubscriptionPricing = (lessonsPerWeek: number, tutorBasePricePerLesson: number): SubscriptionPlan => {
  // Calculate monthly price: (basePrice per lesson * lessons per week * 4 weeks) + 10% processing fee
  const basePricePerLessonCents = Math.round(tutorBasePricePerLesson * 100);
  const baseMonthlyPriceCents = basePricePerLessonCents * lessonsPerWeek * 4;
  const processingFee = Math.round(baseMonthlyPriceCents * 0.10); // 10% processing fee
  const monthlyPriceCents = baseMonthlyPriceCents + processingFee; // Total including processing fee

  return {
    lessonsPerWeek,
    monthlyPrice: monthlyPriceCents // in cents
  };
};

// Helper to get all available lesson plans with tutor pricing
export const getAllSubscriptionPlans = (tutorBasePricePerLesson: number): SubscriptionPlan[] => {
  const availableLessonsPerWeek = [1, 2, 3, 5];

  return availableLessonsPerWeek.map(lessonsPerWeek =>
    calculateSubscriptionPricing(lessonsPerWeek, tutorBasePricePerLesson)
  );
};
