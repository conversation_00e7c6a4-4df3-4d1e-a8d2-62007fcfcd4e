import type { Request, Response, NextFunction, RequestHandler } from "express";

type ExpressField = "query" | "params" | "body";

export function patchReadonlyExpressFields(
  middleware: RequestHandler,
  fields: ExpressField[] = ["query", "params"]
): RequestHandler {
  return function (req: Request, res: Response, next: NextFunction) {
    const originalDescriptors: Record<string, PropertyDescriptor | undefined> =
      {};
    const originalValues: Record<string, any> = {};

    try {
      // Backup and make fields mutable
      fields.forEach((field) => {
        originalDescriptors[field] = Object.getOwnPropertyDescriptor(
          req,
          field
        );
        originalValues[field] = req[field];

        Object.defineProperty(req, field, {
          value: { ...req[field] }, // clone shallowly
          writable: true,
          configurable: true,
        });
      });

      // Run the middleware
      middleware(req, res, (err?: any) => {
        // Restore the original read-only behavior
        fields.forEach((field) => {
          if (originalDescriptors[field]) {
            Object.defineProperty(req, field, originalDescriptors[field]!);
          } else {
            Object.defineProperty(req, field, {
              get() {
                return originalValues[field];
              },
              configurable: true,
            });
          }
        });

        next(err);
      });
    } catch (err) {
      next(err);
    }
  };
}
