import { Request, Response, NextFunction } from "express";
import { JsonResponseProps } from "../types/misc";
import { ERROR_INTERNAL_SERVER, IS_PROD } from "../config/constants";
import { logError } from "../utils/logger";

type ErrorResponseProps = Omit<JsonResponseProps, "data"> & {
  details?: any;
  name: string;
};

export type ErrorProps =
  | {
      statusCode?: any;
      details?: any;
      status?: any;
      message?: string;
      _status?: number;
      code?: string;
      name?: string;
    }
  | string;

export const getErrorResponse = (error: ErrorProps, status?: number) => {
  const err: ErrorResponseProps = {
    code: "",
    name: "",
    ...ERROR_INTERNAL_SERVER,
    success: false,
    status: status || 500,
    details: null,
    datetime: new Date(),
  };

  if (typeof error === "string") {
    if (error) err.message = error;
  } else {
    if (error.message) err.message = error.message;

    err.details = error.details || null;

    err.code = error.code || "";

    err.name = error.name || "";

    if (error._status) err.status = error._status;
    else if (!status) err.status = error.status || error.statusCode;
  }

  const upperText = err.message.toUpperCase();

  if (
    upperText.indexOf("ETIMEDOUT") > -1 ||
    upperText.indexOf("TIMEOUT") > -1
  ) {
    err.code = "GATEWAY_TIMEDOUT";
    err.status = 504;
    err.message = "Gateway connection timedout.";
  }

  if (err.status === 500) {
    // log error in file
    if (IS_PROD) logError(err.message, "others", err.details);
    else
      console.log(
        "🔴 Internal Server Error:",
        err.message,
        err.details,
        JSON.stringify((error as any).stack),
        err.code,
        new Date()
      );
  }

  if (!err.code)
    switch (err.status) {
      case 400:
        err.code = "BAD_REQUEST";
        break;
      case 401:
        err.code = "UNAUTHORIZED";
        break;
      case 403:
        err.code = "FORBIDDEN_ACCESS";
        break;
      case 404:
        err.code = "NOT_FOUND";
        break;
      case 409:
        err.code = "CONFLICT";
        break;
      case 422:
        err.code = "UNPROCESSABLE_ENTITY";
        break;
      case 429:
        err.code = "TOO_MANY_REQUESTS";
        break;
      default:
        err.code = "INTERNAL_SERVER";
        break;
    }

  if (!err.name) err.name = err.code;

  err.datetime = new Date();

  return err;
};

export const createError = (error: ErrorProps, status?: number) => {
  const res = getErrorResponse(error, status);

  const err = new Error(res.message);

  err.data = res;

  return err;
};

export const createErrorResponse = (
  res: Response,
  error: ErrorProps,
  status = 400
) => {
  status = typeof error !== "string" ? error.status || status : status;

  const _error = getErrorResponse(error, status);

  return res.status(_error.status).json(_error);
};

const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  createErrorResponse(res, err, err.status || 500);
};

export default errorHandler;
