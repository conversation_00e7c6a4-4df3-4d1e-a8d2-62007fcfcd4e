import { Request, Response, NextFunction } from "express";
import { createErrorResponse } from "./errorHandler";
import { isValidObjectId } from "../utils/validation";

/**
 * Validation middleware for chat-related operations
 */

/**
 * Validate conversation ID parameter
 */
export const validateConversationId = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { conversationId } = req.params;

    if (!conversationId) {
      createErrorResponse(res, {
        message: "Conversation ID is required",
        details: { field: "conversationId", location: "params" }
      }, 400);
      return;
    }

    if (!isValidObjectId(conversationId)) {
      createErrorResponse(res, {
        message: "Invalid conversation ID format",
        details: { 
          field: "conversationId", 
          value: conversationId,
          location: "params",
          expected: "Valid MongoDB ObjectId"
        }
      }, 400);
      return;
    }

    next();
  } catch (err: any) {
    createErrorResponse(res, {
      message: "Validation error",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Validate user ID parameter
 */
export const validateUserId = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      createErrorResponse(res, {
        message: "User ID is required",
        details: { field: "userId", location: "params" }
      }, 400);
      return;
    }

    if (!isValidObjectId(userId)) {
      createErrorResponse(res, {
        message: "Invalid user ID format",
        details: { 
          field: "userId", 
          value: userId,
          location: "params",
          expected: "Valid MongoDB ObjectId"
        }
      }, 400);
      return;
    }

    next();
  } catch (err: any) {
    createErrorResponse(res, {
      message: "Validation error",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Validate pagination query parameters
 */
export const validatePaginationQuery = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { limit, sortOrder, cursor } = req.query;

    // Validate limit
    if (limit !== undefined) {
      const parsedLimit = parseInt(limit as string, 10);
      if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 100) {
        createErrorResponse(res, {
          message: "Invalid limit parameter",
          details: { 
            field: "limit", 
            value: limit,
            location: "query",
            allowed: "Integer between 1 and 100"
          }
        }, 400);
        return;
      }
    }

    // Validate sortOrder
    if (sortOrder !== undefined && !["asc", "desc"].includes(sortOrder as string)) {
      createErrorResponse(res, {
        message: "Invalid sort order",
        details: { 
          field: "sortOrder", 
          value: sortOrder,
          location: "query",
          allowed: ["asc", "desc"]
        }
      }, 400);
      return;
    }

    // Validate cursor
    if (cursor !== undefined && !isValidObjectId(cursor as string)) {
      createErrorResponse(res, {
        message: "Invalid cursor format",
        details: { 
          field: "cursor", 
          value: cursor,
          location: "query",
          expected: "Valid MongoDB ObjectId"
        }
      }, 400);
      return;
    }

    next();
  } catch (err: any) {
    createErrorResponse(res, {
      message: "Validation error",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Sanitize and normalize query parameters
 */
export const sanitizeQueryParams = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Sanitize limit
    if (req.query.limit) {
      const limit = parseInt(req.query.limit as string, 10);
      req.query.limit = Math.min(Math.max(limit, 1), 100).toString();
    }

    // Sanitize sortOrder
    if (req.query.sortOrder) {
      const sortOrder = (req.query.sortOrder as string).toLowerCase();
      req.query.sortOrder = ["asc", "desc"].includes(sortOrder) ? sortOrder : "desc";
    }

    // Sanitize cursor (trim whitespace)
    if (req.query.cursor) {
      req.query.cursor = (req.query.cursor as string).trim();
    }

    next();
  } catch (err: any) {
    createErrorResponse(res, {
      message: "Parameter sanitization error",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Validate user authorization for accessing conversations
 */
export const validateConversationAccess = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;

    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      createErrorResponse(res, {
        message: "Authentication required",
        details: { reason: "User not authenticated" }
      }, 401);
      return;
    }

    // Authorization check: Users can only access their own conversations unless they're admin
    if (req.user.id !== userId && req.user.role !== "admin") {
      createErrorResponse(res, {
        message: "Forbidden: You can only access your own conversations",
        details: { 
          requestedUserId: userId, 
          authenticatedUserId: req.user.id,
          userRole: req.user.role
        }
      }, 403);
      return;
    }

    next();
  } catch (err: any) {
    createErrorResponse(res, {
      message: "Authorization validation error",
      details: { error: err.message }
    }, 500);
  }
};

/**
 * Combined validation middleware for getting user conversations
 */
export const validateGetUserConversations = [
  validateUserId,
  validatePaginationQuery,
  sanitizeQueryParams,
  validateConversationAccess,
];

/**
 * Combined validation middleware for getting conversation messages
 */
export const validateGetConversationMessages = [
  validateConversationId,
  validatePaginationQuery,
  sanitizeQueryParams,
];
