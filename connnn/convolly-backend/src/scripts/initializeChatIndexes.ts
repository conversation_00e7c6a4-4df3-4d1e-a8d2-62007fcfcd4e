#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to initialize chat-related database indexes for optimal performance
 * Run this script after setting up the database or when deploying to production
 */

import mongoose from "mongoose";
import { createChatIndexes } from "../utils/chatOptimizations";

const initializeIndexes = async () => {
  try {
    console.log("🚀 Starting chat index initialization...");

    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || process.env.DATABASE_URL;
    if (!mongoUri) {
      throw new Error("MongoDB URI not found in environment variables");
    }

    console.log("📡 Connecting to MongoDB...");
    await mongoose.connect(mongoUri);
    console.log("✅ Connected to MongoDB successfully");

    // Create chat indexes
    await createChatIndexes();

    console.log("🎉 Chat indexes initialized successfully!");
    console.log("\nIndexes created:");
    console.log("📝 Message collection:");
    console.log("  - conversation_createdAt_desc: { conversation: 1, createdAt: -1 }");
    console.log("  - conversation_id_desc: { conversation: 1, _id: -1 }");
    console.log("  - sender_createdAt_desc: { sender: 1, createdAt: -1 }");
    console.log("  - recipients_sparse: { recipients: 1 } (sparse)");
    
    console.log("\n💬 Conversation collection:");
    console.log("  - participants_user_updatedAt_desc: { 'participants.user': 1, updatedAt: -1 }");
    console.log("  - participants_user_id_desc: { 'participants.user': 1, _id: -1 }");
    console.log("  - lastMessage_sparse: { lastMessage: 1 } (sparse)");
    console.log("  - updatedAt_desc: { updatedAt: -1 }");

    console.log("\n📊 Performance benefits:");
    console.log("  ✓ Faster conversation message retrieval");
    console.log("  ✓ Optimized user conversation queries");
    console.log("  ✓ Efficient cursor-based pagination");
    console.log("  ✓ Improved sorting performance");

  } catch (error) {
    console.error("❌ Error initializing chat indexes:", error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
    process.exit(0);
  }
};

// Run the script if called directly
if (require.main === module) {
  initializeIndexes();
}

export default initializeIndexes;
