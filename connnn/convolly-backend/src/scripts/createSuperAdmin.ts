#!/usr/bin/env ts-node

/**
 * Super Admin Creation Script
 * 
 * This script creates the first super admin user for the system.
 * Run this script when setting up the system for the first time.
 * 
 * Usage:
 * npm run create-super-admin
 * or
 * npx ts-node src/scripts/createSuperAdmin.ts
 */

import dotenv from "dotenv";
import mongoose from "mongoose";
import readline from "readline";
import Admin, { AdminPermission } from "../models/admin";

// Load environment variables
dotenv.config();

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
const question = (query: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(query, resolve);
  });
};

// Hide password input
const questionHidden = (query: string): Promise<string> => {
  return new Promise((resolve) => {
    process.stdout.write(query);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    
    const onData = (char: string) => {
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004': // Ctrl+D
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdin.removeListener('data', onData);
          console.log('');
          resolve(password);
          break;
        case '\u0003': // Ctrl+C
          process.exit();
          break;
        case '\u007f': // Backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    };
    
    process.stdin.on('data', onData);
  });
};

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate password strength
const isValidPassword = (password: string): boolean => {
  return password.length >= 8;
};

// Connect to database
const connectDB = async (): Promise<void> => {
  try {
    const mongoUri = process.env.NODE_ENV === 'production' 
      ? process.env.MONGO_PROD_URI 
      : process.env.MONGO_DEV_URI;
    
    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables');
    }
    
    await mongoose.connect(mongoUri);
    console.log('🟢 Connected to MongoDB');
  } catch (error) {
    console.error('🔴 Database connection error:', error);
    process.exit(1);
  }
};

// Main function
const createSuperAdmin = async (): Promise<void> => {
  try {
    console.log('🚀 Super Admin Creation Script');
    console.log('================================\n');
    
    // Connect to database
    await connectDB();
    
    // Check if any admin already exists
    const existingAdminCount = await Admin.countDocuments();
    
    if (existingAdminCount > 0) {
      console.log('⚠️  Warning: Admin users already exist in the system.');
      const proceed = await question('Do you want to create another super admin? (y/N): ');
      
      if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
        console.log('Operation cancelled.');
        process.exit(0);
      }
    }
    
    console.log('Please provide the following information:\n');
    
    // Get user input
    const firstname = await question('First Name: ');
    const lastname = await question('Last Name: ');
    
    let email: string;
    do {
      email = await question('Email: ');
      if (!isValidEmail(email)) {
        console.log('❌ Invalid email format. Please try again.');
      }
    } while (!isValidEmail(email));
    
    // Check if email already exists
    const existingAdmin = await Admin.findOne({ email });
    if (existingAdmin) {
      console.log('❌ An admin with this email already exists.');
      process.exit(1);
    }
    
    let password: string;
    do {
      password = await questionHidden('Password (min 8 characters): ');
      if (!isValidPassword(password)) {
        console.log('❌ Password must be at least 8 characters long. Please try again.');
      }
    } while (!isValidPassword(password));
    
    const confirmPassword = await questionHidden('Confirm Password: ');
    
    if (password !== confirmPassword) {
      console.log('❌ Passwords do not match.');
      process.exit(1);
    }
    
    const department = await question('Department (optional, default: System Administration): ') || 'System Administration';
    
    console.log('\n📝 Creating super admin...');
    
    // Create super admin
    const superAdmin = new Admin({
      firstname,
      lastname,
      email,
      password, // Will be hashed by pre-save middleware
      role: 'admin',
      isSuperAdmin: true,
      adminLevel: 5, // Highest level
      department,
      permissions: Object.values(AdminPermission), // All permissions
      isActive: true,
      createdBy: null, // Bootstrap user has no creator
      settings: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        emailNotifications: {
          newTutorApplications: true,
          flaggedContent: true,
          systemAlerts: true,
          financialReports: true
        },
        dashboardPreferences: {
          defaultView: 'overview',
          autoRefresh: true,
          refreshInterval: 300
        }
      }
    });
    
    await superAdmin.save();
    
    console.log('\n✅ Super Admin created successfully!');
    console.log('================================');
    console.log(`Name: ${firstname} ${lastname}`);
    console.log(`Email: ${email}`);
    console.log(`Department: ${department}`);
    console.log(`Admin Level: 5 (Super Admin)`);
    console.log(`Permissions: All permissions granted`);
    console.log('\n🔑 Next Steps:');
    console.log('1. Login using the email and password you provided');
    console.log('2. Access: POST /api/auth/login');
    console.log('3. Use the returned token for admin endpoints');
    console.log('4. Access admin dashboard: GET /api/admin/dashboard');
    console.log('\n📚 Example login request:');
    console.log(`curl -X POST http://localhost:8000/api/auth/login \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"email":"${email}","password":"YOUR_PASSWORD"}'`);
    
  } catch (error: any) {
    console.error('\n❌ Error creating super admin:', error.message);
    process.exit(1);
  } finally {
    rl.close();
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
  }
};

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n\n👋 Script terminated by user');
  rl.close();
  await mongoose.disconnect();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  createSuperAdmin().catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export default createSuperAdmin;
