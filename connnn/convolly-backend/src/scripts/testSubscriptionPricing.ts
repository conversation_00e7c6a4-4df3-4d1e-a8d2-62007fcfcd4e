#!/usr/bin/env ts-node

/**
 * Test script to demonstrate the new subscription pricing calculation
 * with tutor basePrice and 10% processing fee
 */

import { calculateSubscriptionPricing, getAllSubscriptionPlans } from '../middlewares/getSubscriptionPricing';

// Test function to demonstrate pricing calculations
function testPricingCalculations() {
  console.log('='.repeat(60));
  console.log('SUBSCRIPTION PRICING TEST WITH 10% PROCESSING FEE');
  console.log('='.repeat(60));
  
  // Test different tutor base prices
  const testTutors = [
    { name: 'Budget Tutor', basePrice: 15 },
    { name: 'Standard Tutor', basePrice: 25 },
    { name: 'Premium Tutor', basePrice: 40 },
    { name: 'Expert Tutor', basePrice: 60 }
  ];

  testTutors.forEach(tutor => {
    console.log(`\n📚 ${tutor.name} (Base Price: $${tutor.basePrice}/lesson)`);
    console.log('-'.repeat(50));
    
    const plans = getAllSubscriptionPlans(tutor.basePrice);
    
    plans.forEach(plan => {
      const baseMonthlyPrice = tutor.basePrice * plan.lessonsPerWeek * 4;
      const processingFee = baseMonthlyPrice * 0.10;
      const totalPrice = plan.monthlyPrice / 100; // Convert from cents
      
      console.log(`${plan.lessonsPerWeek} lesson${plan.lessonsPerWeek > 1 ? 's' : ''}/week:`);
      console.log(`  Base Price: $${baseMonthlyPrice.toFixed(2)}/month`);
      console.log(`  Processing Fee (10%): $${processingFee.toFixed(2)}`);
      console.log(`  Total Monthly Price: $${totalPrice.toFixed(2)}`);
      console.log('');
    });
  });

  // Test specific calculation
  console.log('\n' + '='.repeat(60));
  console.log('DETAILED CALCULATION EXAMPLE');
  console.log('='.repeat(60));
  
  const exampleTutorPrice = 30;
  const exampleLessonsPerWeek = 3;
  
  console.log(`\nExample: Tutor charges $${exampleTutorPrice}/lesson, student wants ${exampleLessonsPerWeek} lessons/week`);
  console.log('-'.repeat(50));
  
  const result = calculateSubscriptionPricing(exampleLessonsPerWeek, exampleTutorPrice);
  const basePrice = exampleTutorPrice * exampleLessonsPerWeek * 4;
  const processingFee = basePrice * 0.10;
  const totalPrice = result.monthlyPrice / 100;
  
  console.log(`Step 1: Base calculation = $${exampleTutorPrice} × ${exampleLessonsPerWeek} lessons × 4 weeks = $${basePrice}`);
  console.log(`Step 2: Processing fee = $${basePrice} × 10% = $${processingFee.toFixed(2)}`);
  console.log(`Step 3: Total monthly price = $${basePrice} + $${processingFee.toFixed(2)} = $${totalPrice.toFixed(2)}`);
  
  console.log('\n' + '='.repeat(60));
  console.log('COMPARISON: OLD vs NEW PRICING');
  console.log('='.repeat(60));
  
  // Old fixed pricing
  const oldPricing = {
    1: 120,
    2: 220,
    3: 310,
    5: 480
  };
  
  console.log('\nOLD SYSTEM (Fixed Pricing):');
  Object.entries(oldPricing).forEach(([lessons, price]) => {
    console.log(`  ${lessons} lesson${parseInt(lessons) > 1 ? 's' : ''}/week: $${price}/month`);
  });
  
  console.log('\nNEW SYSTEM (Tutor-based + 10% fee):');
  console.log('Example with $25/lesson tutor:');
  [1, 2, 3, 5].forEach(lessons => {
    const result = calculateSubscriptionPricing(lessons, 25);
    const totalPrice = result.monthlyPrice / 100;
    console.log(`  ${lessons} lesson${lessons > 1 ? 's' : ''}/week: $${totalPrice.toFixed(2)}/month`);
  });
  
  console.log('\n✅ Test completed successfully!');
}

// Run the test
if (require.main === module) {
  testPricingCalculations();
}

export { testPricingCalculations };
