#!/usr/bin/env ts-node

/**
 * Test script to demonstrate the new booking endpoints with time and date information
 * This script shows example API calls and expected responses
 */

import axios from 'axios';

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/bookings`;

// Mock JWT tokens (replace with real tokens in actual testing)
const STUDENT_TOKEN = 'student-jwt-token-here';
const TUTOR_TOKEN = 'tutor-jwt-token-here';
const ADMIN_TOKEN = 'admin-jwt-token-here';

/**
 * Test function to demonstrate API calls
 */
async function demonstrateBookingAPIs() {
  console.log('='.repeat(80));
  console.log('BOOKING API WITH TIME & DATE INFORMATION - DEMONSTRATION');
  console.log('='.repeat(80));
  
  console.log('\n📚 Available Endpoints:');
  console.log('1. GET /api/bookings/student - Get student bookings');
  console.log('2. GET /api/bookings/tutor-detailed - Get tutor bookings');
  console.log('3. GET /api/bookings/upcoming - Get upcoming bookings');
  console.log('4. GET /api/bookings/student/:studentId - Admin get student bookings');
  console.log('5. GET /api/bookings/tutor-detailed/:tutorId - Admin get tutor bookings');

  console.log('\n' + '='.repeat(80));
  console.log('EXAMPLE API CALLS');
  console.log('='.repeat(80));

  // Example 1: Student getting their own bookings
  console.log('\n📖 Example 1: Student Getting Their Own Bookings');
  console.log('-'.repeat(50));
  console.log('Request:');
  console.log(`GET ${API_BASE}/student?status=scheduled&page=1&limit=5`);
  console.log('Headers: { Authorization: "Bearer student-jwt-token" }');
  
  console.log('\nExpected Response:');
  console.log(JSON.stringify({
    success: true,
    data: {
      bookings: [
        {
          id: "lesson_123",
          title: "Math Tutoring Session",
          scheduledTime: "2024-01-15T14:00:00.000Z",
          duration: 60,
          status: "scheduled",
          isFreeTrial: false,
          tutor: {
            id: "tutor_456",
            name: "John Smith",
            email: "<EMAIL>",
            subjects: ["Mathematics", "Physics"],
            basePrice: 25
          },
          endTime: "2024-01-15T15:00:00.000Z",
          dayOfWeek: "Monday",
          formattedDate: "January 15, 2024",
          formattedTime: "02:00 PM"
        }
      ],
      pagination: {
        page: 1,
        limit: 5,
        total: 12,
        pages: 3
      },
      summary: {
        totalBookings: 12,
        upcomingBookings: 3,
        completedBookings: 8,
        cancelledBookings: 1,
        freeTrialBookings: 2
      }
    }
  }, null, 2));

  // Example 2: Tutor getting their bookings
  console.log('\n📖 Example 2: Tutor Getting Their Bookings');
  console.log('-'.repeat(50));
  console.log('Request:');
  console.log(`GET ${API_BASE}/tutor-detailed?startDate=2024-01-01&endDate=2024-01-31`);
  console.log('Headers: { Authorization: "Bearer tutor-jwt-token" }');
  
  console.log('\nExpected Response:');
  console.log(JSON.stringify({
    success: true,
    data: {
      bookings: [
        {
          id: "lesson_789",
          title: "Physics Tutoring",
          scheduledTime: "2024-01-16T10:00:00.000Z",
          duration: 50,
          status: "confirmed",
          isFreeTrial: false,
          student: {
            id: "student_101",
            name: "Jane Doe",
            email: "<EMAIL>",
            learningReasons: ["Academic improvement", "Test preparation"]
          },
          endTime: "2024-01-16T10:50:00.000Z",
          dayOfWeek: "Tuesday",
          formattedDate: "January 16, 2024",
          formattedTime: "10:00 AM"
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 25,
        pages: 3
      },
      summary: {
        totalBookings: 25,
        upcomingBookings: 5,
        completedBookings: 18,
        cancelledBookings: 2,
        freeTrialBookings: 3
      }
    }
  }, null, 2));

  // Example 3: Getting upcoming bookings
  console.log('\n📖 Example 3: Getting Upcoming Bookings');
  console.log('-'.repeat(50));
  console.log('Request:');
  console.log(`GET ${API_BASE}/upcoming?limit=3`);
  console.log('Headers: { Authorization: "Bearer student-jwt-token" }');
  
  console.log('\nExpected Response:');
  console.log(JSON.stringify({
    success: true,
    data: {
      upcomingBookings: [
        {
          id: "lesson_999",
          title: "Chemistry Lab Session",
          scheduledTime: "2024-01-15T16:00:00.000Z",
          duration: 90,
          status: "confirmed",
          isFreeTrial: false,
          endTime: "2024-01-15T17:30:00.000Z",
          dayOfWeek: "Monday",
          formattedDate: "January 15, 2024",
          formattedTime: "04:00 PM",
          timeUntilSession: "2 hours, 30 minutes",
          tutor: {
            id: "tutor_202",
            name: "Dr. Smith",
            email: "<EMAIL>",
            subjects: ["Chemistry", "Biology"],
            basePrice: 35
          }
        }
      ],
      count: 1,
      userRole: "student"
    }
  }, null, 2));

  console.log('\n' + '='.repeat(80));
  console.log('QUERY PARAMETERS EXAMPLES');
  console.log('='.repeat(80));

  console.log('\n🔍 Filtering Examples:');
  console.log('• Filter by status: ?status=completed');
  console.log('• Filter by date range: ?startDate=2024-01-01&endDate=2024-01-31');
  console.log('• Pagination: ?page=2&limit=20');
  console.log('• Sorting: ?sortBy=scheduledTime&sortOrder=asc');
  console.log('• Combined: ?status=scheduled&page=1&limit=5&sortBy=scheduledTime&sortOrder=desc');

  console.log('\n📊 Response Features:');
  console.log('• Detailed time information (scheduledTime, endTime, formatted times)');
  console.log('• User information (tutor/student details)');
  console.log('• Subscription information');
  console.log('• Summary statistics');
  console.log('• Pagination metadata');
  console.log('• Human-readable time formatting');

  console.log('\n' + '='.repeat(80));
  console.log('CURL COMMAND EXAMPLES');
  console.log('='.repeat(80));

  console.log('\n🌐 Student getting their bookings:');
  console.log(`curl -X GET "${API_BASE}/student?status=scheduled&page=1&limit=5" \\`);
  console.log('  -H "Authorization: Bearer your-student-jwt-token"');

  console.log('\n🌐 Tutor getting their bookings:');
  console.log(`curl -X GET "${API_BASE}/tutor-detailed?startDate=2024-01-01&endDate=2024-01-31" \\`);
  console.log('  -H "Authorization: Bearer your-tutor-jwt-token"');

  console.log('\n🌐 Getting upcoming bookings:');
  console.log(`curl -X GET "${API_BASE}/upcoming?limit=3" \\`);
  console.log('  -H "Authorization: Bearer your-jwt-token"');

  console.log('\n🌐 Admin getting specific student bookings:');
  console.log(`curl -X GET "${API_BASE}/student/student_id_here" \\`);
  console.log('  -H "Authorization: Bearer your-admin-jwt-token"');

  console.log('\n✅ Demonstration completed successfully!');
  console.log('\n📝 Note: Replace JWT tokens with actual tokens for real API testing.');
  console.log('📝 Note: Ensure the server is running on the specified BASE_URL.');
}

// Helper function to make actual API calls (commented out for demo)
async function makeAPICall(endpoint: string, token: string) {
  try {
    const response = await axios.get(`${API_BASE}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error: any) {
    console.error(`Error calling ${endpoint}:`, error.response?.data || error.message);
    return null;
  }
}

// Run the demonstration
if (require.main === module) {
  demonstrateBookingAPIs();
}

export { demonstrateBookingAPIs, makeAPICall };
