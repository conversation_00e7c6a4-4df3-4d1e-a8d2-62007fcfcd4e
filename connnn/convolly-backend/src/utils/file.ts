import path from "path";
import cloudinary from "../config/misc";
import { FileProps } from "../models/file";
import { KeyValuePair } from "../types/misc";
import { normalizeDatetime } from "./datetime";
import { logError } from "./logger";

type FileDoc = { file: FileProps; id: string } & KeyValuePair;

function getPublicIdFromUrl(secureUrl: string) {
  const parts = secureUrl.split("/");

  const filename = parts[parts.length - 1];
  const dirName = parts[parts.length - 2];

  return `${dirName}/${filename}`;
}

export const uploadFile = async (url: string): Promise<FileProps> => {
  const result = await cloudinary.uploader.upload(url, {
    folder: "uploads",
    resource_type: "auto",
  });

  const format = result.format;
  const resourceType = result.resource_type;

  return {
    url: result.secure_url,
    mimetype: `${resourceType}/${format}`,
    size: (result.bytes / 1024).toFixed(2),
  };
};

export const deleteFile = async (url: string, withError = false) => {
  try {
    await cloudinary.uploader.destroy(getPublicIdFromUrl(url));
  } catch (err) {
    logError(`Failed to delete file ${url}`, "delete");
    if (withError) throw err;
  }
};

export const uploadAndDeleteFile = async (
  uploadUrl: string,
  deleteUrl?: string
) => {
  try {
    const result = await uploadFile(uploadUrl);

    if (deleteUrl) await deleteFile(deleteUrl);

    return result;
  } catch (err: any) {
    // if (!err.message) {
    //   err.message = "Invalid upload url";
    // }

    throw err;
  }
};

export const uploadAndDeleteFiles = async (
  uploadData: FileDoc[],
  deleteData: FileDoc[] = []
) => {
  const uploads: {
    file?: FileProps;
    _id?: string;
    createdAt?: Date;
    updatedAt?: Date;
  }[] = [];

  const errors: { message: string; index: number; code: string }[] = [];

  const length = Math.max(uploadData.length, deleteData.length);

  for (let i = 0; i < length; i++) {
    try {
      const { fileUpload, ...newData } = uploadData[i] || {};

      const { file } = deleteData[i] || {};

      delete newData.createdAt;
      delete newData.updatedAt;

      let oldData;

      uploads[i] = newData;

      if (newData.id) {
        oldData = deleteData.find((item) => item.id === newData.id);

        if (!oldData) {
          throw {
            message: "Record not found.",
            code: "NOT_FOUND",
          };
        }

        if (oldData.toObject) oldData = oldData.toObject();

        uploads[i]._id = newData.id;

        if (oldData.createdAt) {
          uploads[i].createdAt = normalizeDatetime(oldData.createdAt);
          uploads[i].updatedAt = normalizeDatetime();
        }

        uploads[i] = {
          ...oldData,
          ...newData,
        };
      }

      if (fileUpload) uploads[i].file = await uploadFile(fileUpload);

      if (file) await deleteFile(file.url);
    } catch (err: any) {
      errors[i] = {
        message: err.message || `Invalid file at position ${i + 1}`,
        index: i,
        code: err.code || "INVALID_FILE",
      };
    }
  }

  return { uploads, errors };
};

export const getRelativePath = (filePath: string) => {
  return path.resolve(__dirname, filePath);
};
