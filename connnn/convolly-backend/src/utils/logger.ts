import { LOGS_DIR } from "../config/constants";
import path from "path";
import fs from "fs";
import { KeyValuePair } from "../types/misc";

type TLogErrorAction =
  | "post"
  | "get"
  | "put"
  | "delete"
  | "mail"
  | "calendar"
  | "review"
  | "admin"
  | "others";

type TErrorLogEntry = {
  timestamp: string;
  message: string;
  metadata?: Record<string, any>;
};

const ERROR_FILE_PATH = path.resolve(LOGS_DIR, "error-log.json");

export const logError = (
  error: string,
  TLogErrorAction: TLogErrorAction = "others",
  metadata: Record<string, any> = {}
): void => {
  const timestamp = new Date().toISOString();
  const logEntry: TErrorLogEntry = {
    timestamp,
    message: error,
    metadata,
  };

  writeToFile(ERROR_FILE_PATH, (logs) => {
    if (!logs[TLogErrorAction]) logs[TLogErrorAction] = [];

    logs[TLogErrorAction].push(logEntry);

    return {
      ...logs,
    };
  });
};

export const writeToFile = (
  filePath: string,
  transform: ((logs: KeyValuePair) => KeyValuePair) | KeyValuePair,
  logDir = LOGS_DIR
) => {
  let logs: KeyValuePair = {};

  if (fs.existsSync(filePath)) {
    const fileContent = fs.readFileSync(filePath, "utf8");

    try {
      logs = { ...logs, ...(fileContent ? JSON.parse(fileContent) : {}) };
    } catch (e: any) {
      console.log("⚠️ Error parsing existing log file. Starting fresh.");
    }
  } else fs.mkdirSync(logDir, { recursive: true });

  let newLogs = {};

  if (typeof transform === "function") newLogs = transform(logs);
  else
    newLogs = {
      ...logs,
      ...transform,
    };

  fs.writeFileSync(filePath, JSON.stringify(newLogs), "utf8");
};

export const getFileContent = (
  filePath: string,
  type: "json" | "utf8" = "utf8"
): KeyValuePair | string | null => {
  if (fs.existsSync(filePath)) {
    const fileContent = fs.readFileSync(filePath, "utf8");

    switch (type) {
      case "json":
        return fileContent ? JSON.parse(fileContent) : null;
      default:
        return fileContent;
    }
  }

  return null;
};
