import { DateTime, Duration } from "luxon";

export const getTimeLeft = (
  input: Date | string | number,
  timeZone: string = "UTC"
): string => {
  const now: DateTime = DateTime.now().setZone(timeZone);

  const target: DateTime =
    typeof input === "string"
      ? DateTime.fromISO(input, { zone: timeZone })
      : typeof input === "number"
      ? DateTime.fromMillis(input, { zone: timeZone })
      : DateTime.fromJSDate(new Date(input), { zone: timeZone });

  if (target <= now) return "Expired";

  const diff: Duration = target
    .diff(now, ["days", "hours", "minutes", "seconds"])
    .normalize();

  const days: number = Math.floor(diff.days);
  const hours: number = Math.floor(diff.hours);
  const minutes: number = Math.floor(diff.minutes);

  if (days > 0) return `${days} day${days === 1 ? "" : "s"}`;
  if (hours > 0) return `${hours} hour${hours === 1 ? "" : "s"}`;
  if (minutes > 0) return `${minutes} minute${minutes === 1 ? "" : "s"}`;

  return "Less than a minute";
};

const getOrdinalSuffix = (day: number): string => {
  const suffixes: { [key: number]: string } = { 1: "st", 2: "nd", 3: "rd" };
  const mod10 = day % 10;
  const mod100 = day % 100;
  return mod10 > 3 || [11, 12, 13].includes(mod100)
    ? "th"
    : suffixes[mod10] || "th";
};

const getTimeZoneAbbreviation = (date: Date, timeZone?: string): string => {
  const options: Intl.DateTimeFormatOptions = {
    timeZone,
    timeZoneName: "short",
  };
  const formatter: Intl.DateTimeFormat = new Intl.DateTimeFormat(
    "en-US",
    options
  );
  const parts: Intl.DateTimeFormatPart[] = formatter.formatToParts(date);
  const tzPart: Intl.DateTimeFormatPart | undefined = parts.find(
    (p) => p.type === "timeZoneName"
  );
  const tzAbbr: string = tzPart?.value || "";
  return tzAbbr.replace(/^GMT[+-]\d+/, "").trim(); // Strip fallback GMT offset if needed
};

export const getDateTime = (
  input: Date | string | number,
  timeZone?: string
): string => {
  const date: Date = new Date(input);

  const options: Intl.DateTimeFormatOptions = {
    timeZone: timeZone || Intl.DateTimeFormat().resolvedOptions().timeZone,
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  };

  const formatter: Intl.DateTimeFormat = new Intl.DateTimeFormat(
    "en-US",
    options
  );
  const parts: Intl.DateTimeFormatPart[] = formatter.formatToParts(date);

  const month: string = parts.find((p) => p.type === "month")?.value || "";
  const day: number = parseInt(
    parts.find((p) => p.type === "day")?.value || "0",
    10
  );
  const ordinal: string = getOrdinalSuffix(day);
  const year: string = parts.find((p) => p.type === "year")?.value || "";
  const hour: string = parts.find((p) => p.type === "hour")?.value || "";
  const minute: string = parts.find((p) => p.type === "minute")?.value || "";
  const period: string =
    parts.find((p) => p.type === "dayPeriod")?.value.toLowerCase() || "";
  const abbr: string = getTimeZoneAbbreviation(date, timeZone);

  return `${month} ${day}${ordinal}, ${year}, ${hour}:${minute} ${period} ${abbr}`;
};
