import { IAdmin } from "../../models/admin";
import { IStudent } from "../../models/student";
import { ITutor } from "../../models/tutor";

// Simplified interfaces for email templates
interface EmailUser {
  firstname: string;
  lastname: string;
  email: string;
  fullname?: string;
  timezone?: string;
  location?: {
    continent?: string;
    state?: string;
  };
}
import { getDateTime, getTimeLeft } from "../date";
import { getRelativePath } from "../file";
import { getFileContent } from "../logger";
import { MAIL_ALIAS, SendMailParams } from "../mailer";
import { capitalize, replaceLastOf } from "../misc";
import { USER_PROFILE_TYPE } from "../profile";

export const EMAIL_TEST_BODY = `
  <tr>
          <td style="padding: 30px; color: #333333">
                <h1 style="margin-top: 0">Welcome to Convolly!</h1>
                <p>
                  Whether you're here to teach or learn, you're in the right
                  place. We're building the future of online learning and
                  communication — one brilliant conversation at a time.
                </p>
                <p>
                  🚀 Pro tip: Start by exploring available tutors or scheduling
                  your first class today!
                </p>
              </td>
            </tr>
`;

export const renderEmailTemplate = (body: string) => {
  const email = getFileContent(
    getRelativePath("./emails-templates/index.html")
  )!;

  return email.toString().replace(/{{\s*['"]body-content['"]\s*}}/gi, body);
};

const renderUsername = (user: USER_PROFILE_TYPE | EmailUser) => `
<p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Hi <strong>${user.firstname}</strong>,
                </p>
`;

// student emails

export const studentWelcomeTemplate = (student: IStudent) => ({
  subject: " Welcome to Convolly – Let’s get learning!",
  html: renderEmailTemplate(
    `
      <tr>
        <td align="center">
          <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
            <tr>
              <td>
                ${renderUsername(student)}
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Welcome to <strong>Convolly</strong>, where expert tutors and curious minds meet.
                  We’re excited to help you reach your learning goals.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Here’s how to get started:
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  <strong>1. Find a Tutor:</strong> Explore our verified tutor profiles and filter by subject, availability, and reviews.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  <strong>2. Book a Trial Lesson:</strong> Try a free or discounted session to see if it’s the right fit.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  <strong>3. Need Help?</strong> Visit our Help Center or reach out to <a href="mailto:<EMAIL>" style="color:#1a0dab;"><EMAIL></a>.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Let’s make learning easy and enjoyable. 🚀
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    `
  ),
});

export const paymentSuccessTemplate = (
  student: EmailUser | IStudent,
  amountAndCurrency: string,
  viewReceiptLink: string
) => ({
  subject: "Payment Successful – Thank you!",
  html: renderEmailTemplate(`
     <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                  ${renderUsername(student)}
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your payment of <strong>${amountAndCurrency}</strong> was successful. 🎉
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      You can now schedule your lessons on Convolly.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🧾 <a href="${viewReceiptLink}" style="color:#1a0dab;">View Receipt</a>
                    </p> 
                  </td>
                </tr>
              </table>
            </td>
          </tr>
  `),
});

export const guideToFindTutorTemplate = (student: IStudent) => ({
  subject: "Your Guide to Finding the Perfect Tutor",
  html: renderEmailTemplate(`
     <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    ${renderUsername(student)}
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Here are 3 simple tips to get the most out of <strong>Convolly</strong>:
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      <strong>1. Be clear about your goals –</strong> Whether it's exams, fluency, or career, the right tutor can tailor your lessons.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      <strong>2. Use filters wisely –</strong> Sort by availability, native language, or specialties.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      <strong>3. Start with a trial –</strong> A trial session helps build trust and understanding.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Book your first lesson today and start learning smarter.
                    </p> 
                  </td>
                </tr>
  `),
});

export const lessonConfirmationTemplate = (
  student: EmailUser | IStudent,
  tutor: EmailUser | ITutor,
  lessonDate: Date,
  lessonLink: string
) => ({
  subject: "Your lesson is confirmed ✅",
  html: renderEmailTemplate(`
 <tr>
                  <td>
                  ${
                    (student as any).firstname
                      ? renderUsername(student as any)
                      : `${student.firstname} ${student.lastname}`
                  }
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your session with <strong>${
                        tutor.firstname
                      }</strong> is confirmed for:
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      📅 <strong>${getDateTime(
                        lessonDate,
                        `${(tutor as any).location?.continent || "Unknown"}/${
                          (tutor as any).location?.state || "Unknown"
                        }`
                      )}</strong>
                      📍 Online via Convolly (Join link below)
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🔗 <a href="${lessonLink}" style="color:#1a0dab;">Lesson Link</a>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      We recommend arriving 5 minutes early. Looking forward to your session!
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
  `),
});

export const studentLessonReminderTemplate = (
  student: IStudent,
  tutor: ITutor,
  lessonDate: Date,
  lessonLink: string
) => {
  const timeLeft = getTimeLeft(lessonDate, tutor.timezone);

  return {
    subject: `Reminder: Your lesson is in ${timeLeft}`,
    html: renderEmailTemplate(`
      <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    ${renderUsername(student)}
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your lesson with <strong>${
                        tutor.fullname
                      }</strong> is coming up:
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🗓️ <strong>${getDateTime(
                        lessonDate,
                        tutor.timezone
                      )}</strong><br />
                      ⏰ <strong>${timeLeft} left</strong>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🔗 <a href="${lessonLink}" style="color:#1a0dab;">Lesson Link</a>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Don't forget to bring any materials or questions you have.
                    </p> 
                  </td>
                </tr>
              </table>
            </td>
          </tr>
  `),
  };
};

export const studentLessonStatusTemplate = (
  student: EmailUser | IStudent,
  tutor: EmailUser | ITutor,
  status: string,
  newDate?: Date
) => ({
  subject: `Your lesson has been ${capitalize(status)}`,
  html: renderEmailTemplate(
    `
        <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                   ${
                     (student as any).firstname
                       ? renderUsername(student as any)
                       : `${student.firstname} ${student.lastname}`
                   }
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your upcoming lesson with <strong>${
                        tutor.fullname
                      }</strong> has been <strong>${status.toLowerCase()}</strong>.
                    </p>
                    ${
                      newDate
                        ? `<p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      📅 <strong>New Date: ${getDateTime(
                        newDate,
                        tutor.timezone
                      )}</strong>
                    </p>`
                        : ""
                    }
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Visit your dashboard to manage your lessons or find another tutor.
                    </p> 
                  </td>
                </tr>
      
    `
  ),
});

export const subscriptionPausedTemplate = (
  student: IStudent,
  status: string
) => ({
  subject: `Your subscription is now ${capitalize(status)}`,
  html: renderEmailTemplate(`
   <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                  ${renderUsername(student)}
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      We’ve confirmed your request to <strong>${status.toLowerCase()}</strong> your subscription.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      You can always return when you're ready to continue your learning journey.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Need help? Contact us at <a href="mailto:${
                        MAIL_ALIAS["support"]
                      }" style="color:#1a0dab;">${MAIL_ALIAS["support"]}</a>.
                    </p> 
                  </td>
                </tr>
              </table>
            </td>
          </tr>
  `),
});

export const inActiveStudentReminderTemplate = (
  student: IStudent,
  bookLessonLink: string
) => ({
  subject: "We miss you! Ready to book your next lesson?",
  html: renderEmailTemplate(`
   <tr>
              <td align="center">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                  <tr>
                    <td>
                     ${renderUsername(student)}
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        It’s been a while! Your progress matters, and your tutor is waiting.
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        🏁 Pick up where you left off and reach your goals.
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        📚 <a href="${bookLessonLink}" style="color:#1a0dab;">Book a Lesson</a>
                      </p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>   
  `),
});

export const lowLessonBalanceTemplate = (
  student: IStudent,
  lessonLeft: number,
  buyLessonLink: string
) => ({
  subject: `You have ${lessonLeft} lesson left! ⏳`,
  html: renderEmailTemplate(`
   <tr>
              <td align="center">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                  <tr>
                    <td>
                    ${renderUsername(student)}
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        You're ${
                          lessonLeft > 1 ? "almost down" : "down"
                        } to your last lesson. Don’t miss out – top up now to continue seamlessly.
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        ➕ <a href="${buyLessonLink}" style="color:#1a0dab;">Buy More Lessons</a>
                      </p> 
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
    `),
});

// tutor emails

export const tutorWelcomeTemplate = (
  tutor: ITutor,
  onBoardingLeft?: string[]
) => {
  const text = onBoardingLeft ? onBoardingLeft.join(",") : "";
  const onBoardingText = replaceLastOf(text, ",", " and");

  return {
    subject: "Welcome to Convolly – Let’s find you students!",
    html: renderEmailTemplate(` 
    <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td> 
                  ${renderUsername(tutor)}
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      You’re officially a Convolly tutor – congrats! 🎉
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Here’s how to get started:
                    </p>
                    <ul style="font-family:Arial, sans-serif; font-size:16px; color:#333333; padding-left:20px;">
 ${
   onBoardingLeft
     ? `                      <li>Complete Your Profile: Add ${onBoardingText}.</li>`
     : ""
 }                      <li>Respond Quickly: First impressions matter. Respond fast to student messages.</li>
                      <li>Offer a Trial: Help students get to know you.</li>
                    </ul>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Let’s make learning rewarding—for both you and your students.
                    </p> 
                  </td>
                </tr>
              </table>
            </td>
          </tr>
    `),
  };
};

export const tutorApprovalStatusTemplate = (
  tutor: EmailUser | ITutor,
  status: "approved" | "rejected",
  rejectionFeedback?: string,
  onBoardingLink?: string
) => {
  const isApproved = status === "approved";

  return {
    subject: `Your profile has been ${capitalize(status)}`,
    html: renderEmailTemplate(
      `
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                   ${renderUsername(tutor)}
                    <p>Your tutor profile has been <strong>${status.toLowerCase()}</strong>.</p>
                     ${
                       isApproved
                         ? `<p>Start accepting bookings today!</p>`
                         : `
                    <p>Here’s what needs fixing:</p>
                    <p>
                    ${rejectionFeedback}
                    </p>
                    
                    <p>
                      <a href="${onBoardingLink}" style="color:#1a0dab;">Update Profile</a>
                    </p> 
                    `
                     } 
                  </td>
                </tr>
              </table>
            </td>
          </tr>
    
    `
    ),
  };
};

export const lessonBookingTemplate = (
  tutor: EmailUser | ITutor,
  student: EmailUser | IStudent,
  lessonDate: Date,
  viewBookingLink: string
) => ({
  subject: "You have a new lesson booking!",
  html: renderEmailTemplate(`
     <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                   ${renderUsername(tutor)}
                    <p>
                      <strong>${
                        student.fullname
                      }</strong> has booked a lesson with you: 🗓️ <strong>${getDateTime(
    lessonDate
  )}</strong>
                    </p>
                    <p>⏰ Check your calendar and be ready to teach.</p>
                    <p>
                      🔗 <a href="${viewBookingLink}" style="color:#1a0dab;">View Booking</a>
                    </p> 
                  </td>
                </tr>
              </table>
            </td>
          </tr>
  `),
});

export const tutorLessonReminder = (
  tutor: ITutor,
  student: IStudent,
  lessonDate: Date
) => ({
  subject: " Reminder: You have a lesson coming up",
  html: renderEmailTemplate(`
 <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    ${renderUsername(tutor)}
                  <p>
                  Don’t forget – you’ve got a lesson with <strong>${
                    student.fullname
                  }</strong>: 📅 ${getDateTime(lessonDate, tutor.timezone)}
🔗 [Join Lesson Room]

                  </p>
                    <p>Arrive early and bring your A-game. 🎯</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
  `),
});

export const lessonCancelledByStudent = (
  tutor: ITutor,
  student: IStudent,
  status: "cancelled" | "rescheduled",
  newLessonDate?: Date
) => {
  const isRescheduled = status === "rescheduled";

  return {
    subject: `Lesson update: ${capitalize(status)} by student`,
    html: renderEmailTemplate(`
    <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    ${renderUsername(tutor)}
                    <p><strong>${
                      student.fullname
                    }</strong> has <strong>${status.toLowerCase()}</strong> your upcoming lesson.</p>
                   ${
                     isRescheduled
                       ? `
                     <p>📆 New Date: <strong>${getDateTime(
                       newLessonDate!
                     )}</strong></p>
                    <p>Check your calendar for more details.</p>
                    `
                       : ""
                   }
                   </td>
                </tr>
              </table>
            </td>
          </tr>
          `),
  };
};

export const payoutConfirmationTemplate = (
  tutor: EmailUser | ITutor,
  amountAndCurrency: string,
  paymentDate: Date,
  paymentMethod: string
) => ({
  subject: "You've been paid 💸",
  html: renderEmailTemplate(`
     <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    ${renderUsername(tutor)}
                    <p>We’ve sent your payout for completed lessons.</p>
                    <p>💰 <strong>Amount:</strong> ${amountAndCurrency}</p>
                    <p>📆 <strong>Payout Date:</strong> ${getDateTime(
                      paymentDate
                    )}</p>
                    <p>💳 <strong>Method:</strong> ${paymentMethod}</p>
                    <p>Thank you for being part of Convolly!</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
    `),
});

// admin emails

export const approveTutorTemplate = (
  admin: IAdmin,
  tutor: ITutor,
  isOnboarding = true
) => ({
  subject: "Tutor Profile Awaiting Approval",
  html: renderEmailTemplate(`
       <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  ${renderUsername(admin)}
                    <p>${
                      isOnboarding
                        ? "A new tutor has completed onboarding"
                        : `Tutor ${tutor.firstname} has made changes to their profile`
                    } and is awaiting approval.</p>
                    <p><strong>Profile ID:</strong> ${tutor.id}</p>
                    <p><strong>Profile Email:</strong> ${tutor.email}</p>
                    <p>Please review and approve the profile in the admin dashboard.</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
    `),
});

// general emails

export const subscriptionReminderTemplate = (
  user: USER_PROFILE_TYPE,
  endDate: Date,
  renewLink: string
) => ({
  subject: "Your subscription is ending soon",
  html: renderEmailTemplate(` 
      <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
 ${renderUsername(user)}
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your subscription will expire on <strong>${getDateTime(
                        endDate,
                        user.timezone
                      )}</strong>. Renew now to avoid interruptions in your lessons.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🛒 <a href="${renewLink}" style="color:#1a0dab;">Click here to Renew.</a>
                    </p>
                  </td>
                </tr>
    `),
});

export const paymentReceiptTemplate = (
  user: USER_PROFILE_TYPE,
  amountAndCurrency: string,
  paymentDate: string,
  paymentMethod: string,
  transactionId: string,
  attachments: Required<SendMailParams["attachments"]>
) => ({
  attachments,
  subject: "Here’s your receipt from Convolly",
  html: renderEmailTemplate(`
    <tr>
  <td style="padding: 20px">
    ${renderUsername(user)}
    <p style="font-size: 14px; color: #1a1a1a; margin: 0 0 20px">
      Thank you for your payment. Here are your billing details:
    </p>

    <table
      cellpadding="0"
      cellspacing="0"
      border="0"
      style="font-size: 14px; color: #1a1a1a; margin-bottom: 20px"
    >
      <tr>
        <td style="padding: 5px 0; font-weight: bold">Amount Paid:</td>
        <td style="padding: 5px 0">${amountAndCurrency}</td>
      </tr>
      <tr>
        <td style="padding: 5px 0; font-weight: bold">Payment Method:</td>
        <td style="padding: 5px 0">${paymentMethod}</td>
      </tr>
      <tr>
        <td style="padding: 5px 0; font-weight: bold">Date:</td>
        <td style="padding: 5px 0">${paymentDate}</td>
      </tr>
      <tr>
        <td style="padding: 5px 0; font-weight: bold">Transaction ID:</td>
        <td style="padding: 5px 0">${transactionId}</td>
      </tr>
    </table>
  </td>
</tr>
    `),
});

export const upcomingBillingTemplate = (
  user: USER_PROFILE_TYPE,
  timeLeft: Date,
  nextBillingDate: Date,
  amountAndCurrency: string,
  manageSubscriptionLink: string
) => ({
  subject: "Your subscription renews soon",
  html: renderEmailTemplate(`
    <tr>
  <td style="padding: 20px">
     ${renderUsername(user)}
    <p style="font-size: 14px; color: #1a1a1a; margin: 0 0 20px">
      Just a heads-up – your Convolly subscription will renew in ${getTimeLeft(
        timeLeft
      )}
    </p>

    <table
      cellpadding="0"
      cellspacing="0"
      border="0"
      style="font-size: 14px; color: #1a1a1a; margin-bottom: 20px"
    >
      <tr>
        <td style="padding: 5px 0">💳 <strong>Next Billing Date:</strong></td>
        <td style="padding: 5px 0">${getDateTime(nextBillingDate)}</td>
      </tr>
      <tr>
        <td style="padding: 5px 0">💰 <strong>Amount:</strong></td>
        <td style="padding: 5px 0">${amountAndCurrency}</td>
      </tr>
    </table>

    <p style="font-size: 14px; margin: 0">
      Want to make changes? <a
        href="${manageSubscriptionLink}"
        target="_blank"
        style="color: #1a174e; text-decoration: underline"
      >Manage Subscription</a>
    </p>
  </td>
</tr>

    `),
});
