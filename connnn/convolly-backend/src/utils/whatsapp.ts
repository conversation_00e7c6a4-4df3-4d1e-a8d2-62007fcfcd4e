import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Sends a WhatsApp message through an API service
 * @param phoneNumber - The recipient's phone number with country code (e.g., +1234567890)
 * @param message - The message text to send
 */
export const sendWhatsAppMessage = async (phoneNumber: string, message: string): Promise<void> => {
  try {
    // This implementation uses WhatsApp Business API
    // You'll need to configure your .env with the appropriate credentials
    const whatsappApiUrl = process.env.WHATSAPP_API_URL;
    const whatsappApiKey = process.env.WHATSAPP_API_KEY;
    
    if (!whatsappApiUrl || !whatsappApiKey) {
      throw new Error('WhatsApp API configuration is missing');
    }
    
    // Remove any spaces or special characters from the phone number
    const sanitizedPhoneNumber = phoneNumber.replace(/\s+/g, '');
    
    // Make the API request to send the WhatsApp message
    const response = await axios.post(
      whatsappApiUrl,
      {
        phone: sanitizedPhoneNumber,
        message: message,
        type: 'text'
      },
      {
        headers: {
          'Authorization': `Bearer ${whatsappApiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.status !== 200) {
      throw new Error(`Failed to send WhatsApp message: ${response.data}`);
    }
    
    console.log(`WhatsApp message sent successfully to ${phoneNumber}`);
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    throw new Error('Failed to send password reset token via WhatsApp');
  }
};

// Alternative implementation using Twilio WhatsApp API
export const sendWhatsAppMessageViaTwilio = async (phoneNumber: string, message: string): Promise<void> => {
  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const fromNumber = process.env.TWILIO_WHATSAPP_NUMBER;
    
    if (!accountSid || !authToken || !fromNumber) {
      throw new Error('Twilio configuration is missing');
    }
    
    const client = require('twilio')(accountSid, authToken);
    
    // Format for Twilio WhatsApp messaging
    const formattedFromNumber = fromNumber.startsWith('whatsapp:') ? fromNumber : `whatsapp:${fromNumber}`;
    const formattedToNumber = phoneNumber.startsWith('whatsapp:') ? phoneNumber : `whatsapp:${phoneNumber}`;
    
    await client.messages.create({
      body: message,
      from: formattedFromNumber,
      to: formattedToNumber
    });
    
    console.log(`WhatsApp message sent successfully via Twilio to ${phoneNumber}`);
  } catch (error) {
    console.error('Error sending WhatsApp message via Twilio:', error);
    throw new Error('Failed to send password reset token via WhatsApp');
  }
};