const  validateCardDetails = (cardDetails: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!cardDetails.number || cardDetails.number.length < 13) {
    errors.push('Invalid card number');
  }
  
  if (!cardDetails.exp_month || cardDetails.exp_month < 1 || cardDetails.exp_month > 12) {
    errors.push('Invalid expiration month');
  }
  
  if (!cardDetails.exp_year || cardDetails.exp_year < new Date().getFullYear()) {
    errors.push('Invalid expiration year');
  }
  
  if (!cardDetails.cvc || cardDetails.cvc.length < 3) {
    errors.push('Invalid CVC');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

export default validateCardDetails