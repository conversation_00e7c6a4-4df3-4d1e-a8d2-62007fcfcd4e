import { Model } from "mongoose";
import Tu<PERSON>, { ITutor } from "../models/tutor";
import Student, { IStudent } from "../models/student";
import { IProfile } from "../models/profile";
import { KeyValuePair } from "../types/misc";
import Admin, { IAdmin } from "../models/admin";
import { getErrorResponse } from "../middlewares/errorHandler";
import { ERROR_INVALID_ROLE } from "../config/constants";
import { uploadAndDeleteFile, uploadAndDeleteFiles } from "./file";
import mongoose from "mongoose";

export type USER_PROFILE_TYPE = IAdmin | ITutor | IStudent;

export type GetProfileProps = {
  email?: string;
  id?: string | mongoose.Types.ObjectId;
  role?: IProfile["role"];
};

export const safeUserUpdateBody = (user: Partial<IProfile>, strict = true) => {
  const body: Partial<IProfile> = {
    firstname: user.firstname,
    lastname: user.lastname,
  };

  if (!strict) {
    body.email = user.email;
  }

  return body;
};

export function getProfileModelByRole(role: "student"): Model<IStudent>;
export function getProfileModelByRole(role: "tutor"): Model<ITutor>;
export function getProfileModelByRole(role: "admin"): Model<IAdmin>;

export function getProfileModelByRole(
  role: IProfile["role"]
): Model<IStudent> | Model<ITutor> | Model<IAdmin> {
  const modelMap = {
    student: Student,
    tutor: Tutor,
    admin: Admin,
  };

  const model = modelMap[role];

  if (!model) throw getErrorResponse(ERROR_INVALID_ROLE);

  return model;
}

export const getProfile = async (
  { email, id, role }: GetProfileProps,
  opts?: { strict?: boolean; sort?: KeyValuePair }
): Promise<USER_PROFILE_TYPE | null> => {
  const query: KeyValuePair = {};

  if (email) query.email = email;
  else if (id) query._id = id;
  else throw getErrorResponse("Invalid body, user identity is unknown", 400);

  if (role) {
    const model = getProfileModelByRole(role as any);
    return await model.findOne(query);
  } else {
    const student = await Student.findOne(query);

    if (student) return student;

    const tutor = await Tutor.findOne(query);
    if (tutor) return tutor;

    // Check all three models when no role is specified
    const admin = await Admin.findOne(query);
    if (admin) return admin;

    return null;
  }
};

export const getProfiles = async (
  query: KeyValuePair,
  opts?: { select?: string }
) => {
  let profiles: USER_PROFILE_TYPE[] = [];

  // Include admin profiles in the search
  profiles = await Admin.find(query).select(opts?.select || "");

  profiles = profiles.concat(
    await Tutor.find(query).select(opts?.select || "")
  );

  profiles = profiles.concat(
    await Student.find(query).select(opts?.select || "")
  );

  return profiles; // Added return statement
};

export type UPLOAD_ERROR_DETAILS = null | KeyValuePair;

export const updateProfileMedias = async (
  body: any,
  user: USER_PROFILE_TYPE | null,
  withError = false
): Promise<UPLOAD_ERROR_DETAILS> => {
  let errorDetails: KeyValuePair | null = null;

  const uploadSingle = async (key: string) => {
    if (body[key]) {
      try {
        body[key] = (
          await uploadAndDeleteFile(body[key], user?.[key as keyof typeof user])
        ).url;
      } catch (err: any) {
        if (!errorDetails) errorDetails = {};
        errorDetails[key] = {
          message: err.message || `Invalid upload URL for ${key}`,
          path: `${key}`,
        };
      }
    }
  };

  const handleUpdate = async (key: string) => {
    if (Array.isArray(body[key])) {
      const result = await uploadAndDeleteFiles(
        body[key],
        user![key as keyof typeof user]
      );

      body[key] = result.uploads;

      if (result.errors.length) {
        if (!errorDetails) errorDetails = {};
        errorDetails[key] = result.errors;
      }
    }
  };

  await uploadSingle("image");

  if (user) {
    switch (user.role) {
      case "tutor":
        await uploadSingle("introVideo");
        await handleUpdate("certificates");
        await handleUpdate("academics");
        break;
      case "admin":
        // Add any admin-specific media handling if needed
        // For now, only handling the common "image" field
        break;
      case "student":
        // Add any student-specific media handling if needed
        // For now, only handling the common "image" field
        break;
    }
  }

  if (withError && errorDetails)
    throw getErrorResponse({
      status: 400,
      details: errorDetails,
      message: "Invalid upload file",
    });

  return errorDetails;
};
