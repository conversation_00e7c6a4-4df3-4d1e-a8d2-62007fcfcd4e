import { KeyValuePair } from "../types/misc";
import path from "path";
import { LOGS_DIR } from "../config/constants";
import { getFileContent, writeToFile } from "./logger";
import { handleExitSignals } from "./error";
import { SOCKET_ERROR_KEY } from "../config/socket/server-socket";
import { getErrorResponse } from "../middlewares/errorHandler";
import { Server, Socket } from "socket.io";
import { ensureUnder1MB } from "./misc";

const LOG_FILE_PATH = path.resolve(LOGS_DIR, "socket-state.json");

export type TSocketData = {} & KeyValuePair;

export const registerSocket = (
  socketId: string,
  userId: string,
  data: TSocketData
) => {
  const socketData = getSocketData(userId);

  const hasData = !!socketData;

  writeToFile(LOG_FILE_PATH, (logs) => {
    if (hasData) {
      delete logs[socketData.socketId];
      delete logs[userId];
    }

    return {
      ...logs,
      [socketId]: data,
      [userId]: {
        ...data,
        socketId,
        channelId: data.channelId,
      },
    };
  });

  return hasData;
};

export const unRegisterSocket = (userId: string) => {
  writeToFile(LOG_FILE_PATH, (logs: KeyValuePair) => {
    const { socketId } = logs[userId];

    delete logs[userId];
    delete logs[socketId];

    return {
      ...logs,
    };
  });
};

export const unRegisterSocketWithKillSignals = (socketId: string) => {
  handleExitSignals(() => unRegisterSocket(socketId));
};

export const getSocketData = (socketId: string) => {
  const content = getFileContent(LOG_FILE_PATH, "json") as KeyValuePair;

  if (content) return content[socketId];
  return null;
};

export const handleUnRegisterSocket = (
  socket: any,
  roomKey: string,
  userRoomKey: string,
  eventName?: string
) => {
  const data = getSocketData(roomKey);

  if (!data || !data.channelId) return;

  socket.leave(data.channelId);

  unRegisterSocket(userRoomKey);

  if (eventName) socket.to(data.channelId).emit(eventName, data);
};

export const handleReJoinRoom = (
  userRoomKey: string,
  onJoinRoom: (channelId: string, socketData: any) => void
) => {
  // incases were the server restart after a
  // side effect

  const socketData = getSocketData(userRoomKey);

  if (socketData) {
    delete socketData.socketId;

    onJoinRoom(socketData.channelId, socketData);
  }
};

export const handleRegisterRoomSocketData = (
  socket: any,
  roomKey: string,
  userRoomKey: string,
  channelId: string = userRoomKey,
  joinData?: TSocketData
): { hasErrors: boolean; hasJoined: boolean } => {
  try {
    if (!channelId) {
      throw getErrorResponse(
        {
          message: "channel-id is required",
          name: "CHANNEL_ERROR",
        },
        400
      );
    }

    ensureUnder1MB(joinData);

    socket.join(channelId);

    const hasJoined = registerSocket(roomKey, userRoomKey, {
      ...joinData,
      channelId,
    });

    return {
      hasJoined,
      hasErrors: false,
    };
  } catch (err: any) {
    socket.emit(SOCKET_ERROR_KEY, getErrorResponse(err));
    return {
      hasJoined: false,
      hasErrors: true,
    };
  }
};

export const safelyBind = (
  socket: Socket | Server,
  event: string,
  handler: (...args: any[]) => void
) => {
  socket.off(event, handler);
  socket.on(event, handler);
};

export const unBindEvents = (
  socket: Socket,
  handlers: {
    [key: string]: {
      event: string;
      handler: (...args: any[]) => void;
    };
  }
) => {
  for (const { event, handler } of Object.values(handlers)) {
    socket.off(event, handler);
  }
};
