import mongoose from "mongoose";
import Message from "../models/message";
import Conversation from "../models/conversation";

/**
 * Database optimization utilities for chat operations
 */

/**
 * Create optimized indexes for chat collections
 */
export const createChatIndexes = async (): Promise<void> => {
  try {
    console.log("Creating chat performance indexes...");

    // Message collection indexes
    await Message.collection.createIndex(
      { conversation: 1, createdAt: -1 },
      { 
        name: "conversation_createdAt_desc",
        background: true 
      }
    );

    await Message.collection.createIndex(
      { conversation: 1, _id: -1 },
      { 
        name: "conversation_id_desc",
        background: true 
      }
    );

    await Message.collection.createIndex(
      { sender: 1, createdAt: -1 },
      { 
        name: "sender_createdAt_desc",
        background: true 
      }
    );

    await Message.collection.createIndex(
      { "recipients": 1 },
      { 
        name: "recipients_sparse",
        background: true,
        sparse: true 
      }
    );

    // Conversation collection indexes
    await Conversation.collection.createIndex(
      { "participants.user": 1, updatedAt: -1 },
      { 
        name: "participants_user_updatedAt_desc",
        background: true 
      }
    );

    await Conversation.collection.createIndex(
      { "participants.user": 1, _id: -1 },
      { 
        name: "participants_user_id_desc",
        background: true 
      }
    );

    await Conversation.collection.createIndex(
      { lastMessage: 1 },
      { 
        name: "lastMessage_sparse",
        background: true,
        sparse: true 
      }
    );

    await Conversation.collection.createIndex(
      { updatedAt: -1 },
      { 
        name: "updatedAt_desc",
        background: true 
      }
    );

    console.log("Chat indexes created successfully");
  } catch (error) {
    console.error("Error creating chat indexes:", error);
    throw error;
  }
};

/**
 * Optimized query builder for conversation messages
 */
export interface OptimizedMessageQuery {
  conversationId: string;
  limit: number;
  cursor?: string;
  sortOrder: "asc" | "desc";
  userId: string;
}

export const buildOptimizedMessageQuery = (params: OptimizedMessageQuery) => {
  const { conversationId, limit, cursor, sortOrder, userId } = params;

  // Base query
  const query: any = {
    conversation: new mongoose.Types.ObjectId(conversationId),
  };

  // Add cursor-based pagination
  if (cursor) {
    const cursorQuery = sortOrder === "asc" 
      ? { _id: { $gt: new mongoose.Types.ObjectId(cursor) } }
      : { _id: { $lt: new mongoose.Types.ObjectId(cursor) } };
    Object.assign(query, cursorQuery);
  }

  // Optimized aggregation pipeline
  const pipeline: any[] = [
    { $match: query },
    { $sort: { _id: sortOrder === "asc" ? 1 : -1 } },
    { $limit: limit + 1 }, // +1 to check if there are more results
    {
      $lookup: {
        from: "students", // Assuming sender can be student
        localField: "sender",
        foreignField: "_id",
        as: "senderStudent",
        pipeline: [
          { $project: { firstName: 1, lastName: 1, profilePicture: 1, role: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: "tutors", // Assuming sender can be tutor
        localField: "sender",
        foreignField: "_id",
        as: "senderTutor",
        pipeline: [
          { $project: { firstName: 1, lastName: 1, profilePicture: 1, role: 1 } }
        ]
      }
    },
    {
      $addFields: {
        sender: {
          $cond: {
            if: { $gt: [{ $size: "$senderStudent" }, 0] },
            then: { $arrayElemAt: ["$senderStudent", 0] },
            else: { $arrayElemAt: ["$senderTutor", 0] }
          }
        }
      }
    },
    {
      $project: {
        __v: 0,
        senderStudent: 0,
        senderTutor: 0
      }
    }
  ];

  return pipeline;
};

/**
 * Optimized query builder for user conversations
 */
export interface OptimizedConversationQuery {
  userId: string;
  limit: number;
  cursor?: string;
  sortOrder: "asc" | "desc";
}

export const buildOptimizedConversationQuery = (params: OptimizedConversationQuery) => {
  const { userId, limit, cursor, sortOrder } = params;

  // Base query
  const query: any = {
    "participants.user": new mongoose.Types.ObjectId(userId),
  };

  // Add cursor-based pagination
  if (cursor) {
    const cursorQuery = sortOrder === "asc" 
      ? { updatedAt: { $gt: new Date(cursor) } }
      : { updatedAt: { $lt: new Date(cursor) } };
    Object.assign(query, cursorQuery);
  }

  // Optimized aggregation pipeline
  const pipeline: any[] = [
    { $match: query },
    { $sort: { updatedAt: sortOrder === "asc" ? 1 : -1 } },
    { $limit: limit + 1 }, // +1 to check if there are more results
    
    // Lookup participants
    {
      $lookup: {
        from: "students",
        localField: "participants.user",
        foreignField: "_id",
        as: "participantStudents",
        pipeline: [
          { $project: { firstName: 1, lastName: 1, profilePicture: 1, role: 1, isActive: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: "tutors",
        localField: "participants.user",
        foreignField: "_id",
        as: "participantTutors",
        pipeline: [
          { $project: { firstName: 1, lastName: 1, profilePicture: 1, role: 1, isActive: 1 } }
        ]
      }
    },
    
    // Lookup last message
    {
      $lookup: {
        from: "messages",
        localField: "lastMessage",
        foreignField: "_id",
        as: "lastMessageData",
        pipeline: [
          {
            $project: {
              recipients: 1,
              createdAt: 1,
              deliveredAt: 1,
              readAt: 1,
              sender: 1
            }
          },
          {
            $lookup: {
              from: "students",
              localField: "sender",
              foreignField: "_id",
              as: "senderStudent",
              pipeline: [
                { $project: { firstName: 1, lastName: 1, profilePicture: 1, role: 1 } }
              ]
            }
          },
          {
            $lookup: {
              from: "tutors",
              localField: "sender",
              foreignField: "_id",
              as: "senderTutor",
              pipeline: [
                { $project: { firstName: 1, lastName: 1, profilePicture: 1, role: 1 } }
              ]
            }
          },
          {
            $addFields: {
              sender: {
                $cond: {
                  if: { $gt: [{ $size: "$senderStudent" }, 0] },
                  then: { $arrayElemAt: ["$senderStudent", 0] },
                  else: { $arrayElemAt: ["$senderTutor", 0] }
                }
              }
            }
          },
          {
            $project: {
              senderStudent: 0,
              senderTutor: 0
            }
          }
        ]
      }
    },
    
    // Merge participant data
    {
      $addFields: {
        allParticipants: { $concatArrays: ["$participantStudents", "$participantTutors"] },
        lastMessage: { $arrayElemAt: ["$lastMessageData", 0] }
      }
    },
    
    // Map participants correctly
    {
      $addFields: {
        participants: {
          $map: {
            input: "$participants",
            as: "participant",
            in: {
              userModel: "$$participant.userModel",
              user: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: "$allParticipants",
                      cond: { $eq: ["$$this._id", "$$participant.user"] }
                    }
                  },
                  0
                ]
              }
            }
          }
        }
      }
    },
    
    {
      $project: {
        __v: 0,
        participantStudents: 0,
        participantTutors: 0,
        allParticipants: 0,
        lastMessageData: 0
      }
    }
  ];

  return pipeline;
};

/**
 * Cache configuration for chat operations
 */
export interface ChatCacheConfig {
  conversationTTL: number; // Time to live for conversation cache
  messageTTL: number; // Time to live for message cache
  userConversationsTTL: number; // Time to live for user conversations cache
}

export const defaultCacheConfig: ChatCacheConfig = {
  conversationTTL: 300, // 5 minutes
  messageTTL: 600, // 10 minutes
  userConversationsTTL: 180, // 3 minutes
};

/**
 * Generate cache keys for chat operations
 */
export const ChatCacheKeys = {
  userConversations: (userId: string, limit: number, cursor?: string) =>
    `chat:user_conversations:${userId}:${limit}:${cursor || 'first'}`,
  
  conversationMessages: (conversationId: string, limit: number, cursor?: string) =>
    `chat:conversation_messages:${conversationId}:${limit}:${cursor || 'first'}`,
  
  conversationAccess: (conversationId: string, userId: string) =>
    `chat:conversation_access:${conversationId}:${userId}`,
};

/**
 * Performance monitoring thresholds
 */
export const PerformanceThresholds = {
  SLOW_QUERY_MS: 1000,
  VERY_SLOW_QUERY_MS: 5000,
  MAX_MESSAGES_PER_REQUEST: 100,
  MAX_CONVERSATIONS_PER_REQUEST: 50,
};
