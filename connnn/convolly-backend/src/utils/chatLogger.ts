import { Request } from "express";

/**
 * Chat-specific logging utilities
 */

export interface ChatLogContext {
  operation: string;
  userId?: string;
  conversationId?: string;
  messageId?: string;
  userRole?: string;
  ip?: string;
  userAgent?: string;
  timestamp: string;
  duration?: number;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Log levels for chat operations
 */
export enum ChatLogLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  DEBUG = 'debug',
}

/**
 * Chat logger class for structured logging
 */
export class ChatLogger {
  private static instance: ChatLogger;

  private constructor() {}

  public static getInstance(): ChatLogger {
    if (!ChatLogger.instance) {
      ChatLogger.instance = new ChatLogger();
    }
    return ChatLogger.instance;
  }

  /**
   * Log chat operation with context
   */
  public log(level: ChatLogLevel, message: string, context: Partial<ChatLogContext> = {}) {
    const logEntry = {
      level,
      message,
      service: 'chat',
      timestamp: new Date().toISOString(),
      ...context,
    };

    // In production, send to proper logging service (e.g., Winston, ELK stack)
    console.log(`[CHAT_${level.toUpperCase()}] ${JSON.stringify(logEntry)}`);

    // Additional logging for errors
    if (level === ChatLogLevel.ERROR) {
      console.error(`[CHAT_ERROR_DETAIL] ${message}`, context);
    }
  }

  /**
   * Log successful operation
   */
  public logSuccess(operation: string, context: Partial<ChatLogContext> = {}) {
    this.log(ChatLogLevel.INFO, `${operation} completed successfully`, {
      operation,
      ...context,
    });
  }

  /**
   * Log error with context
   */
  public logError(operation: string, error: Error | string, context: Partial<ChatLogContext> = {}) {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    this.log(ChatLogLevel.ERROR, `${operation} failed: ${errorMessage}`, {
      operation,
      error: errorMessage,
      stack: errorStack,
      ...context,
    });
  }

  /**
   * Log warning
   */
  public logWarning(operation: string, message: string, context: Partial<ChatLogContext> = {}) {
    this.log(ChatLogLevel.WARN, `${operation}: ${message}`, {
      operation,
      ...context,
    });
  }

  /**
   * Log debug information (only in development)
   */
  public logDebug(operation: string, message: string, context: Partial<ChatLogContext> = {}) {
    if (process.env.NODE_ENV === 'development') {
      this.log(ChatLogLevel.DEBUG, `${operation}: ${message}`, {
        operation,
        ...context,
      });
    }
  }

  /**
   * Create context from Express request
   */
  public createContextFromRequest(req: Request, operation: string): Partial<ChatLogContext> {
    return {
      operation,
      userId: req.user?.id,
      userRole: req.user?.role,
      conversationId: req.params.conversationId,
      ip: req.ip || req.socket.remoteAddress,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      metadata: {
        params: req.params,
        query: req.query,
        method: req.method,
        url: req.originalUrl,
      },
    };
  }
}

/**
 * Performance monitoring for chat operations
 */
export class ChatPerformanceMonitor {
  private static timers = new Map<string, number>();

  /**
   * Start timing an operation
   */
  public static startTimer(operationId: string): void {
    this.timers.set(operationId, Date.now());
  }

  /**
   * End timing and get duration
   */
  public static endTimer(operationId: string): number {
    const startTime = this.timers.get(operationId);
    if (!startTime) {
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(operationId);
    return duration;
  }

  /**
   * Log performance metrics
   */
  public static logPerformance(
    operation: string,
    duration: number,
    context: Partial<ChatLogContext> = {}
  ): void {
    const logger = ChatLogger.getInstance();
    
    if (duration > 5000) { // Log slow operations (>5s)
      logger.logWarning(operation, `Slow operation detected: ${duration}ms`, {
        ...context,
        duration,
        performance: 'slow',
      });
    } else if (duration > 1000) { // Log medium operations (>1s)
      logger.logDebug(operation, `Operation took ${duration}ms`, {
        ...context,
        duration,
        performance: 'medium',
      });
    } else {
      logger.logDebug(operation, `Operation completed in ${duration}ms`, {
        ...context,
        duration,
        performance: 'fast',
      });
    }
  }
}

/**
 * Error categorization for better monitoring
 */
export enum ChatErrorCategory {
  VALIDATION = 'validation',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  DATABASE = 'database',
  NETWORK = 'network',
  RATE_LIMIT = 'rate_limit',
  INTERNAL = 'internal',
}

/**
 * Structured error for chat operations
 */
export class ChatError extends Error {
  public readonly category: ChatErrorCategory;
  public readonly statusCode: number;
  public readonly context: Record<string, any>;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    category: ChatErrorCategory,
    statusCode: number = 500,
    context: Record<string, any> = {},
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'ChatError';
    this.category = category;
    this.statusCode = statusCode;
    this.context = context;
    this.isOperational = isOperational;

    // Capture stack trace
    Error.captureStackTrace(this, ChatError);
  }

  /**
   * Convert to JSON for logging
   */
  public toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      category: this.category,
      statusCode: this.statusCode,
      context: this.context,
      isOperational: this.isOperational,
      stack: this.stack,
    };
  }
}

/**
 * Helper functions for common chat errors
 */
export const ChatErrors = {
  conversationNotFound: (conversationId: string) =>
    new ChatError(
      'Conversation not found or access denied',
      ChatErrorCategory.NOT_FOUND,
      404,
      { conversationId }
    ),

  unauthorizedAccess: (userId: string, resource: string) =>
    new ChatError(
      `Unauthorized access to ${resource}`,
      ChatErrorCategory.AUTHORIZATION,
      403,
      { userId, resource }
    ),

  invalidInput: (field: string, value: any) =>
    new ChatError(
      `Invalid input for field: ${field}`,
      ChatErrorCategory.VALIDATION,
      400,
      { field, value }
    ),

  rateLimitExceeded: (limit: number, windowMs: number) =>
    new ChatError(
      'Rate limit exceeded',
      ChatErrorCategory.RATE_LIMIT,
      429,
      { limit, windowMs }
    ),

  databaseError: (operation: string, originalError: Error) =>
    new ChatError(
      `Database operation failed: ${operation}`,
      ChatErrorCategory.DATABASE,
      500,
      { operation, originalError: originalError.message }
    ),
};

// Export singleton instance
export const chatLogger = ChatLogger.getInstance();
