import nodemailer from "nodemailer";
import { IS_PROD } from "../config/constants";
import { Attachment } from "nodemailer/lib/mailer";
import { renderEmailTemplate } from "./emails-templates/email-templates";

export type SendMailParams = {
  subject: string;
  html: string;
  from?: "no-reply" | "admin" | "marketing" | "support";
  attachments?: Attachment[];
};

export const MAIL_ALIAS = {
  "no-reply": "<EMAIL>",
  admin: "<EMAIL>",
  marketing: "<EMAIL>",
  support: "<EMAIL>",
};

export const sendResetCode = async (
  user: { email: string; id: string },
  code: string,
  expireAt: Date | number = 10
) => {
  await sendMail(user.email, {
    subject: "Reset your password",
    html: renderEmailTemplate(`
      <p>Your password reset code is:</p>
      <h2>${code}</h2>
      <p>This code will expire in ${
        typeof expireAt === "number" ? expireAt : expireAt.getMinutes()
      } minutes.</p>
    `),
  });
};

export const sendMail = async (to: string, params: SendMailParams) => {
  const fromEmail = MAIL_ALIAS[params.from || "no-reply"];

  const transporter = nodemailer.createTransport({
    host: "smtp.zoho.com",
    port: 465,
    secure: true, // SSL
    auth: {
      user: fromEmail,
      pass: process.env.ZOHO_MAIL_APP_PASSWORD,
    },
    connectionTimeout: 20_000,
    greetingTimeout: 10_000,
    socketTimeout: 30_000,
  });

  await transporter.sendMail({
    to: to || (IS_PROD ? undefined : MAIL_ALIAS.support),
    from: fromEmail,
    subject: params.subject,
    html: params.html,
    attachments: params.attachments,
  });
};
