import { Request, Response } from 'express';
import { processCompletedSessions, processSpecificSession } from '../services/sessionCompletionService';
import { createErrorResponse } from '../middlewares/errorHandler';

/**
 * Admin endpoint to manually process all completed sessions
 */
export const processAllCompletedSessions = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await processCompletedSessions();
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message
      });
    }
  } catch (error: any) {
    console.error('Error in processAllCompletedSessions:', error);
    createErrorResponse(res, 'Failed to process completed sessions', 500);
  }
};

/**
 * Admin endpoint to manually process a specific session
 */
export const processSpecificSessionEndpoint = async (req: Request, res: Response): Promise<void> => {
  try {
    const { eventId } = req.params;
    
    if (!eventId) {
      res.status(400).json({
        success: false,
        message: 'Event ID is required'
      });
      return;
    }

    const result = await processSpecificSession(eventId);
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        data: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error: any) {
    console.error('Error in processSpecificSessionEndpoint:', error);
    createErrorResponse(res, 'Failed to process specific session', 500);
  }
};

/**
 * Get session completion statistics
 */
export const getSessionCompletionStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { Event } = require('../models/Event');
    const LessonModel = require('../models/lesson.model').default;
    
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(startOfDay);
    startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get statistics
    const [
      totalCompletedEvents,
      unprocessedEvents,
      todayCompletedLessons,
      weekCompletedLessons,
      monthCompletedLessons,
      pendingProcessing
    ] = await Promise.all([
      Event.countDocuments({ 
        endDateTime: { $lt: now }, 
        status: 'confirmed',
        processed: true 
      }),
      Event.countDocuments({ 
        endDateTime: { $lt: now }, 
        status: 'confirmed',
        processed: { $ne: true } 
      }),
      LessonModel.countDocuments({
        status: 'completed',
        confirmedAt: { $gte: startOfDay }
      }),
      LessonModel.countDocuments({
        status: 'completed',
        confirmedAt: { $gte: startOfWeek }
      }),
      LessonModel.countDocuments({
        status: 'completed',
        confirmedAt: { $gte: startOfMonth }
      }),
      Event.find({ 
        endDateTime: { $lt: now }, 
        status: 'confirmed',
        processed: { $ne: true } 
      }).select('_id title startDateTime endDateTime').limit(10)
    ]);

    res.json({
      success: true,
      data: {
        totalCompletedEvents,
        unprocessedEvents,
        completedLessons: {
          today: todayCompletedLessons,
          thisWeek: weekCompletedLessons,
          thisMonth: monthCompletedLessons
        },
        pendingProcessing: {
          count: pendingProcessing.length,
          sessions: pendingProcessing
        }
      }
    });
  } catch (error: any) {
    console.error('Error in getSessionCompletionStats:', error);
    createErrorResponse(res, 'Failed to get session completion statistics', 500);
  }
};

/**
 * Scheduled job function to automatically process completed sessions
 * This should be called by a cron job or scheduler
 */
export const scheduledSessionProcessing = async (): Promise<void> => {
  try {
    console.log('Starting scheduled session processing...');
    const result = await processCompletedSessions();
    
    if (result.success) {
      console.log(`Scheduled processing completed: ${result.message}`);
      if (result.data && result.data.errors.length > 0) {
        console.warn('Errors during processing:', result.data.errors);
      }
    } else {
      console.error('Scheduled processing failed:', result.message);
    }
  } catch (error: any) {
    console.error('Error in scheduled session processing:', error);
  }
};

/**
 * Health check endpoint for session processing service
 */
export const sessionProcessingHealthCheck = async (req: Request, res: Response): Promise<void> => {
  try {
    const { Event } = require('../models/Event');
    const now = new Date();
    
    // Check for sessions that ended more than 1 hour ago but haven't been processed
    const overdueCount = await Event.countDocuments({
      endDateTime: { $lt: new Date(now.getTime() - 60 * 60 * 1000) }, // 1 hour ago
      status: 'confirmed',
      processed: { $ne: true }
    });

    const isHealthy = overdueCount < 10; // Consider unhealthy if more than 10 overdue sessions

    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      message: isHealthy ? 'Session processing service is healthy' : 'Session processing service needs attention',
      data: {
        overdueSessionsCount: overdueCount,
        threshold: 10,
        isHealthy
      }
    });
  } catch (error: any) {
    console.error('Error in session processing health check:', error);
    res.status(503).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
};
