import { Request, Response } from 'express';
import Student from '../models/student';
import <PERSON><PERSON> from '../models/tutor';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { createOkResponse } from '../utils/misc';
import { uploadAndDeleteFile } from '../utils/file';

// Configure multer for file upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads/profile-images');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const userId = (req as AuthRequest).user?.id;
    const timestamp = Date.now();
    const extension = path.extname(file.originalname);
    cb(null, `profile-${userId}-${timestamp}${extension}`);
  }
});

// File filter for images only
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.'));
  }
};

// Configure multer
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
  }
});

export class ProfileImageController {
  // Upload profile image
  static async uploadProfileImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;
      const file = req.file;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      if (!file) {
        createErrorResponse(res, 'No file uploaded', 400);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId);
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId);
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      // Delete old profile image if it exists
      if (user.image) {
        const oldImagePath = path.join(__dirname, '../../uploads/profile-images', path.basename(user.image));
        try {
          if (fs.existsSync(oldImagePath)) {
            await unlinkAsync(oldImagePath);
          }
        } catch (error) {
          console.warn('Failed to delete old profile image:', error);
        }
      }

      // Update user with new image path
      const imageUrl = `/uploads/profile-images/${file.filename}`;
      user.image = imageUrl;
      await user.save();

      createOkResponse(res, {
        success: true,
        message: 'Profile image uploaded successfully',
        data: {
          imageUrl,
        },
      });
    } catch (error: any) {
      console.error('Error uploading profile image:', error);
      
      // Clean up uploaded file if there was an error
      if (req.file) {
        try {
          await unlinkAsync(req.file.path);
        } catch (cleanupError) {
          console.warn('Failed to cleanup uploaded file:', cleanupError);
        }
      }
      
      createErrorResponse(res, error.message || 'Failed to upload profile image', 500);
    }
  }

  // Delete profile image
  static async deleteProfileImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId);
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId);
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      if (!user.image) {
        createErrorResponse(res, 'No profile image to delete', 400);
        return;
      }

      // Delete image file
      const imagePath = path.join(__dirname, '../../uploads/profile-images', path.basename(user.image));
      try {
        if (fs.existsSync(imagePath)) {
          await unlinkAsync(imagePath);
        }
      } catch (error) {
        console.warn('Failed to delete profile image file:', error);
      }

      // Remove image reference from user
      user.image = undefined;
      await user.save();

      createOkResponse(res, {
        success: true,
        message: 'Profile image deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting profile image:', error);
      createErrorResponse(res, 'Failed to delete profile image', 500);
    }
  }

  // Get profile image URL
  static async getProfileImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId).select('image');
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId).select('image');
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      createOkResponse(res, {
        success: true,
        data: {
          imageUrl: user.image || null,
        },
      });
    } catch (error: any) {
      console.error('Error fetching profile image:', error);
      createErrorResponse(res, 'Failed to fetch profile image', 500);
    }
  }
}
