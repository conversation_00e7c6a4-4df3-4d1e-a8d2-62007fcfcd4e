import { Request, Response } from 'express';
import { Event } from '../models/Event';
import { Calendar } from '../models/calender';
// import mongoose from 'mongoose';

// Create event (only calendar owner can create events)
export const createEvent = async (req: Request, res: Response): Promise<any>  => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const {
      calendarId,
      title,
      description,
      location,
      startDateTime,
      endDateTime,
      allDay,
      status,
      visibility,
      priority
    } = req.body;

    // Validate required fields
    if (!calendarId || !title || !startDateTime || !endDateTime) {
      return res.status(400).json({ message: 'calendarId, title, startDateTime, and endDateTime are required.' });
    }

    // Validate date format
    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format for startDateTime or endDateTime' });
    }

    if (startDate >= endDate) {
      return res.status(400).json({ message: 'startDateTime must be before endDateTime' });
    }

    // if (!mongoose.Types.ObjectId.isValid(calendarId)) {
    //   return res.status(400).json({ message: 'Invalid calendarId' });
    // }

    // Check calendar existence and ownership
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      return res.status(404).json({ message: 'Calendar not found' });
    }

    if (!calendar.ownerUserId || calendar.ownerUserId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'You do not own this calendar' });
    }


    // Create and save event
    const event = new Event({
      calendarId,
      title,
      description,
      location,
      startDateTime: startDate,
      endDateTime: endDate,
      allDay: allDay ?? false,
      // createdBy: req.user.id,
      status: status ?? 'confirmed',
      visibility: visibility ?? 'public',
      priority: priority ?? 3,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await event.save();

    res.status(201).json({
      success: true,
      message: 'Event created successfully',
      data: event
    });
  } catch (error: any) {
    console.error('Create Event Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create event',
      error: error.message
    });
  }
};


// Get events by calendar (for learners or tutors)
export const getEventsByCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    const calendarId = req.params.calendarId;

    // Optionally, check if calendar is shared or owned by tutor
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      return res.status(404).json({ message: 'Calendar not found' });
    }

    // If user is learner, show only public events
    let query: any = { calendarId };
    if (req.user.role === 'learner') {
      query.visibility = 'public';
    }

    const events = await Event.find(query);
    res.json(events);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Update event (only calendar owner can update events)
export const updateEvent = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const eventId = req.params.id;
    const event = await Event.findById(eventId).populate('calendarId');
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Check if user owns the calendar
    const calendar = event.calendarId as any;
    if (!calendar || calendar.ownerUserId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized to update this event' });
    }

    const updates = req.body;

    // Validate dates if provided
    if (updates.startDateTime || updates.endDateTime) {
      const startDate = new Date(updates.startDateTime || event.startDateTime);
      const endDate = new Date(updates.endDateTime || event.endDateTime);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return res.status(400).json({ message: 'Invalid date format' });
      }

      if (startDate >= endDate) {
        return res.status(400).json({ message: 'startDateTime must be before endDateTime' });
      }
    }

    Object.assign(event, updates, { updatedAt: new Date() });

    await event.save();

    res.json({
      success: true,
      message: 'Event updated successfully',
      data: event
    });
  } catch (error: any) {
    console.error('Update event error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update event',
      error: error.message
    });
  }
};

// Delete event (only calendar owner can delete events)
export const deleteEvent = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const eventId = req.params.id;
    const event = await Event.findById(eventId).populate('calendarId');
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Check if user owns the calendar
    const calendar = event.calendarId as any;
    if (!calendar || calendar.ownerUserId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Not authorized to delete this event' });
    }

    await event.deleteOne();

    res.json({
      success: true,
      message: 'Event deleted successfully'
    });
  } catch (error: any) {
    console.error('Delete event error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete event',
      error: error.message
    });
  }
};
