import { Request, Response } from "express";
import { IReview, Review } from "../models/review";
import {
  createErrorResponse,
  getErrorResponse,
} from "../middlewares/errorHandler";
import { capitalize, createOkResponse, paginate } from "../utils/misc";
import {
  getProfile,
  GetProfileProps,
  USER_PROFILE_TYPE,
} from "../utils/profile";
import { IProfile } from "../models/profile";
import { logError } from "../utils/logger";

export const REVIEW_POPULATE = ["createdBy", "targetUser"];

export const getUpdatedReviewStats = (
  user: USER_PROFILE_TYPE,
  newRating: number,
  shouldIncrement = true
): IProfile["reviewStats"] => {
  const existingStats: IProfile["reviewStats"] = (user.toObject
    ? user.toObject()
    : user
  ).reviewStats || {
    avgRating: 0,
    totalRating: 0,
    totalReview: 0,
    ratingCount: {},
  };

  const updatedTotalReview = shouldIncrement
    ? existingStats.totalReview + 1
    : existingStats.totalReview
    ? existingStats.totalReview - 1
    : 0;

  const updatedTotalRating = shouldIncrement
    ? existingStats.totalRating + newRating
    : existingStats.totalRating
    ? existingStats.totalRating - newRating
    : 0;

  const updatedAvgRating = Number(
    (updatedTotalRating / updatedTotalReview).toFixed(1)
  );

  const count = existingStats.ratingCount[newRating] || 0;

  existingStats.ratingCount[newRating] = shouldIncrement
    ? count + 1
    : count
    ? count - 1
    : 0;

  return {
    avgRating: updatedAvgRating,
    totalRating: updatedTotalRating,
    totalReview: updatedTotalReview,
    ratingCount: existingStats.ratingCount,
  };
};

const validate = async (body: {
  targetId: Required<GetProfileProps>["id"];
  rating: string | number;
  targetRole: Required<GetProfileProps>["role"];
}) => {
  const rating = Number(body.rating) || 0;

  if (body.rating !== undefined) {
    if (!rating || rating > 5) {
      throw getErrorResponse("Rating should be between 1 and 5", 400);
    }
  }

  const targetUser = await getProfile({
    id: body.targetId,
    role: body.targetRole,
  });

  if (!targetUser) {
    throw getErrorResponse(`${capitalize(body.targetRole)} not found.`, 404);
  }

  return { rating, targetUser };
};

export const createReview = async (req: Request, res: Response) => {
  try {
    if (!req.body.rating) {
      createErrorResponse(res, "Review rating is less than 1");
      return;
    }

    const { targetUser, rating } = await validate(req.body);

    const review = await Review.create({
      createdBy: req.user.id,
      createdByModel: capitalize(req.user.role),
      targetUser: req.body.targetId,
      targetUserModel: capitalize(req.body.targetRole),
      rating,
      comment: req.body.comment,
      lesson: req.body.lessonId,
    });

    await review.save();

    try {
      await targetUser.updateOne({
        reviewStats: getUpdatedReviewStats(targetUser, rating),
      });
    } catch (err) {
      logError("Failed to update review target user", "review", req.body);

      await review.deleteOne();

      throw getErrorResponse("Failed to submit review, please try again.", 500);
    }

    createOkResponse(res, "Review submitted successfully");

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const getReviewsByMe = async (req: Request, res: Response) => {
  try {
    const result = await paginate({
      ...req.body,
      model: Review,
      query: {
        createdBy: req.user.id,
      },
      populate: REVIEW_POPULATE,
    });

    createOkResponse(res, result);

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const getReviewsOnMe = async (req: Request, res: Response) => {
  try {
    const result = await paginate({
      ...req.body,
      model: Review,
      query: {
        targetUser: req.user.id,
      },
      populate: REVIEW_POPULATE,
    });

    createOkResponse(res, result);

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const deleteReview = async (req: Request, res: Response) => {
  try {
    const review = req.review!;

    await review.deleteOne(req.body.reviewId);

    if (review) {
      try {
        const targetUser = await getProfile({
          id: req.body.targetId,
          role: req.body.targetRole,
        });

        if (targetUser) {
          await targetUser.updateOne({
            reviewStats: getUpdatedReviewStats(
              targetUser,
              review.rating,
              false
            ),
          });
        }
      } catch (err: any) {
        logError(
          "Failed to decrease tutor review after delete",
          "review",
          review.toObject()
        );
      }
    }

    createOkResponse(res, "Review deleted successfully.");
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const updateReview = async (req: Request, res: Response) => {
  try {
    const review = req.review!;

    const { targetUser, rating } = await validate({
      ...req.body,
      targetId: review.targetUser,
      targetRole: review.targetUserModel.toLowerCase(),
    });

    if (!review) {
      createErrorResponse(res, "Review not found", 404);
      return;
    }

    let err;

    const updates: Partial<IReview> = {};

    if (req.body.comment) updates.comment = req.body.comment;

    if (rating) {
      try {
        updates.rating = rating;

        const stats = getUpdatedReviewStats(targetUser, review.rating, false);

        const newStats = getUpdatedReviewStats(
          {
            ...targetUser.toObject(),
            reviewStats: stats,
          },
          rating
        );

        await targetUser.updateOne({
          reviewStats: newStats,
        });
      } catch (err) {
        logError("Failed to update review target user", "review", req.body);

        err = "Failed to update rating";
      }
    }

    await review.updateOne(updates);

    createOkResponse(res, {
      success: !err,
      data: review,
      message: "Review updated updated successfully.",
      details: {
        reason: err,
        code: err ? "REVIEW_UPDATE_ERROR" : undefined,
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};
