import { Request, Response } from "express";
import Classroom from "../models/classroom";
import { createErrorResponse } from "../middlewares/errorHandler";
import { generateAgoraChatUserToken, generateRTCToken } from "../utils/hashing";
import { createOkResponse } from "../utils/misc";
import { isValidObjectId } from "../utils/validation";
import {
  CLASSROOM_STATUS_CODES,
  createClassroomHook,
} from "../hooks/classroom";
import { getChatUser } from "../services/agora";

export const createClassroom = async (req: Request, res: Response) => {
  try {
    const classroom = await createClassroomHook(req.body, req.user);

    createOkResponse(res, {
      data: classroom,
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getClassroomRTCData = async (req: Request, res: Response) => {
  try {
    const { channelId } = req.body;
    const { username, id: userId } = req.user;

    if (!channelId) {
      createErrorResponse(res, "Channel ID is required.", 400);
      return;
    }

    if (!isValidObjectId(channelId)) {
      createErrorResponse(res, "Invalid channel ID");
      return;
    }

    const classroom = await Classroom.findById(channelId);

    if (!classroom) {
      createErrorResponse(res, "No clasroom found for this channel", 404);
      return;
    }

    if (!classroom.tutor || userId !== classroom.tutor.toString())
      switch (classroom.visibility) {
        case "students-only":
          if (!classroom.students.includes(userId)) {
            createErrorResponse(
              res,
              {
                message:
                  "Access denied. You are not enrolled in this classroom.",
                code: CLASSROOM_STATUS_CODES["403"],
              },
              403
            );
            return;
          }
          break;
        case "private":
          createErrorResponse(
            res,
            {
              message: "Unauthorized Access",
              code: CLASSROOM_STATUS_CODES["403"],
            },
            403
          );
          return;
        default:
          break;
      }

    const chatUser = await getChatUser(username);

    if (!chatUser) {
      createErrorResponse(res, {
        message: "Unauthorized access",
        code: CLASSROOM_STATUS_CODES["403"],
        details: {
          message: "User isn't registered with agora.",
        },
      });
      return;
    }
    const rtcToken = generateRTCToken(channelId, chatUser.id);

    const chatUserToken = generateAgoraChatUserToken(chatUser.id);

    createOkResponse(res, {
      message: "RTC data generated successfully.",
      data: {
        chatGroup: classroom.chatGroup,
        rtcToken,
        chatUserToken,
        chatUser,
        channel: {
          id: channelId,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getClassroomData = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!isValidObjectId(id)) {
      createErrorResponse(res, "Invalid classroom id");
      return;
    }

    let classroom = await Classroom.findById(id);

    if (!classroom) {
      createErrorResponse(res, "Classroom not found", 404);
      return;
    }

    classroom = await classroom.populate("tutor", "-approvalStatus");

    createOkResponse(res, {
      data: classroom,
    });
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const updateTimeLog = async (req: Request, res: Response) => {
  // try {
  //   const { sessionId } = req.params;
  //   const { action } = req.body;
  //   const userId = req.user?.id;
  //   const user = await getProfile({ id: req.user!.id });
  //   if (!user) {
  //     createErrorResponse(res, "Profile not found", 404);
  //     return;
  //   }
  //   const userRole = user.role;
  //   const session = await Classroom.findById(sessionId);
  //   if (!session) {
  //     createErrorResponse(res, "Session not found.", 404);
  //     return;
  //   }
  //   const now = new Date();
  //   if (userRole === "tutor" && session.tutor.toString() === userId) {
  //     if (action === "join") session.tutorJoinedAt = now;
  //     else if (action === "leave") session.tutorLeftAt = now;
  //   } else if (
  //     userRole === "student" &&
  //     session.learner.toString() === userId
  //   ) {
  //     if (action === "join") session.learnerJoinedAt = now;
  //     else if (action === "leave") session.learnerLeftAt = now;
  //   } else {
  //     res
  //       .status(403)
  //       .json({ message: "You are not a participant in this session." });
  //     return;
  //   }
  //   await session.save();
  //   res.status(200).json(session);
  // } catch (err) {
  //   console.error(err);
  //   res.status(500).json({ message: "Error updating time log." });
  // }
};
