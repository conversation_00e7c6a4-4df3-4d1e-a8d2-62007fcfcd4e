import { Request, Response } from "express";
import postHogClient from "../config/posthog";
import { createOkResponse, getReqSessionId } from "../utils/misc";
import { createErrorResponse } from "../middlewares/errorHandler";

export const captureProfileClick = async (req: Request, res: Response) => {
  try {
    const sessionId = getReqSessionId(req);
    const profileId = req.params.profileId;

    postHogClient.capture({
      distinctId: sessionId,
      event: "user_clicked_profile",
      properties: {
        userId: profileId,
        timestamp: new Date().toISOString(),
      },
    });
    createOkResponse(res, "Profile click captured");
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};
