import { Request, Response } from "express";
import { Types } from "mongoose";
import Tu<PERSON> from "../models/tutor";
import Student from "../models/student";
import Admin from "../models/admin";
import Subscription from "../models/subscription.model";
import LessonModel from "../models/lesson.model";
import TransactionModel from "../models/transaction.model";
import EscrowModel from "../models/escrow.model";
import WithdrawalRequest from "../models/withdrawalRequest.model";
import RefundRequest from "../models/refundRequest.model";
import { TransactionService } from "../services/transactionService";
import { AuthRequest } from "../types/AuthRequest";
import { createErrorResponse } from "../middlewares/errorHandler";
import { getProfile, getProfileModelByRole, getProfiles } from "../utils/profile";
import { EmailNotificationService } from "../services/emailNotificationService";
import { hashPassword, generateEncryptionData } from "../utils/hashing";

// ============================================================================
// PERMISSION MIDDLEWARE HELPER
// ============================================================================

// Note: Permission checking is handled by the requireAdmin middleware in routes
// Individual permission checks can be added here if needed in the future

// ============================================================================
// DASHBOARD & ANALYTICS
// ============================================================================

export const getDashboardStats = async (_req: AuthRequest, res: Response): Promise<void> => {
  try {
    const [
      totalUsers,
      totalTutors,
      totalStudents,
      pendingTutors,
      activeTutors,
      totalSubscriptions,
      activeSubscriptions,
      totalLessons,
      completedLessons,
      flaggedContent,
      revenueThisMonth
    ] = await Promise.all([
      // User counts
      Promise.all([
        Tutor.countDocuments(),
        Student.countDocuments()
      ]).then(([tutors, students]) => tutors + students),

      Tutor.countDocuments(),
      Student.countDocuments(),
      Tutor.countDocuments({ approvalStatus: "pending" }),
      Tutor.countDocuments({ approvalStatus: "approved", isActive: true }),

      // Subscription stats
      Subscription.countDocuments(),
      Subscription.countDocuments({ status: "active" }),

      // Lesson stats
      LessonModel.countDocuments(),
      LessonModel.countDocuments({ status: "completed" }),

      // Flagged content
      Tutor.countDocuments({ isFlagged: true }),

      // Revenue calculation (simplified)
      Subscription.aggregate([
        {
          $match: {
            status: "active",
            createdAt: {
              $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: "$monthlyPrice" }
          }
        }
      ]).then(result => result[0]?.total || 0)
    ]);

    // Recent activity
    const recentTutors = await Tutor.find({ approvalStatus: "pending" })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('firstname lastname email createdAt');

    const recentSubscriptions = await Subscription.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('studentId', 'firstname lastname')
      .populate('tutorId', 'firstname lastname')
      .select('planType monthlyPrice status createdAt');

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          totalTutors,
          totalStudents,
          pendingTutors,
          activeTutors,
          totalSubscriptions,
          activeSubscriptions,
          totalLessons,
          completedLessons,
          flaggedContent,
          revenueThisMonth
        },
        recentActivity: {
          newTutorApplications: recentTutors,
          newSubscriptions: recentSubscriptions
        }
      }
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    createErrorResponse(res, "Failed to fetch dashboard statistics", 500);
  }
};

export const getAnalytics = async (_req: Request, res: Response): Promise<void> => {
  try {
    const totalTutors = await Tutor.countDocuments();
    const approvedTutors = await Tutor.countDocuments({ approvalStatus: "approved" });
    const flaggedTutors = await Tutor.countDocuments({ isFlagged: true });

    res.json({
      totalTutors,
      approvedTutors,
      flaggedTutors,
      reviewFlaggedCount: await Tutor.aggregate([
        { $unwind: "$reviews" },
        { $match: { "reviews.flagged": true } },
        { $count: "count" },
      ]).then((r) => r[0]?.count || 0),
    });
  } catch (error) {
    console.error("Error fetching analytics:", error);
    createErrorResponse(res, "Analytics service down. Try again later.", 500);
  }
};

// ============================================================================
// TUTOR MANAGEMENT
// ============================================================================

export const approveTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      {
        approvalStatus: "approved",
        isActive: true
      },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    // Send tutor approval email notification
    try {
      await EmailNotificationService.sendTutorApprovalStatus(
        {
          id: (tutor._id as Types.ObjectId).toString(),
          firstname: tutor.firstname,
          lastname: tutor.lastname,
          email: tutor.email
        },
        'approved'
      );
      console.log(`Tutor approval email sent to ${tutor.email}`);
    } catch (emailError: any) {
      console.error('Error sending tutor approval email:', emailError);
      // Don't fail the approval if email sending fails
    }

    res.json({
      success: true,
      message: "Tutor approved successfully",
      data: tutor
    });
  } catch (error) {
    console.error("Error approving tutor:", error);
    createErrorResponse(res, "Failed to approve tutor", 500);
  }
};

export const rejectTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;
  const { rejectionReason } = req.body;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    if (!rejectionReason) {
      createErrorResponse(res, "Rejection reason is required", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      {
        approvalStatus: "rejected",
        isActive: false
      },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    // Send tutor rejection email notification
    try {
      await EmailNotificationService.sendTutorApprovalStatus(
        {
          id: (tutor._id as Types.ObjectId).toString(),
          firstname: tutor.firstname,
          lastname: tutor.lastname,
          email: tutor.email
        },
        'rejected',
        rejectionReason
      );
      console.log(`Tutor rejection email sent to ${tutor.email}`);
    } catch (emailError: any) {
      console.error('Error sending tutor rejection email:', emailError);
      // Don't fail the rejection if email sending fails
    }

    res.json({
      success: true,
      message: "Tutor rejected",
      data: tutor
    });
  } catch (error) {
    console.error("Error rejecting tutor:", error);
    createErrorResponse(res, "Failed to reject tutor", 500);
  }
};

export const getPendingTutors = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [tutors, total] = await Promise.all([
      Tutor.find({ approvalStatus: "pending" })
        .select('firstname lastname email teachingSubjects basePrice createdAt approvalStatus')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      Tutor.countDocuments({ approvalStatus: "pending" })
    ]);

    res.json({
      success: true,
      data: tutors,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching pending tutors:", error);
    createErrorResponse(res, "Failed to fetch pending tutors", 500);
  }
};

// export const getAllTutors = async (req: Request, res: Response): Promise<void> => {
//   try {
//     const {
//       page = 1,
//       limit = 20,
//       status,
//       approvalStatus,
//       search,
//       sortBy = 'createdAt',
//       sortOrder = 'desc'
//     } = req.query;

//     const skip = (Number(page) - 1) * Number(limit);
//     const query: any = {};

//     // Filters
//     if (status) query.isActive = status === 'active';
//     if (approvalStatus) query.approvalStatus = approvalStatus;
//     if (search) {
//       query.$or = [
//         { firstname: { $regex: search, $options: 'i' } },
//         { lastname: { $regex: search, $options: 'i' } },
//         { email: { $regex: search, $options: 'i' } }
//       ];
//     }

//     const sortOptions: any = {};
//     sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

//     const [tutors, total] = await Promise.all([
//       Tutor.find(query)
//         // .select('firstname lastname email teachingSubjects basePrice rating totalLessons approvalStatus isActive isFlagged createdAt')
//         .sort(sortOptions)
//         .skip(skip)
//         .limit(Number(limit)),
//       Tutor.countDocuments(query)
//     ]);

//     res.json({
//       success: true,
//       data: tutors,
//       pagination: {
//         page: Number(page),
//         limit: Number(limit),
//         total,
//         pages: Math.ceil(total / Number(limit))
//       }
//     });
//   } catch (error) {
//     console.error("Error fetching tutors:", error);
//     createErrorResponse(res, "Failed to fetch tutors", 500);
//   }
// };

export const getAllTutors = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = "1",
      limit = "20",
      status,
      approvalStatus,
      search,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = req.query;

    const TutorModel = getProfileModelByRole("tutor");

    const skip = (parseInt(page as string, 10) - 1) * parseInt(limit as string, 10);
    const query: any = {};

    // Apply filters
    if (status) query.isActive = status === "active";
    if (approvalStatus) query.approvalStatus = approvalStatus;
    if (search) {
      query.$or = [
        { firstname: { $regex: search, $options: "i" } },
        { lastname: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } }
      ];
    }

    // Sorting options
    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === "desc" ? -1 : 1;

    // Fetch data and count
    const [tutors, total] = await Promise.all([
      TutorModel.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit as string, 10)),
      TutorModel.countDocuments(query)
    ]);

    res.status(200).json({
      success: true,
      data: tutors,
      pagination: {
        page: parseInt(page as string, 10),
        limit: parseInt(limit as string, 10),
        total,
        pages: Math.ceil(total / parseInt(limit as string, 10))
      }
    });

  } catch (error: any) {
    console.error("Error fetching tutors:", error);
    createErrorResponse(res, error.message || "Failed to fetch tutors", 500);
  }
};

// export const getTutorById = async (req: Request, res: Response): Promise<void> => {
//   const { tutorId } = req.params;

//   try {
//     if (!Types.ObjectId.isValid(tutorId)) {
//       createErrorResponse(res, "Invalid tutor ID format", 400);
//       return;
//     }

//     const tutor = await Tutor.findById(tutorId)
//       .populate('subscriptions')
//       .populate('lessons');

//     if (!tutor) {
//       createErrorResponse(res, "Tutor not found", 404);
//       return;
//     }

//     res.json({
//       success: true,
//       data: tutor
//     });
//   } catch (error) {
//     console.error("Error fetching tutor:", error);
//     createErrorResponse(res, "Failed to fetch tutor", 500);
//   }
// };

export const getTutorById = async (req: Request, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const TutorModel = getProfileModelByRole("tutor");

    const tutor = await TutorModel.findById(tutorId)
      .populate("subscriptions")
      .populate("lessons");

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.status(200).json({
      success: true,
      data: tutor
    });
  } catch (error: any) {
    console.error("Error fetching tutor:", error);
    createErrorResponse(res, error.message || "Failed to fetch tutor", 500);
  }
};

export const flagTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      { isFlagged: true },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Tutor flagged successfully",
      data: tutor
    });
  } catch (error) {
    console.error("Error flagging tutor:", error);
    createErrorResponse(res, "Failed to flag tutor", 500);
  }
};

export const unflagTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      { isFlagged: false },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Tutor unflagged successfully",
      data: tutor
    });
  } catch (error) {
    console.error("Error unflagging tutor:", error);
    createErrorResponse(res, "Failed to unflag tutor", 500);
  }
};

export const suspendTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;
  const { reason, duration } = req.body;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    if (!reason) {
      createErrorResponse(res, "Suspension reason is required", 400);
      return;
    }

    const tutor = await Tutor.findByIdAndUpdate(
      tutorId,
      { isActive: false },
      { new: true }
    );

    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    res.json({
      success: true,
      message: `Tutor suspended ${duration ? `for ${duration} days` : 'indefinitely'}`,
      data: tutor
    });
  } catch (error) {
    console.error("Error suspending tutor:", error);
    createErrorResponse(res, "Failed to suspend tutor", 500);
  }
};

export const deleteTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  const { tutorId } = req.params;
  const { deleteReason, transferSubscriptions = false } = req.body;

  try {
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, "Invalid tutor ID format", 400);
      return;
    }

    if (!deleteReason) {
      createErrorResponse(res, "Deletion reason is required", 400);
      return;
    }

    // Check if tutor exists
    const tutor = await Tutor.findById(tutorId);
    if (!tutor) {
      createErrorResponse(res, "Tutor not found", 404);
      return;
    }

    // Check for active subscriptions
    const activeSubscriptions = await Subscription.find({
      tutorId,
      status: { $in: ['active', 'pending_transfer'] }
    });

    if (activeSubscriptions.length > 0 && !transferSubscriptions) {
      createErrorResponse(res,
        `Cannot delete tutor with ${activeSubscriptions.length} active subscription(s). Set transferSubscriptions to true to handle them.`,
        400
      );
      return;
    }

    // Handle active subscriptions if requested
    if (activeSubscriptions.length > 0 && transferSubscriptions) {
      // Cancel all active subscriptions
      await Subscription.updateMany(
        { tutorId, status: { $in: ['active', 'pending_transfer'] } },
        {
          status: 'cancelled',
          autoRenew: false,
          updatedAt: new Date()
        }
      );

      // Create transaction records for any refunds needed
      for (const subscription of activeSubscriptions) {
        const refundTransaction = new TransactionModel({
          userId: subscription.studentId,
          amount: subscription.monthlyPrice * 100, // Convert to cents
          type: 'refund',
          status: 'pending',
          description: `Refund due to tutor deletion - ${deleteReason}`
        });
        await refundTransaction.save();
      }
    }

    // Handle pending withdrawal requests
    await WithdrawalRequest.updateMany(
      { tutorId, status: 'pending' },
      {
        status: 'rejected',
        rejectionReason: `Tutor account deleted - ${deleteReason}`,
        processedBy: req.user!._id,
        processedAt: new Date()
      }
    );

    // Handle escrow transactions
    const heldEscrow = await EscrowModel.find({ tutorId, status: 'held' });
    if (heldEscrow.length > 0) {
      // Refund held escrow to students
      for (const escrow of heldEscrow) {
        escrow.status = 'refunded';
        await escrow.save();

        // Create refund transaction
        const refundTransaction = new TransactionModel({
          userId: escrow.studentId,
          amount: escrow.amountHeld,
          type: 'refund',
          status: 'pending',
          description: `Escrow refund due to tutor deletion - ${deleteReason}`
        });
        await refundTransaction.save();
      }
    }

    // Soft delete the tutor (mark as deleted instead of removing)
    await Tutor.findByIdAndUpdate(tutorId, {
      isActive: false,
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy: req.user!._id,
      deletionReason: deleteReason,
      email: `deleted_${Date.now()}_${tutor.email}` // Prevent email conflicts
    });

    res.json({
      success: true,
      message: "Tutor deleted successfully",
      data: {
        tutorId,
        activeSubscriptionsCancelled: activeSubscriptions.length,
        escrowTransactionsRefunded: heldEscrow.length,
        deletionReason: deleteReason
      }
    });
  } catch (error) {
    console.error("Error deleting tutor:", error);
    createErrorResponse(res, "Failed to delete tutor", 500);
  }
};

// ============================================================================
// STUDENT MANAGEMENT
// ============================================================================

export const getAllStudents = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = "1",
      limit = "20",
      status,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
      tutorId
    } = req.query;

    const parsedPage = parseInt(page as string, 10);
    const parsedLimit = parseInt(limit as string, 10);

    const query: any = { role: "student" };

    if (status) query.isActive = status === "active";
    if (tutorId) query.assignedTutor = tutorId;
    if (search) {
      query.$or = [
        { firstname: { $regex: search, $options: "i" } },
        { lastname: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } }
      ];
    }

    // Fetch all matching students first
    let students = await getProfiles(query);

    // Sort manually
    students = students.sort((a: any, b: any) => {
      const aVal = a[sortBy as string];
      const bVal = b[sortBy as string];
      if (sortOrder === "desc") return aVal < bVal ? 1 : -1;
      return aVal > bVal ? 1 : -1;
    });

    // Paginate manually
    const total = students.length;
    const start = (parsedPage - 1) * parsedLimit;
    const paginatedStudents = students.slice(start, start + parsedLimit);

    res.status(200).json({
      success: true,
      data: paginatedStudents,
      pagination: {
        page: parsedPage,
        limit: parsedLimit,
        total,
        pages: Math.ceil(total / parsedLimit)
      }
    });
  } catch (error: any) {
    console.error("Error fetching students:", error);
    createErrorResponse(res, error.message || "Failed to fetch students", 500);
  }
};

export const getStudentById = async (req: Request, res: Response): Promise<void> => {
  const { studentId } = req.params;

  try {
    if (!Types.ObjectId.isValid(studentId)) {
      createErrorResponse(res, "Invalid student ID format", 400);
      return;
    }

    const student = await Student.findById(studentId)
    .select('-password -authToken')
      .populate('subscriptions')
      .populate('lessons');

    if (!student) {
      createErrorResponse(res, "Student not found", 404);
      return;
    }

    res.json({
      success: true,
      data: student
    });
  } catch (error) {
    console.error("Error fetching student:", error);
    createErrorResponse(res, "Failed to fetch student", 500);
  }
};

export const suspendStudent = async (req: AuthRequest, res: Response): Promise<void> => {
  const { studentId } = req.params;
  const { reason, duration } = req.body;

  try {
    if (!Types.ObjectId.isValid(studentId)) {
      createErrorResponse(res, "Invalid student ID format", 400);
      return;
    }

    if (!reason) {
      createErrorResponse(res, "Suspension reason is required", 400);
      return;
    }

    const student = await Student.findByIdAndUpdate(
      studentId,
      { isActive: false },
      { new: true }
    );

    if (!student) {
      createErrorResponse(res, "Student not found", 404);
      return;
    }

    res.json({
      success: true,
      message: `Student suspended ${duration ? `for ${duration} days` : 'indefinitely'}`,
      data: student
    });
  } catch (error) {
    console.error("Error suspending student:", error);
    createErrorResponse(res, "Failed to suspend student", 500);
  }
};

export const deleteStudent = async (req: AuthRequest, res: Response): Promise<void> => {
  const { studentId } = req.params;
  const { deleteReason, refundSubscriptions = true } = req.body;

  try {
    if (!Types.ObjectId.isValid(studentId)) {
      createErrorResponse(res, "Invalid student ID format", 400);
      return;
    }

    if (!deleteReason) {
      createErrorResponse(res, "Deletion reason is required", 400);
      return;
    }

    // Check if student exists
    const student = await Student.findById(studentId);
    if (!student) {
      createErrorResponse(res, "Student not found", 404);
      return;
    }

    // Get all student's subscriptions
    const subscriptions = await Subscription.find({
      studentId,
      status: { $in: ['active', 'paused', 'incomplete'] }
    });

    // Handle active subscriptions
    if (subscriptions.length > 0) {
      for (const subscription of subscriptions) {
        const wasActive = subscription.status === 'active';

        // Cancel the subscription
        subscription.status = 'cancelled';
        subscription.autoRenew = false;
        subscription.updatedAt = new Date();
        await subscription.save();

        // Create refund transaction if requested and subscription was active
        if (refundSubscriptions && wasActive) {
          // Calculate prorated refund based on remaining days
          const now = new Date();
          const periodEnd = new Date(subscription.currentPeriodEnd);
          const periodStart = new Date(subscription.currentPeriodStart);
          const totalDays = Math.ceil((periodEnd.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24));
          const remainingDays = Math.ceil((periodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

          if (remainingDays > 0) {
            const refundAmount = Math.round((subscription.monthlyPrice * remainingDays / totalDays) * 100); // Convert to cents

            const refundTransaction = new TransactionModel({
              userId: studentId,
              amount: refundAmount,
              type: 'refund',
              status: 'pending',
              description: `Prorated refund due to student account deletion - ${deleteReason} (${remainingDays}/${totalDays} days remaining)`
            });
            await refundTransaction.save();
          }
        }
      }
    }

    // Handle escrow transactions (refund any held amounts back to student)
    const heldEscrow = await EscrowModel.find({ studentId, status: 'held' });
    if (heldEscrow.length > 0) {
      for (const escrow of heldEscrow) {
        escrow.status = 'refunded';
        await escrow.save();

        // Create refund transaction
        const refundTransaction = new TransactionModel({
          userId: studentId,
          amount: escrow.amountHeld,
          type: 'refund',
          status: 'pending',
          description: `Escrow refund due to student account deletion - ${deleteReason}`
        });
        await refundTransaction.save();
      }
    }

    // Cancel any pending lessons
    await LessonModel.updateMany(
      { studentId, status: { $in: ['scheduled', 'confirmed'] } },
      {
        status: 'cancelled',
        cancellationReason: `Student account deleted - ${deleteReason}`,
        cancelledAt: new Date(),
        cancelledBy: req.user!._id
      }
    );

    // Soft delete the student (mark as deleted instead of removing)
    await Student.findByIdAndUpdate(studentId, {
      isActive: false,
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy: req.user!._id,
      deletionReason: deleteReason,
      email: `deleted_${Date.now()}_${student.email}` // Prevent email conflicts
    });

    res.json({
      success: true,
      message: "Student deleted successfully",
      data: {
        studentId,
        subscriptionsCancelled: subscriptions.length,
        escrowTransactionsRefunded: heldEscrow.length,
        refundSubscriptions,
        deletionReason: deleteReason
      }
    });
  } catch (error) {
    console.error("Error deleting student:", error);
    createErrorResponse(res, "Failed to delete student", 500);
  }
};

// ============================================================================
// SUBSCRIPTION MANAGEMENT
// ============================================================================

export const getAllSubscriptions = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (status) query.status = status;

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [subscriptions, total] = await Promise.all([
      Subscription.find(query)
        .populate('studentId', 'firstname lastname email')
        .populate('tutorId', 'firstname lastname email')
        .select('planType lessonsPerWeek monthlyPrice status currentPeriodStart currentPeriodEnd createdAt')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      Subscription.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: subscriptions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    createErrorResponse(res, "Failed to fetch subscriptions", 500);
  }
};

export const getSubscriptionById = async (req: Request, res: Response): Promise<void> => {
  const { subscriptionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(subscriptionId)) {
      createErrorResponse(res, "Invalid subscription ID format", 400);
      return;
    }

    const subscription = await Subscription.findById(subscriptionId)
      .populate('studentId')
      .populate('tutorId');

    if (!subscription) {
      createErrorResponse(res, "Subscription not found", 404);
      return;
    }

    res.json({
      success: true,
      data: subscription
    });
  } catch (error) {
    console.error("Error fetching subscription:", error);
    createErrorResponse(res, "Failed to fetch subscription", 500);
  }
};

export const cancelSubscription = async (req: AuthRequest, res: Response): Promise<void> => {
  const { subscriptionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(subscriptionId)) {
      createErrorResponse(res, "Invalid subscription ID format", 400);
      return;
    }

    const subscription = await Subscription.findByIdAndUpdate(
      subscriptionId,
      {
        status: 'cancelled',
        autoRenew: false
      },
      { new: true }
    );

    if (!subscription) {
      createErrorResponse(res, "Subscription not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Subscription cancelled successfully",
      data: subscription
    });
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    createErrorResponse(res, "Failed to cancel subscription", 500);
  }
};

// ============================================================================
// CONTENT MODERATION
// ============================================================================

export const getFlaggedItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const [flaggedTutors, flaggedReviews, total] = await Promise.all([
      Tutor.find({ isFlagged: true })
        .select('firstname lastname email isFlagged createdAt')
        .skip(skip)
        .limit(Number(limit)),

      Tutor.find({ "reviews.flagged": true })
        .select('firstname lastname email reviews')
        .skip(skip)
        .limit(Number(limit)),

      Promise.all([
        Tutor.countDocuments({ isFlagged: true }),
        Tutor.countDocuments({ "reviews.flagged": true })
      ]).then(([tutors, reviews]) => tutors + reviews)
    ]);

    res.json({
      success: true,
      data: {
        flaggedTutors,
        flaggedReviews: flaggedReviews.map(tutor => ({
          ...tutor.toObject(),
          reviews: tutor.reviews.filter(review => review.flagged)
        }))
      },
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching flagged items:", error);
    createErrorResponse(res, "Failed to fetch flagged items", 500);
  }
};

export const moderateContent = async (req: AuthRequest, res: Response): Promise<void> => {
  const { contentType, contentId, action } = req.body;

  try {
    if (!['approve', 'remove', 'flag'].includes(action)) {
      createErrorResponse(res, "Invalid action. Must be 'approve', 'remove', or 'flag'", 400);
      return;
    }

    let result;

    switch (contentType) {
      case 'tutor':
        if (action === 'flag') {
          result = await Tutor.findByIdAndUpdate(contentId, { isFlagged: true }, { new: true });
        } else if (action === 'approve') {
          result = await Tutor.findByIdAndUpdate(contentId, { isFlagged: false }, { new: true });
        }
        break;

      case 'review':
        // Handle review moderation
        const tutor = await Tutor.findOne({ "reviews._id": contentId });
        if (tutor) {
          const review = tutor.reviews.find((r: any) => r._id.toString() === contentId);
          if (review) {
            review.flagged = action === 'flag';
            result = await tutor.save();
          }
        }
        break;

      default:
        createErrorResponse(res, "Invalid content type", 400);
        return;
    }

    if (!result) {
      createErrorResponse(res, "Content not found", 404);
      return;
    }

    res.json({
      success: true,
      message: `Content ${action}ed successfully`,
      data: result
    });
  } catch (error) {
    console.error("Error moderating content:", error);
    createErrorResponse(res, "Failed to moderate content", 500);
  }
};

// ============================================================================
// ADMIN MANAGEMENT
// ============================================================================

export const getAllAdmins = async (req: Request, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 20, level, department } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    if (level) query.adminLevel = Number(level);
    if (department) query.department = department;

    const [admins, total] = await Promise.all([
      Admin.find(query)
        .select('firstname lastname email adminLevel department permissions isActive lastActiveAt createdAt')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Admin.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: admins,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching admins:", error);
    createErrorResponse(res, "Failed to fetch admins", 500);
  }
};

export const createAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  const {
    firstname,
    lastname,
    email,
    password,
    adminLevel,
    department,
    permissions
  } = req.body;

  try {
    // Validate required fields
    if (!firstname || !lastname || !email || !password) {
      createErrorResponse(res, "Missing required fields: firstname, lastname, email, password", 400);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      createErrorResponse(res, "Invalid email format", 400);
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      createErrorResponse(res, "Password must be at least 8 characters long", 400);
      return;
    }

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email });
    if (existingAdmin) {
      createErrorResponse(res, "Admin with this email already exists", 409);
      return;
    }

    // Hash the password
    const hashedPassword = await hashPassword(password);

    // Generate encryption data (required by Profile schema)
    const encryptionData = generateEncryptionData();

    // Validate admin level
    const validAdminLevel = adminLevel && [1, 2, 3, 4, 5].includes(adminLevel) ? adminLevel : 1;

    // Set default permissions based on admin level if not provided
    let adminPermissions = permissions || [];
    if (!permissions || permissions.length === 0) {
      adminPermissions = (Admin as any).getPermissionsByLevel(validAdminLevel);
    }

    const newAdmin = new Admin({
      firstname,
      lastname,
      email,
      password: hashedPassword,
      role: 'admin',
      adminLevel: validAdminLevel,
      department,
      permissions: adminPermissions,
      createdBy: req.user?.id,
      isActive: true,
      encryptedData: encryptionData,
      settings: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        emailNotifications: {
          newTutorApplications: true,
          flaggedContent: true,
          systemAlerts: true,
          financialReports: false
        },
        dashboardPreferences: {
          defaultView: 'overview',
          widgetOrder: [],
          autoRefresh: false,
          refreshInterval: 300
        }
      }
    });

    await newAdmin.save();

    // Log the admin creation activity
    if (req.user) {
      const creatorAdmin = await Admin.findById(req.user.id);
      if (creatorAdmin) {
        creatorAdmin.addActivity({
          action: 'CREATE_ADMIN',
          targetType: 'user',
          targetId: newAdmin._id as Types.ObjectId,
          details: {
            adminEmail: email,
            adminLevel: validAdminLevel,
            department: department || 'Not specified',
            permissions: adminPermissions
          }
        });
        await creatorAdmin.save();
      }
    }

    // Remove password from response
    const adminResponse = newAdmin.toObject();
    delete adminResponse.password;

    res.status(201).json({
      success: true,
      message: "Admin created successfully",
      data: {
        admin: adminResponse,
        summary: {
          adminLevel: validAdminLevel,
          permissionsCount: adminPermissions.length,
          department: department || 'Not specified',
          isActive: true
        }
      }
    });
  } catch (error: any) {
    console.error("Error creating admin:", error);

    // Handle specific MongoDB errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern || {})[0];
      createErrorResponse(res, `Admin with this ${field} already exists`, 409);
      return;
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      createErrorResponse(res, `Validation error: ${validationErrors.join(', ')}`, 400);
      return;
    }

    createErrorResponse(res, "Failed to create admin", 500);
  }
};

export const updateAdminPermissions = async (req: AuthRequest, res: Response): Promise<void> => {
  const { adminId } = req.params;
  const { permissions, adminLevel } = req.body;

  try {
    if (!Types.ObjectId.isValid(adminId)) {
      createErrorResponse(res, "Invalid admin ID format", 400);
      return;
    }

    // Validate required fields
    if (!permissions && adminLevel === undefined) {
      createErrorResponse(res, "At least one field (permissions or adminLevel) is required", 400);
      return;
    }

    // Validate permissions array if provided
    if (permissions && !Array.isArray(permissions)) {
      createErrorResponse(res, "Permissions must be an array", 400);
      return;
    }

    // Validate adminLevel if provided
    if (adminLevel !== undefined && (typeof adminLevel !== 'number' || adminLevel < 1 || adminLevel > 5)) {
      createErrorResponse(res, "Admin level must be a number between 1 and 5", 400);
      return;
    }

    const admin = await Admin.findByIdAndUpdate(
      adminId,
      {
        permissions,
        adminLevel,
        modifiedBy: req.user?._id,
        modifiedAt: new Date()
      },
      { new: true }
    ).select('-password');

    if (!admin) {
      createErrorResponse(res, "Admin not found", 404);
      return;
    }

    res.json({
      success: true,
      message: "Admin permissions updated successfully",
      data: admin
    });
  } catch (error) {
    console.error("Error updating admin permissions:", error);
    createErrorResponse(res, "Failed to update admin permissions", 500);
  }
};

// ============================================================================
// FINANCIAL MANAGEMENT & PAYMENT TRACKING
// ============================================================================

export const getFinancialOverview = async (_req: AuthRequest, res: Response): Promise<void> => {
  try {
    const [
      totalRevenue,
      monthlyRevenue,
      totalTransactions,
      pendingPayouts,
      escrowBalance,
      refundedAmount,
      platformFees,
      activeSubscriptions,
      failedPayments
    ] = await Promise.all([
      // Total revenue from all successful transactions
      TransactionModel.aggregate([
        { $match: { status: 'completed', type: { $in: ['subscription_payment', 'fee'] } } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Monthly revenue (current month)
      TransactionModel.aggregate([
        {
          $match: {
            status: 'completed',
            type: { $in: ['subscription_payment', 'fee'] },
            createdAt: {
              $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Total transaction count
      TransactionModel.countDocuments(),

      // Pending tutor payouts
      TransactionModel.aggregate([
        { $match: { status: 'pending', type: 'lesson_payout' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Total amount in escrow
      EscrowModel.aggregate([
        { $match: { status: 'held' } },
        { $group: { _id: null, total: { $sum: '$amountHeld' } } }
      ]).then(result => result[0]?.total || 0),

      // Total refunded amount
      TransactionModel.aggregate([
        { $match: { status: 'completed', type: 'refund' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),

      // Platform fees collected
      EscrowModel.aggregate([
        { $match: { status: { $in: ['released', 'held'] } } },
        { $group: { _id: null, total: { $sum: '$platformFee' } } }
      ]).then(result => result[0]?.total || 0),

      // Active subscriptions count
      Subscription.countDocuments({ status: 'active' }),

      // Failed payments count (last 30 days)
      TransactionModel.countDocuments({
        status: 'failed',
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      })
    ]);

    // Recent transactions
    const recentTransactions = await TransactionModel.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('userId', 'firstname lastname email')
      .select('amount type status description createdAt');

    // Payment method breakdown
    const paymentMethodStats = await Subscription.aggregate([
      { $match: { status: 'active' } },
      {
        $group: {
          _id: '$planType',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$monthlyPrice' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalRevenue: totalRevenue / 100, // Convert from cents
          monthlyRevenue: monthlyRevenue / 100,
          totalTransactions,
          pendingPayouts: pendingPayouts / 100,
          escrowBalance: escrowBalance / 100,
          refundedAmount: refundedAmount / 100,
          platformFees: platformFees / 100,
          activeSubscriptions,
          failedPayments
        },
        recentTransactions,
        paymentMethodStats
      }
    });
  } catch (error) {
    console.error("Error fetching financial overview:", error);
    createErrorResponse(res, "Failed to fetch financial overview", 500);
  }
};

export const getAllTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      userId,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (type) query.type = type;
    if (status) query.status = status;
    if (userId && Types.ObjectId.isValid(userId as string)) {
      query.userId = new Types.ObjectId(userId as string);
    }
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [transactions, total, summary] = await Promise.all([
      TransactionModel.find(query)
        .populate('userId', 'firstname lastname email role')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      TransactionModel.countDocuments(query),
      TransactionModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' },
            completedAmount: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, '$amount', 0] }
            },
            pendingAmount: {
              $sum: { $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0] }
            },
            failedAmount: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, '$amount', 0] }
            }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: transactions,
      summary: summary[0] ? {
        totalAmount: summary[0].totalAmount / 100,
        completedAmount: summary[0].completedAmount / 100,
        pendingAmount: summary[0].pendingAmount / 100,
        failedAmount: summary[0].failedAmount / 100
      } : null,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    createErrorResponse(res, "Failed to fetch transactions", 500);
  }
};

export const getTransactionById = async (req: Request, res: Response): Promise<void> => {
  const { transactionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(transactionId)) {
      createErrorResponse(res, "Invalid transaction ID format", 400);
      return;
    }

    const transaction = await TransactionModel.findById(transactionId)
      .populate('userId', 'firstname lastname email role');

    if (!transaction) {
      createErrorResponse(res, "Transaction not found", 404);
      return;
    }

    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error("Error fetching transaction:", error);
    createErrorResponse(res, "Failed to fetch transaction", 500);
  }
};

export const getAllEscrowTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      tutorId,
      studentId,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (status) query.status = status;
    if (tutorId && Types.ObjectId.isValid(tutorId as string)) {
      query.tutorId = new Types.ObjectId(tutorId as string);
    }
    if (studentId && Types.ObjectId.isValid(studentId as string)) {
      query.studentId = new Types.ObjectId(studentId as string);
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [escrowTransactions, total, summary] = await Promise.all([
      EscrowModel.find(query)
        .populate('tutorId', 'firstname lastname email')
        .populate('studentId', 'firstname lastname email')
        .populate('lessonId', 'title scheduledAt')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),
      EscrowModel.countDocuments(query),
      EscrowModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalHeld: { $sum: '$amountHeld' },
            totalFees: { $sum: '$platformFee' },
            totalPayouts: { $sum: '$tutorPayout' },
            heldCount: { $sum: { $cond: [{ $eq: ['$status', 'held'] }, 1, 0] } },
            releasedCount: { $sum: { $cond: [{ $eq: ['$status', 'released'] }, 1, 0] } },
            refundedCount: { $sum: { $cond: [{ $eq: ['$status', 'refunded'] }, 1, 0] } }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: escrowTransactions,
      summary: summary[0] ? {
        totalHeld: summary[0].totalHeld / 100,
        totalFees: summary[0].totalFees / 100,
        totalPayouts: summary[0].totalPayouts / 100,
        heldCount: summary[0].heldCount,
        releasedCount: summary[0].releasedCount,
        refundedCount: summary[0].refundedCount
      } : null,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching escrow transactions:", error);
    createErrorResponse(res, "Failed to fetch escrow transactions", 500);
  }
};

export const releaseEscrowFunds = async (req: AuthRequest, res: Response): Promise<void> => {
  const { escrowId } = req.params;
  const { reason } = req.body;

  try {
    if (!Types.ObjectId.isValid(escrowId)) {
      createErrorResponse(res, "Invalid escrow ID format", 400);
      return;
    }

    const escrow = await EscrowModel.findById(escrowId)
      .populate('tutorId', 'firstname lastname email')
      .populate('lessonId', 'title');

    if (!escrow) {
      createErrorResponse(res, "Escrow transaction not found", 404);
      return;
    }

    if (escrow.status !== 'held') {
      createErrorResponse(res, "Escrow funds can only be released from 'held' status", 400);
      return;
    }

    // Update escrow status
    escrow.status = 'released';
    escrow.releasedAt = new Date();
    await escrow.save();

    // Create payout transaction for tutor
    const payoutTransaction = new TransactionModel({
      userId: escrow.tutorId,
      amount: escrow.tutorPayout,
      type: 'lesson_payout',
      status: 'completed',
      description: `Payout for lesson: ${(escrow as any).lessonId?.title || 'Unknown'} - ${reason || 'Admin release'}`
    });
    await payoutTransaction.save();

    res.json({
      success: true,
      message: "Escrow funds released successfully",
      data: {
        escrow,
        payoutTransaction
      }
    });
  } catch (error) {
    console.error("Error releasing escrow funds:", error);
    createErrorResponse(res, "Failed to release escrow funds", 500);
  }
};

export const processRefund = async (req: AuthRequest, res: Response): Promise<void> => {
  const { subscriptionId } = req.params;
  const { amount, reason, refundType = 'partial' } = req.body;

  try {
    if (!Types.ObjectId.isValid(subscriptionId)) {
      createErrorResponse(res, "Invalid subscription ID format", 400);
      return;
    }

    if (!amount || amount <= 0) {
      createErrorResponse(res, "Valid refund amount is required", 400);
      return;
    }

    if (!reason) {
      createErrorResponse(res, "Refund reason is required", 400);
      return;
    }

    const subscription = await Subscription.findById(subscriptionId)
      .populate('studentId', 'firstname lastname email');

    if (!subscription) {
      createErrorResponse(res, "Subscription not found", 404);
      return;
    }

    // Create refund transaction
    const refundTransaction = new TransactionModel({
      userId: subscription.studentId,
      amount: Math.round(amount * 100), // Convert to cents
      type: 'refund',
      status: 'completed',
      description: `${refundType} refund for subscription ${subscription.planType} - ${reason}`
    });
    await refundTransaction.save();

    // Update subscription status if full refund
    if (refundType === 'full') {
      subscription.status = 'cancelled';
      await subscription.save();
    }

    res.json({
      success: true,
      message: "Refund processed successfully",
      data: {
        refundTransaction,
        subscription: refundType === 'full' ? subscription : null
      }
    });
  } catch (error) {
    console.error("Error processing refund:", error);
    createErrorResponse(res, "Failed to process refund", 500);
  }
};

// ============================================================================
// WITHDRAWAL REQUEST MANAGEMENT
// ============================================================================

export const getAllWithdrawalRequests = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, status, tutorId } = req.query;

    const query: any = {};
    if (status) query.status = status;
    if (tutorId && Types.ObjectId.isValid(tutorId as string)) {
      query.tutorId = tutorId;
    }

    const withdrawalRequests = await WithdrawalRequest.find(query)
      .populate('tutorId', 'firstname lastname email availableBalance')
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await WithdrawalRequest.countDocuments(query);

    // Get summary statistics
    const summary = await WithdrawalRequest.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    res.json({
      success: true,
      data: withdrawalRequests,
      summary: summary.reduce((acc, item) => {
        acc[item._id] = {
          count: item.count,
          totalAmount: item.totalAmount / 100 // Convert to dollars
        };
        return acc;
      }, {}),
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching withdrawal requests:", error);
    createErrorResponse(res, "Failed to fetch withdrawal requests", 500);
  }
};

export const getWithdrawalRequestById = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    const withdrawalRequest = await WithdrawalRequest.findById(requestId)
      .populate('tutorId', 'firstname lastname email availableBalance')
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId');

    if (!withdrawalRequest) {
      createErrorResponse(res, "Withdrawal request not found", 404);
      return;
    }

    res.json({
      success: true,
      data: withdrawalRequest
    });
  } catch (error) {
    console.error("Error fetching withdrawal request:", error);
    createErrorResponse(res, "Failed to fetch withdrawal request", 500);
  }
};

export const approveWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    const withdrawalRequest = await WithdrawalRequest.findById(requestId)
      .populate('tutorId');

    if (!withdrawalRequest) {
      createErrorResponse(res, "Withdrawal request not found", 404);
      return;
    }

    if (!withdrawalRequest.canBeApproved()) {
      createErrorResponse(res, `Cannot approve withdrawal request in ${withdrawalRequest.status} status`, 400);
      return;
    }

    const tutor = withdrawalRequest.tutorId as any;
    if (!tutor.canWithdraw(withdrawalRequest.amount)) {
      createErrorResponse(res, "Tutor has insufficient balance for this withdrawal", 400);
      return;
    }

    // Update withdrawal request
    withdrawalRequest.status = 'approved';
    withdrawalRequest.processedBy = req.user!._id as any;
    withdrawalRequest.processedAt = new Date();
    if (adminNotes) {
      withdrawalRequest.adminNotes = adminNotes;
    }

    await withdrawalRequest.save();

    res.json({
      success: true,
      message: "Withdrawal request approved successfully",
      data: withdrawalRequest
    });
  } catch (error) {
    console.error("Error approving withdrawal request:", error);
    createErrorResponse(res, "Failed to approve withdrawal request", 500);
  }
};

export const rejectWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { rejectionReason, adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    if (!rejectionReason) {
      createErrorResponse(res, "Rejection reason is required", 400);
      return;
    }

    const withdrawalRequest = await WithdrawalRequest.findById(requestId);

    if (!withdrawalRequest) {
      createErrorResponse(res, "Withdrawal request not found", 404);
      return;
    }

    if (!withdrawalRequest.canBeRejected()) {
      createErrorResponse(res, `Cannot reject withdrawal request in ${withdrawalRequest.status} status`, 400);
      return;
    }

    // Update withdrawal request
    withdrawalRequest.status = 'rejected';
    withdrawalRequest.rejectionReason = rejectionReason;
    withdrawalRequest.processedBy = req.user!._id as any;
    withdrawalRequest.processedAt = new Date();
    if (adminNotes) {
      withdrawalRequest.adminNotes = adminNotes;
    }

    await withdrawalRequest.save();

    res.json({
      success: true,
      message: "Withdrawal request rejected successfully",
      data: withdrawalRequest
    });
  } catch (error) {
    console.error("Error rejecting withdrawal request:", error);
    createErrorResponse(res, "Failed to reject withdrawal request", 500);
  }
};

export const processWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { stripeTransferId, adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    const withdrawalRequest = await WithdrawalRequest.findById(requestId)
      .populate('tutorId');

    if (!withdrawalRequest) {
      createErrorResponse(res, "Withdrawal request not found", 404);
      return;
    }

    if (withdrawalRequest.status !== 'approved') {
      createErrorResponse(res, "Only approved withdrawal requests can be processed", 400);
      return;
    }

    const tutor = withdrawalRequest.tutorId as any;

    // Create transaction record
    const transaction = new TransactionModel({
      userId: tutor._id,
      amount: withdrawalRequest.amount,
      type: 'withdrawal',
      status: 'completed',
      description: `Withdrawal payout - Request #${withdrawalRequest._id}`,
      stripeTransactionId: stripeTransferId
    });

    await transaction.save();

    // Update tutor balance
    tutor.availableBalance -= withdrawalRequest.amount;
    await tutor.save();

    // Update withdrawal request
    withdrawalRequest.status = 'processed';
    withdrawalRequest.processedBy = req.user!._id as any;
    withdrawalRequest.processedAt = new Date();
    withdrawalRequest.transactionId = transaction._id as any;
    if (stripeTransferId) {
      withdrawalRequest.stripeTransferId = stripeTransferId;
    }
    if (adminNotes) {
      withdrawalRequest.adminNotes = adminNotes;
    }

    await withdrawalRequest.save();

    // Send payout confirmation email notification
    try {
      await EmailNotificationService.sendPayoutConfirmation(
        {
          id: tutor._id.toString(),
          firstname: tutor.firstname,
          lastname: tutor.lastname,
          email: tutor.email
        },
        {
          amount: withdrawalRequest.amount,
          currency: 'usd',
          payoutDate: new Date(),
          method: withdrawalRequest.bankDetails.bankName || 'Bank Transfer',
          transactionId: (transaction._id as Types.ObjectId).toString()
        }
      );
      console.log(`Payout confirmation email sent to ${tutor.email}`);
    } catch (emailError: any) {
      console.error('Error sending payout confirmation email:', emailError);
      // Don't fail the withdrawal processing if email sending fails
    }

    res.json({
      success: true,
      message: "Withdrawal request processed successfully",
      data: {
        withdrawalRequest,
        transaction,
        tutorNewBalance: tutor.availableBalance / 100
      }
    });
  } catch (error) {
    console.error("Error processing withdrawal request:", error);
    createErrorResponse(res, "Failed to process withdrawal request", 500);
  }
};

/**
 * Update payout status with enhanced status handling (pending, failed, paid)
 */
export const updatePayoutStatus = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { status, failureReason, stripeTransferId, adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    // Validate status
    const validStatuses = ['pending', 'failed', 'paid'];
    if (!validStatuses.includes(status)) {
      createErrorResponse(res, `Invalid status. Must be one of: ${validStatuses.join(', ')}`, 400);
      return;
    }

    const withdrawalRequest = await WithdrawalRequest.findById(requestId)
      .populate('tutorId');

    if (!withdrawalRequest) {
      createErrorResponse(res, "Withdrawal request not found", 404);
      return;
    }

    // Only allow status updates for approved requests
    if (withdrawalRequest.status !== 'approved' && withdrawalRequest.status !== 'pending') {
      createErrorResponse(res, `Cannot update status for withdrawal request in ${withdrawalRequest.status} status`, 400);
      return;
    }

    const tutor = withdrawalRequest.tutorId as any;
    let transaction = null;

    // Handle different status updates
    switch (status) {
      case 'pending':
        // Set to pending (useful for reverting failed attempts)
        withdrawalRequest.status = 'approved'; // Keep as approved but mark as pending processing
        withdrawalRequest.adminNotes = `${withdrawalRequest.adminNotes || ''}\nStatus updated to pending: ${adminNotes || 'Processing initiated'}`.trim();
        break;

      case 'failed':
        // Mark as failed - don't deduct balance, create failed transaction
        if (!failureReason) {
          createErrorResponse(res, "Failure reason is required for failed status", 400);
          return;
        }

        transaction = new TransactionModel({
          userId: tutor._id,
          amount: withdrawalRequest.amount,
          type: 'withdrawal',
          status: 'failed',
          description: `Failed withdrawal payout - Request #${withdrawalRequest._id}: ${failureReason}`,
          stripeTransactionId: stripeTransferId || null
        });

        await transaction.save();

        withdrawalRequest.status = 'rejected'; // Use existing rejected status for failed payouts
        withdrawalRequest.rejectionReason = `Payout failed: ${failureReason}`;
        withdrawalRequest.transactionId = transaction._id as any;
        break;

      case 'paid':
        // Mark as successfully paid - deduct balance, create completed transaction
        if (tutor.availableBalance < withdrawalRequest.amount) {
          createErrorResponse(res, "Tutor has insufficient balance for this withdrawal", 400);
          return;
        }

        transaction = new TransactionModel({
          userId: tutor._id,
          amount: withdrawalRequest.amount,
          type: 'withdrawal',
          status: 'completed',
          description: `Successful withdrawal payout - Request #${withdrawalRequest._id}`,
          stripeTransactionId: stripeTransferId || null
        });

        await transaction.save();

        // Update tutor balance
        tutor.availableBalance -= withdrawalRequest.amount;
        await tutor.save();

        withdrawalRequest.status = 'processed';
        withdrawalRequest.transactionId = transaction._id as any;
        break;
    }

    // Update common fields
    withdrawalRequest.processedBy = req.user!._id as any;
    withdrawalRequest.processedAt = new Date();

    if (stripeTransferId) {
      withdrawalRequest.stripeTransferId = stripeTransferId;
    }

    if (adminNotes && status !== 'pending') {
      withdrawalRequest.adminNotes = `${withdrawalRequest.adminNotes || ''}\n${adminNotes}`.trim();
    }

    await withdrawalRequest.save();

    res.json({
      success: true,
      message: `Payout status updated to ${status} successfully`,
      data: {
        withdrawalRequest,
        transaction,
        tutorNewBalance: tutor.availableBalance / 100,
        statusUpdate: {
          previousStatus: withdrawalRequest.status,
          newStatus: status,
          processedBy: req.user!._id,
          processedAt: new Date()
        }
      }
    });
  } catch (error) {
    console.error("Error updating payout status:", error);
    createErrorResponse(res, "Failed to update payout status", 500);
  }
};

/**
 * Bulk update payout status for multiple withdrawal requests
 */
export const bulkUpdatePayoutStatus = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestIds, status, failureReason, adminNotes } = req.body;

  try {
    // Validate input
    if (!Array.isArray(requestIds) || requestIds.length === 0) {
      createErrorResponse(res, "Request IDs array is required and cannot be empty", 400);
      return;
    }

    const validStatuses = ['pending', 'failed', 'paid'];
    if (!validStatuses.includes(status)) {
      createErrorResponse(res, `Invalid status. Must be one of: ${validStatuses.join(', ')}`, 400);
      return;
    }

    if (status === 'failed' && !failureReason) {
      createErrorResponse(res, "Failure reason is required for failed status", 400);
      return;
    }

    // Validate all request IDs
    const invalidIds = requestIds.filter(id => !Types.ObjectId.isValid(id));
    if (invalidIds.length > 0) {
      createErrorResponse(res, `Invalid request ID format: ${invalidIds.join(', ')}`, 400);
      return;
    }

    const results: {
      successful: Array<{
        requestId: string;
        newStatus: string;
        transactionId?: string;
      }>;
      failed: Array<{
        requestId: string;
        error: string;
      }>;
      totalProcessed: number;
    } = {
      successful: [],
      failed: [],
      totalProcessed: 0
    };

    // Process each withdrawal request
    for (const requestId of requestIds) {
      try {
        const withdrawalRequest = await WithdrawalRequest.findById(requestId)
          .populate('tutorId');

        if (!withdrawalRequest) {
          results.failed.push({
            requestId,
            error: 'Withdrawal request not found'
          });
          continue;
        }

        if (withdrawalRequest.status !== 'approved' && withdrawalRequest.status !== 'pending') {
          results.failed.push({
            requestId,
            error: `Cannot update status for request in ${withdrawalRequest.status} status`
          });
          continue;
        }

        const tutor = withdrawalRequest.tutorId as any;
        let transaction = null;

        // Handle different status updates
        switch (status) {
          case 'pending':
            withdrawalRequest.status = 'approved';
            withdrawalRequest.adminNotes = `${withdrawalRequest.adminNotes || ''}\nBulk update to pending: ${adminNotes || 'Processing initiated'}`.trim();
            break;

          case 'failed':
            transaction = new TransactionModel({
              userId: tutor._id,
              amount: withdrawalRequest.amount,
              type: 'withdrawal',
              status: 'failed',
              description: `Failed withdrawal payout (bulk) - Request #${withdrawalRequest._id}: ${failureReason}`,
            });

            await transaction.save();

            withdrawalRequest.status = 'rejected';
            withdrawalRequest.rejectionReason = `Bulk payout failed: ${failureReason}`;
            withdrawalRequest.transactionId = transaction._id as any;
            break;

          case 'paid':
            if (tutor.availableBalance < withdrawalRequest.amount) {
              results.failed.push({
                requestId,
                error: 'Tutor has insufficient balance for this withdrawal'
              });
              continue;
            }

            transaction = new TransactionModel({
              userId: tutor._id,
              amount: withdrawalRequest.amount,
              type: 'withdrawal',
              status: 'completed',
              description: `Successful withdrawal payout (bulk) - Request #${withdrawalRequest._id}`,
            });

            await transaction.save();

            tutor.availableBalance -= withdrawalRequest.amount;
            await tutor.save();

            withdrawalRequest.status = 'processed';
            withdrawalRequest.transactionId = transaction._id as any;
            break;
        }

        // Update common fields
        withdrawalRequest.processedBy = req.user!._id as any;
        withdrawalRequest.processedAt = new Date();

        if (adminNotes && status !== 'pending') {
          withdrawalRequest.adminNotes = `${withdrawalRequest.adminNotes || ''}\nBulk update: ${adminNotes}`.trim();
        }

        await withdrawalRequest.save();

        results.successful.push({
          requestId,
          newStatus: status,
          transactionId: transaction?._id?.toString()
        });
        results.totalProcessed++;

      } catch (error: any) {
        results.failed.push({
          requestId,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: `Bulk payout status update completed. ${results.successful.length} successful, ${results.failed.length} failed.`,
      data: results
    });
  } catch (error) {
    console.error("Error in bulk payout status update:", error);
    createErrorResponse(res, "Failed to update payout statuses", 500);
  }
};

/**
 * Get payout status history and analytics
 */
export const getPayoutStatusAnalytics = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { startDate, endDate, tutorId, status } = req.query;

    // Build date filter
    const dateFilter: any = {};
    if (startDate) dateFilter.$gte = new Date(startDate as string);
    if (endDate) dateFilter.$lte = new Date(endDate as string);

    // Build match stage
    const matchStage: any = {};
    if (Object.keys(dateFilter).length > 0) {
      matchStage.createdAt = dateFilter;
    }
    if (tutorId && Types.ObjectId.isValid(tutorId as string)) {
      matchStage.tutorId = new Types.ObjectId(tutorId as string);
    }
    if (status) {
      matchStage.status = status;
    }

    const [
      statusBreakdown,
      dailyTrends,
      tutorBreakdown,
      recentActivity
    ] = await Promise.all([
      // Status breakdown
      WithdrawalRequest.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' },
            avgAmount: { $avg: '$amount' }
          }
        }
      ]),

      // Daily trends
      WithdrawalRequest.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
              status: '$status'
            },
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' }
          }
        },
        { $sort: { '_id.date': -1 } },
        { $limit: 30 }
      ]),

      // Tutor breakdown (top 10)
      WithdrawalRequest.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$tutorId',
            totalRequests: { $sum: 1 },
            totalAmount: { $sum: '$amount' },
            pendingCount: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
            approvedCount: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
            processedCount: { $sum: { $cond: [{ $eq: ['$status', 'processed'] }, 1, 0] } },
            rejectedCount: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } }
          }
        },
        { $sort: { totalAmount: -1 } },
        { $limit: 10 },
        {
          $lookup: {
            from: 'tutors',
            localField: '_id',
            foreignField: '_id',
            as: 'tutor'
          }
        },
        { $unwind: '$tutor' }
      ]),

      // Recent activity
      WithdrawalRequest.find(matchStage)
        .populate('tutorId', 'firstname lastname email')
        .populate('processedBy', 'firstname lastname')
        .sort({ updatedAt: -1 })
        .limit(20)
    ]);

    res.json({
      success: true,
      data: {
        statusBreakdown: statusBreakdown.map(item => ({
          status: item._id,
          count: item.count,
          totalAmount: item.totalAmount / 100,
          avgAmount: item.avgAmount / 100
        })),
        dailyTrends: dailyTrends.map(item => ({
          date: item._id.date,
          status: item._id.status,
          count: item.count,
          totalAmount: item.totalAmount / 100
        })),
        tutorBreakdown: tutorBreakdown.map(item => ({
          tutor: {
            id: item._id,
            name: `${item.tutor.firstname} ${item.tutor.lastname}`,
            email: item.tutor.email
          },
          totalRequests: item.totalRequests,
          totalAmount: item.totalAmount / 100,
          statusCounts: {
            pending: item.pendingCount,
            approved: item.approvedCount,
            processed: item.processedCount,
            rejected: item.rejectedCount
          }
        })),
        recentActivity
      }
    });
  } catch (error) {
    console.error("Error fetching payout status analytics:", error);
    createErrorResponse(res, "Failed to fetch payout status analytics", 500);
  }
};

export const getWithdrawalStatistics = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;

    const dateFilter: any = {};
    if (startDate) dateFilter.$gte = new Date(startDate as string);
    if (endDate) dateFilter.$lte = new Date(endDate as string);

    const matchStage: any = {};
    if (Object.keys(dateFilter).length > 0) {
      matchStage.createdAt = dateFilter;
    }

    const stats = await WithdrawalRequest.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Get total tutors with available balance
    const tutorsWithBalance = await Tutor.countDocuments({
      availableBalance: { $gt: 0 }
    });

    // Get total available balance across all tutors
    const totalBalanceResult = await Tutor.aggregate([
      {
        $group: {
          _id: null,
          totalBalance: { $sum: '$availableBalance' }
        }
      }
    ]);

    const totalBalance = totalBalanceResult[0]?.totalBalance || 0;

    res.json({
      success: true,
      data: {
        withdrawalStats: stats.reduce((acc, item) => {
          acc[item._id] = {
            count: item.count,
            totalAmount: item.totalAmount / 100,
            avgAmount: item.avgAmount / 100
          };
          return acc;
        }, {}),
        tutorsWithBalance,
        totalAvailableBalance: totalBalance / 100
      }
    });
  } catch (error) {
    console.error("Error fetching withdrawal statistics:", error);
    createErrorResponse(res, "Failed to fetch withdrawal statistics", 500);
  }
};

export const getFinancialReports = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      reportType = 'monthly',
      startDate,
      endDate,
      year = new Date().getFullYear()
    } = req.query;

    let dateFilter: any = {};

    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate as string),
          $lte: new Date(endDate as string)
        }
      };
    } else if (reportType === 'monthly') {
      // Current year monthly breakdown
      dateFilter = {
        createdAt: {
          $gte: new Date(Number(year), 0, 1),
          $lt: new Date(Number(year) + 1, 0, 1)
        }
      };
    }

    const [
      revenueByMonth,
      transactionsByType,
      subscriptionMetrics,
      tutorPayouts,
      refundAnalysis
    ] = await Promise.all([
      // Monthly revenue breakdown
      TransactionModel.aggregate([
        {
          $match: {
            ...dateFilter,
            status: 'completed',
            type: { $in: ['subscription_payment', 'fee'] }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            revenue: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]),

      // Transaction breakdown by type
      TransactionModel.aggregate([
        { $match: { ...dateFilter, status: 'completed' } },
        {
          $group: {
            _id: '$type',
            total: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Subscription metrics
      Subscription.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalRevenue: { $sum: '$monthlyPrice' }
          }
        }
      ]),

      // Tutor payout summary
      TransactionModel.aggregate([
        {
          $match: {
            ...dateFilter,
            type: 'lesson_payout',
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            totalPayouts: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Refund analysis
      TransactionModel.aggregate([
        {
          $match: {
            ...dateFilter,
            type: 'refund',
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            totalRefunds: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: {
        reportType,
        period: { startDate, endDate, year },
        revenueByMonth: revenueByMonth.map(item => ({
          ...item,
          revenue: item.revenue / 100
        })),
        transactionsByType: transactionsByType.map(item => ({
          ...item,
          total: item.total / 100
        })),
        subscriptionMetrics,
        tutorPayouts: tutorPayouts[0] ? {
          totalPayouts: tutorPayouts[0].totalPayouts / 100,
          count: tutorPayouts[0].count
        } : { totalPayouts: 0, count: 0 },
        refundAnalysis: refundAnalysis[0] ? {
          totalRefunds: refundAnalysis[0].totalRefunds / 100,
          count: refundAnalysis[0].count
        } : { totalRefunds: 0, count: 0 }
      }
    });
  } catch (error) {
    console.error("Error generating financial reports:", error);
    createErrorResponse(res, "Failed to generate financial reports", 500);
  }
};

export const getPaymentMethodAnalytics = async (_req: AuthRequest, res: Response): Promise<void> => {
  try {
    const [
      subscriptionsByPlan,
      paymentFailureRate,
      averageSubscriptionValue,
      churnAnalysis
    ] = await Promise.all([
      // Subscription breakdown by plan type
      Subscription.aggregate([
        { $match: { status: 'active' } },
        {
          $group: {
            _id: '$planType',
            count: { $sum: 1 },
            totalRevenue: { $sum: '$monthlyPrice' },
            avgPrice: { $avg: '$monthlyPrice' }
          }
        },
        { $sort: { count: -1 } }
      ]),

      // Payment failure rate (last 30 days)
      TransactionModel.aggregate([
        {
          $match: {
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
            type: 'subscription_payment'
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]),

      // Average subscription value
      Subscription.aggregate([
        { $match: { status: 'active' } },
        {
          $group: {
            _id: null,
            avgMonthlyValue: { $avg: '$monthlyPrice' },
            totalActiveSubscriptions: { $sum: 1 }
          }
        }
      ]),

      // Churn analysis (cancelled subscriptions in last 30 days)
      Subscription.aggregate([
        {
          $match: {
            status: 'cancelled',
            updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: "%Y-%m-%d", date: "$updatedAt" }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    // Calculate failure rate percentage
    const totalPaymentAttempts = paymentFailureRate.reduce((sum, item) => sum + item.count, 0);
    const failedPayments = paymentFailureRate.find(item => item._id === 'failed')?.count || 0;
    const failureRatePercentage = totalPaymentAttempts > 0 ? (failedPayments / totalPaymentAttempts) * 100 : 0;

    res.json({
      success: true,
      data: {
        subscriptionsByPlan: subscriptionsByPlan.map(plan => ({
          ...plan,
          totalRevenue: plan.totalRevenue / 100,
          avgPrice: plan.avgPrice / 100
        })),
        paymentMetrics: {
          failureRate: Math.round(failureRatePercentage * 100) / 100,
          totalAttempts: totalPaymentAttempts,
          failedPayments,
          successfulPayments: totalPaymentAttempts - failedPayments
        },
        averageSubscriptionValue: averageSubscriptionValue[0] ? {
          avgMonthlyValue: averageSubscriptionValue[0].avgMonthlyValue / 100,
          totalActiveSubscriptions: averageSubscriptionValue[0].totalActiveSubscriptions
        } : null,
        churnAnalysis
      }
    });
  } catch (error) {
    console.error("Error fetching payment method analytics:", error);
    createErrorResponse(res, "Failed to fetch payment method analytics", 500);
  }
};

export const getTutorPayoutSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      tutorId,
      status = 'all',
      startDate,
      endDate
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    let query: any = { type: 'lesson_payout' };

    // Filters
    if (tutorId && Types.ObjectId.isValid(tutorId as string)) {
      query.userId = new Types.ObjectId(tutorId as string);
    }
    if (status !== 'all') {
      query.status = status;
    }
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const [payouts, total, summary] = await Promise.all([
      TransactionModel.find(query)
        .populate('userId', 'firstname lastname email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      TransactionModel.countDocuments(query),
      TransactionModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$status',
            total: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    // Get top earning tutors
    const topEarners = await TransactionModel.aggregate([
      {
        $match: {
          type: 'lesson_payout',
          status: 'completed',
          createdAt: startDate || endDate ? {
            ...(startDate && { $gte: new Date(startDate as string) }),
            ...(endDate && { $lte: new Date(endDate as string) })
          } : { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: '$userId',
          totalEarnings: { $sum: '$amount' },
          payoutCount: { $sum: 1 }
        }
      },
      { $sort: { totalEarnings: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'tutors',
          localField: '_id',
          foreignField: '_id',
          as: 'tutor'
        }
      },
      { $unwind: '$tutor' }
    ]);

    res.json({
      success: true,
      data: payouts,
      summary: summary.map(item => ({
        status: item._id,
        total: item.total / 100,
        count: item.count
      })),
      topEarners: topEarners.map(earner => ({
        tutor: {
          _id: earner._id,
          firstname: earner.tutor.firstname,
          lastname: earner.tutor.lastname,
          email: earner.tutor.email
        },
        totalEarnings: earner.totalEarnings / 100,
        payoutCount: earner.payoutCount
      })),
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching tutor payout summary:", error);
    createErrorResponse(res, "Failed to fetch tutor payout summary", 500);
  }
};

// ============================================================================
// STUDENT REFUND REQUEST MANAGEMENT
// ============================================================================

export const getAllRefundRequests = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, status, refundType, studentId } = req.query;

    const query: any = {};
    if (status) query.status = status;
    if (refundType) query.refundType = refundType;
    if (studentId && Types.ObjectId.isValid(studentId as string)) {
      query.studentId = studentId;
    }

    const refundRequests = await RefundRequest.find(query)
      .populate('studentId', 'firstname lastname email')
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId')
      .populate('subscriptionId', 'planType monthlyPrice status')
      .populate('lessonId', 'title scheduledAt status')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await RefundRequest.countDocuments(query);

    // Get summary statistics
    const summary = await RefundRequest.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    res.json({
      success: true,
      data: refundRequests,
      summary: summary.reduce((acc, item) => {
        acc[item._id] = {
          count: item.count,
          totalAmount: item.totalAmount / 100 // Convert to dollars
        };
        return acc;
      }, {} as any),
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching refund requests:", error);
    createErrorResponse(res, "Failed to fetch refund requests", 500);
  }
};

export const getRefundRequestByIdAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    const refundRequest = await RefundRequest.findById(requestId)
      .populate('studentId', 'firstname lastname email')
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId')
      .populate('subscriptionId')
      .populate('lessonId');

    if (!refundRequest) {
      createErrorResponse(res, "Refund request not found", 404);
      return;
    }

    res.json({
      success: true,
      data: refundRequest
    });
  } catch (error) {
    console.error("Error fetching refund request:", error);
    createErrorResponse(res, "Failed to fetch refund request", 500);
  }
};

export const approveRefundRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    const refundRequest = await RefundRequest.findById(requestId)
      .populate('studentId');

    if (!refundRequest) {
      createErrorResponse(res, "Refund request not found", 404);
      return;
    }

    if (!refundRequest.canBeApproved()) {
      createErrorResponse(res, `Cannot approve refund request in ${refundRequest.status} status`, 400);
      return;
    }

    // Update refund request
    refundRequest.status = 'approved';
    refundRequest.processedBy = req.user!._id as any;
    refundRequest.processedAt = new Date();
    if (adminNotes) {
      refundRequest.adminNotes = adminNotes;
    }

    await refundRequest.save();

    res.json({
      success: true,
      message: "Refund request approved successfully",
      data: refundRequest
    });
  } catch (error) {
    console.error("Error approving refund request:", error);
    createErrorResponse(res, "Failed to approve refund request", 500);
  }
};

export const rejectRefundRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { rejectionReason, adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    if (!rejectionReason) {
      createErrorResponse(res, "Rejection reason is required", 400);
      return;
    }

    const refundRequest = await RefundRequest.findById(requestId);

    if (!refundRequest) {
      createErrorResponse(res, "Refund request not found", 404);
      return;
    }

    if (!refundRequest.canBeRejected()) {
      createErrorResponse(res, `Cannot reject refund request in ${refundRequest.status} status`, 400);
      return;
    }

    // Update refund request
    refundRequest.status = 'rejected';
    refundRequest.rejectionReason = rejectionReason;
    refundRequest.processedBy = req.user!._id as any;
    refundRequest.processedAt = new Date();
    if (adminNotes) {
      refundRequest.adminNotes = adminNotes;
    }

    await refundRequest.save();

    res.json({
      success: true,
      message: "Refund request rejected successfully",
      data: refundRequest
    });
  } catch (error) {
    console.error("Error rejecting refund request:", error);
    createErrorResponse(res, "Failed to reject refund request", 500);
  }
};

export const processRefundRequest = async (req: AuthRequest, res: Response): Promise<void> => {
  const { requestId } = req.params;
  const { stripeRefundId, adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(requestId)) {
      createErrorResponse(res, "Invalid request ID format", 400);
      return;
    }

    const refundRequest = await RefundRequest.findById(requestId)
      .populate('studentId');

    if (!refundRequest) {
      createErrorResponse(res, "Refund request not found", 404);
      return;
    }

    if (refundRequest.status !== 'approved') {
      createErrorResponse(res, "Only approved refund requests can be processed", 400);
      return;
    }

    const student = refundRequest.studentId as any;

    // Create transaction record
    const transaction = new TransactionModel({
      userId: student._id,
      amount: refundRequest.amount,
      type: 'refund',
      status: 'completed',
      description: `Refund processed - Request #${refundRequest._id}: ${refundRequest.reason}`,
      stripeTransactionId: stripeRefundId
    });

    await transaction.save();

    // Update refund request
    refundRequest.status = 'processed';
    refundRequest.processedBy = req.user!._id as any;
    refundRequest.processedAt = new Date();
    refundRequest.transactionId = transaction._id as any;
    if (stripeRefundId) {
      refundRequest.stripeRefundId = stripeRefundId;
    }
    if (adminNotes) {
      refundRequest.adminNotes = adminNotes;
    }

    await refundRequest.save();

    res.json({
      success: true,
      message: "Refund request processed successfully",
      data: {
        refundRequest,
        transaction
      }
    });
  } catch (error) {
    console.error("Error processing refund request:", error);
    createErrorResponse(res, "Failed to process refund request", 500);
  }
};

export const getRefundStatistics = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;

    const dateFilter: any = {};
    if (startDate) dateFilter.$gte = new Date(startDate as string);
    if (endDate) dateFilter.$lte = new Date(endDate as string);

    const matchStage: any = {};
    if (Object.keys(dateFilter).length > 0) {
      matchStage.createdAt = dateFilter;
    }

    const stats = await RefundRequest.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Get refund type breakdown
    const typeBreakdown = await RefundRequest.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$refundType',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        refundStats: stats.reduce((acc, item) => {
          acc[item._id] = {
            count: item.count,
            totalAmount: item.totalAmount / 100,
            avgAmount: item.avgAmount / 100
          };
          return acc;
        }, {} as any),
        typeBreakdown: typeBreakdown.reduce((acc, item) => {
          acc[item._id] = {
            count: item.count,
            totalAmount: item.totalAmount / 100
          };
          return acc;
        }, {} as any)
      }
    });
  } catch (error) {
    console.error("Error fetching refund statistics:", error);
    createErrorResponse(res, "Failed to fetch refund statistics", 500);
  }
};

// ============================================================================
// TRANSACTION MANAGEMENT
// ============================================================================

export const getAllTransactionsAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      status,
      userId,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const query: any = {};

    // Filters
    if (type) query.type = type;
    if (category) query.category = category;
    if (status) query.status = status;
    if (userId && Types.ObjectId.isValid(userId as string)) {
      query.userId = new Types.ObjectId(userId as string);
    }
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [transactions, total, summary] = await Promise.all([
      TransactionModel.find(query)
        .populate('userId', 'firstname lastname email role')
        .populate('relatedUserId', 'firstname lastname email role')
        .populate('subscription', 'planType monthlyPrice status')
        .populate('lesson', 'title scheduledAt status')
        .populate('withdrawalRequest', 'amount status')
        .populate('refundRequest', 'amount status reason')
        .sort(sortOptions)
        .skip(skip)
        .limit(Number(limit)),

      TransactionModel.countDocuments(query),

      TransactionModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$category',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 },
            avgAmount: { $avg: '$amount' }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: transactions,
      summary: summary.reduce((acc, item) => {
        acc[item._id] = {
          totalAmount: item.totalAmount / 100,
          count: item.count,
          avgAmount: item.avgAmount / 100
        };
        return acc;
      }, {} as any),
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    createErrorResponse(res, "Failed to fetch transactions", 500);
  }
};

export const getTransactionByIdAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  const { transactionId } = req.params;

  try {
    if (!Types.ObjectId.isValid(transactionId)) {
      createErrorResponse(res, "Invalid transaction ID format", 400);
      return;
    }

    const transaction = await TransactionModel.findById(transactionId)
      .populate('userId', 'firstname lastname email role')
      .populate('relatedUserId', 'firstname lastname email role')
      .populate('subscription')
      .populate('lesson')
      .populate('withdrawalRequest')
      .populate('refundRequest');

    if (!transaction) {
      createErrorResponse(res, "Transaction not found", 404);
      return;
    }

    res.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error("Error fetching transaction:", error);
    createErrorResponse(res, "Failed to fetch transaction", 500);
  }
};

export const createManualTransactionAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const {
      userId,
      relatedUserId,
      amount,
      currency = 'usd',
      type,
      category,
      paymentMethod = 'manual',
      subscriptionId,
      lessonId,
      withdrawalRequestId,
      refundRequestId,
      stripeTransactionId,
      stripePaymentIntentId,
      stripeInvoiceId,
      stripeRefundId,
      paypalTransactionId,
      description,
      adminNotes,
      metadata,
      platformFeeAmount,
      processingFeeAmount
    } = req.body;

    // Validate required fields
    if (!userId || !amount || !type || !description) {
      createErrorResponse(res, "Missing required fields: userId, amount, type, description", 400);
      return;
    }

    if (!Types.ObjectId.isValid(userId)) {
      createErrorResponse(res, "Invalid user ID format", 400);
      return;
    }

    if (relatedUserId && !Types.ObjectId.isValid(relatedUserId)) {
      createErrorResponse(res, "Invalid related user ID format", 400);
      return;
    }

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      createErrorResponse(res, "Amount must be a positive number", 400);
      return;
    }

    const transaction = await TransactionService.createTransaction({
      userId: new Types.ObjectId(userId),
      relatedUserId: relatedUserId ? new Types.ObjectId(relatedUserId) : undefined,
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      type,
      category,
      status: 'completed',
      paymentMethod,
      subscriptionId: subscriptionId ? new Types.ObjectId(subscriptionId) : undefined,
      lessonId: lessonId ? new Types.ObjectId(lessonId) : undefined,
      withdrawalRequestId: withdrawalRequestId ? new Types.ObjectId(withdrawalRequestId) : undefined,
      refundRequestId: refundRequestId ? new Types.ObjectId(refundRequestId) : undefined,
      stripeTransactionId,
      stripePaymentIntentId,
      stripeInvoiceId,
      stripeRefundId,
      paypalTransactionId,
      description,
      adminNotes: `${adminNotes || ''}\nCreated by admin: ${req.user!.firstname} ${req.user!.lastname}`.trim(),
      metadata: {
        ...metadata,
        createdByAdmin: req.user!._id,
        createdByAdminName: `${req.user!.firstname} ${req.user!.lastname}`,
        createdAt: new Date()
      },
      platformFeeAmount: platformFeeAmount ? Math.round(platformFeeAmount * 100) : undefined,
      processingFeeAmount: processingFeeAmount ? Math.round(processingFeeAmount * 100) : undefined
    });

    res.status(201).json({
      success: true,
      message: "Manual transaction created successfully",
      data: transaction
    });
  } catch (error: any) {
    console.error("Error creating manual transaction:", error);
    createErrorResponse(res, error.message || "Failed to create manual transaction", 500);
  }
};

export const updateTransactionStatus = async (req: AuthRequest, res: Response): Promise<void> => {
  const { transactionId } = req.params;
  const { status, adminNotes } = req.body;

  try {
    if (!Types.ObjectId.isValid(transactionId)) {
      createErrorResponse(res, "Invalid transaction ID format", 400);
      return;
    }

    const validStatuses = ['pending', 'completed', 'failed', 'cancelled', 'processing'];
    if (!status || !validStatuses.includes(status)) {
      createErrorResponse(res, `Invalid status. Must be one of: ${validStatuses.join(', ')}`, 400);
      return;
    }

    const transaction = await TransactionModel.findById(transactionId);
    if (!transaction) {
      createErrorResponse(res, "Transaction not found", 404);
      return;
    }

    // Update transaction
    transaction.status = status;
    if (status === 'completed') {
      transaction.processedAt = new Date();
    } else if (status === 'failed') {
      transaction.failedAt = new Date();
    }

    if (adminNotes) {
      transaction.adminNotes = `${transaction.adminNotes || ''}\n${adminNotes}`.trim();
    }

    await transaction.save();

    res.json({
      success: true,
      message: `Transaction status updated to ${status} successfully`,
      data: transaction
    });
  } catch (error) {
    console.error("Error updating transaction status:", error);
    createErrorResponse(res, "Failed to update transaction status", 500);
  }
};

export const getTransactionAnalytics = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { startDate, endDate, groupBy = 'day' } = req.query;

    const analytics = await TransactionService.getFinancialAnalytics(
      startDate ? new Date(startDate as string) : undefined,
      endDate ? new Date(endDate as string) : undefined
    );

    // Get additional analytics
    const dateFilter: any = {};
    if (startDate) dateFilter.$gte = new Date(startDate as string);
    if (endDate) dateFilter.$lte = new Date(endDate as string);

    const matchStage: any = { status: 'completed' };
    if (Object.keys(dateFilter).length > 0) {
      matchStage.createdAt = dateFilter;
    }

    const [
      typeBreakdown,
      userTypeBreakdown,
      paymentMethodBreakdown,
      topUsers
    ] = await Promise.all([
      // Transaction type breakdown
      TransactionModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$type',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 },
            avgAmount: { $avg: '$amount' }
          }
        },
        { $sort: { totalAmount: -1 } }
      ]),

      // User type breakdown (students vs tutors)
      TransactionModel.aggregate([
        { $match: matchStage },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        {
          $group: {
            _id: '$user.role',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Payment method breakdown
      TransactionModel.aggregate([
        { $match: { ...matchStage, paymentMethod: { $exists: true } } },
        {
          $group: {
            _id: '$paymentMethod',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Top users by transaction volume
      TransactionModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$userId',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { totalAmount: -1 } },
        { $limit: 10 },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' }
      ])
    ]);

    // Format response
    const formattedAnalytics = {
      revenue: {
        totalRevenue: analytics.revenue.totalRevenue / 100,
        totalTransactions: analytics.revenue.totalTransactions,
        avgTransactionAmount: analytics.revenue.avgTransactionAmount / 100,
        totalPlatformFees: analytics.revenue.totalPlatformFees / 100
      },
      categoryBreakdown: analytics.categoryBreakdown.map(item => ({
        category: item._id,
        totalAmount: item.totalAmount / 100,
        count: item.count
      })),
      typeBreakdown: typeBreakdown.map(item => ({
        type: item._id,
        totalAmount: item.totalAmount / 100,
        count: item.count,
        avgAmount: item.avgAmount / 100
      })),
      userTypeBreakdown: userTypeBreakdown.map(item => ({
        userType: item._id,
        totalAmount: item.totalAmount / 100,
        count: item.count
      })),
      paymentMethodBreakdown: paymentMethodBreakdown.map(item => ({
        paymentMethod: item._id,
        totalAmount: item.totalAmount / 100,
        count: item.count
      })),
      topUsers: topUsers.map(item => ({
        userId: item._id,
        user: {
          firstname: item.user.firstname,
          lastname: item.user.lastname,
          email: item.user.email,
          role: item.user.role
        },
        totalAmount: item.totalAmount / 100,
        transactionCount: item.count
      })),
      dailyTrends: analytics.dailyTrends.map(item => ({
        date: item._id.date,
        category: item._id.category,
        totalAmount: item.totalAmount / 100,
        count: item.count
      }))
    };

    res.json({
      success: true,
      data: formattedAnalytics
    });
  } catch (error) {
    console.error("Error fetching transaction analytics:", error);
    createErrorResponse(res, "Failed to fetch transaction analytics", 500);
  }
};

export const bulkUpdateTransactions = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { transactionIds, status, adminNotes } = req.body;

    // Validate input
    if (!Array.isArray(transactionIds) || transactionIds.length === 0) {
      createErrorResponse(res, "Transaction IDs array is required", 400);
      return;
    }

    if (!status) {
      createErrorResponse(res, "Status is required", 400);
      return;
    }

    const validStatuses = ['pending', 'completed', 'failed', 'cancelled', 'processing'];
    if (!validStatuses.includes(status)) {
      createErrorResponse(res, `Invalid status. Must be one of: ${validStatuses.join(', ')}`, 400);
      return;
    }

    // Validate all transaction IDs
    for (const id of transactionIds) {
      if (!Types.ObjectId.isValid(id)) {
        createErrorResponse(res, `Invalid transaction ID format: ${id}`, 400);
        return;
      }
    }

    const results: {
      successful: Array<{ transactionId: any; newStatus: any }>;
      failed: Array<{ transactionId: any; error: string }>;
      totalProcessed: number;
    } = {
      successful: [],
      failed: [],
      totalProcessed: 0
    };

    // Process each transaction
    for (const transactionId of transactionIds) {
      try {
        const transaction = await TransactionModel.findById(transactionId);

        if (!transaction) {
          results.failed.push({
            transactionId,
            error: 'Transaction not found'
          });
          continue;
        }

        // Update transaction
        transaction.status = status;
        if (status === 'completed') {
          transaction.processedAt = new Date();
        } else if (status === 'failed') {
          transaction.failedAt = new Date();
        }

        if (adminNotes) {
          transaction.adminNotes = `${transaction.adminNotes || ''}\nBulk update: ${adminNotes}`.trim();
        }

        await transaction.save();

        results.successful.push({
          transactionId,
          newStatus: status
        });

        results.totalProcessed++;
      } catch (error: any) {
        results.failed.push({
          transactionId,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: `Bulk transaction update completed. ${results.successful.length} successful, ${results.failed.length} failed.`,
      data: results
    });
  } catch (error) {
    console.error("Error in bulk transaction update:", error);
    createErrorResponse(res, "Failed to update transactions", 500);
  }
};

export const getTransactionsByUser = async (req: AuthRequest, res: Response): Promise<void> => {
  const { userId } = req.params;

  try {
    if (!Types.ObjectId.isValid(userId)) {
      createErrorResponse(res, "Invalid user ID format", 400);
      return;
    }

    const {
      page = 1,
      limit = 20,
      type,
      category,
      status,
      startDate,
      endDate
    } = req.query;

    const options = {
      page: Number(page),
      limit: Number(limit),
      type: type as string,
      category: category as string,
      status: status as string,
      startDate: startDate ? new Date(startDate as string) : undefined,
      endDate: endDate ? new Date(endDate as string) : undefined
    };

    const result = await TransactionService.getUserTransactions(
      new Types.ObjectId(userId),
      options
    );

    res.json({
      success: true,
      data: result.transactions,
      summary: result.summary,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: result.total,
        pages: Math.ceil(result.total / Number(limit))
      }
    });
  } catch (error) {
    console.error("Error fetching user transactions:", error);
    createErrorResponse(res, "Failed to fetch user transactions", 500);
  }
};

export const exportTransactions = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const {
      startDate,
      endDate,
      type,
      category,
      status,
      format = 'json'
    } = req.query;

    const query: any = {};
    if (type) query.type = type;
    if (category) query.category = category;
    if (status) query.status = status;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate as string);
      if (endDate) query.createdAt.$lte = new Date(endDate as string);
    }

    const transactions = await TransactionModel.find(query)
      .populate('userId', 'firstname lastname email role')
      .populate('relatedUserId', 'firstname lastname email role')
      .populate('subscription', 'planType monthlyPrice')
      .populate('lesson', 'title scheduledAt')
      .sort({ createdAt: -1 })
      .limit(10000); // Limit for performance

    if (format === 'csv') {
      // Convert to CSV format
      const csvData = transactions.map(t => ({
        id: t._id,
        userId: t.userId._id,
        userEmail: (t.userId as any).email,
        amount: t.amount / 100,
        currency: t.currency,
        type: t.type,
        category: t.category,
        status: t.status,
        paymentMethod: t.paymentMethod,
        description: t.description,
        createdAt: t.createdAt,
        processedAt: t.processedAt
      }));

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=transactions.csv');

      // Simple CSV conversion (in production, use a proper CSV library)
      const csvHeaders = Object.keys(csvData[0] || {}).join(',');
      const csvRows = csvData.map(row => Object.values(row).join(','));
      const csvContent = [csvHeaders, ...csvRows].join('\n');

      res.send(csvContent);
    } else {
      // JSON format
      res.json({
        success: true,
        data: transactions,
        total: transactions.length,
        exportedAt: new Date()
      });
    }
  } catch (error) {
    console.error("Error exporting transactions:", error);
    createErrorResponse(res, "Failed to export transactions", 500);
  }
};
