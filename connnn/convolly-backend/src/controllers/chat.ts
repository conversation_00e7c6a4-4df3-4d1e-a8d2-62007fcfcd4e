import { Request, Response } from "express";
import Message from "../models/message";
import { createErrorResponse } from "../middlewares/errorHandler";
import {
  createOkResponse,
} from "../utils/misc";
import Conversation from "../models/conversation";
import {
  chat<PERSON><PERSON><PERSON>,
  ChatPerformanceMonitor,
  ChatErro<PERSON>,
} from "../utils/chatLogger";
import {
  buildOptimizedMessageQuery,
  buildOptimizedConversationQuery,
  PerformanceThresholds,
} from "../utils/chatOptimizations";
import {
  GetConversationMessagesResponse,
  GetUserConversationsResponse,
  ChatRequest,
} from "../types/chat";

/**
 * @fileoverview Chat controller for handling conversation and message operations
 * @version 1.0.0
 * <AUTHOR> Backend Team
 *
 * This controller provides endpoints for:
 * - Retrieving conversation messages with pagination
 * - Retrieving user conversations with pagination
 * - Optimized database queries for performance
 * - Comprehensive logging and monitoring
 * - Security and authorization checks
 */

/**
 * Default population configuration for conversation queries
 * @deprecated Use optimized aggregation pipelines instead
 */
export const CONVERSATION_POPULATE = [
  "participants.user",
  {
    path: "lastMessage",
    populate: {
      path: "sender",
    },
  },
];

/**
 * Get messages for a specific conversation
 * @param req - Express request object
 * @param res - Express response object
 * @description Retrieves paginated messages for a conversation. User must be a participant.
 * @security Requires authentication and conversation access verification
 */
export const getConversationMessages = async (req: Request, res: Response) => {
  const operation = "GET_CONVERSATION_MESSAGES";
  const operationId = `${operation}_${Date.now()}_${Math.random()}`;
  const context = chatLogger.createContextFromRequest(req, operation);

  // Start performance monitoring
  ChatPerformanceMonitor.startTimer(operationId);

  try {
    const { conversationId } = req.params;

    chatLogger.logDebug(operation, "Starting message retrieval", {
      ...context,
      conversationId,
    });

    // Get pagination parameters (already validated by middleware)
    const {
      limit = "20",
      sortOrder = "asc",
      cursor,
    } = req.query as { limit?: string; sortOrder?: string; cursor?: string };

    const parsedLimit = Math.min(parseInt(limit, 10), PerformanceThresholds.MAX_MESSAGES_PER_REQUEST);

    // Conversation access already verified by middleware
    // Use the conversation from middleware if available
    const conversation = req.conversation || await Conversation.findOne({
      _id: conversationId,
      "participants.user": req.user.id,
    }).select("_id participants").lean(); // Use lean() for better performance

    if (!conversation) {
      const error = ChatErrors.conversationNotFound(conversationId);
      chatLogger.logError(operation, error, context);

      createErrorResponse(res, {
        message: error.message,
        details: error.context
      }, error.statusCode);
      return;
    }

    chatLogger.logDebug(operation, "Conversation access verified", {
      ...context,
      metadata: {
        ...context.metadata,
        participantCount: conversation.participants.length,
      },
    });

    // Use optimized aggregation pipeline for better performance
    const pipeline = buildOptimizedMessageQuery({
      conversationId,
      limit: parsedLimit,
      cursor: cursor as string,
      sortOrder: sortOrder as "asc" | "desc",
      userId: req.user.id,
    });

    const results = await Message.aggregate(pipeline);

    // Check if there are more results
    const hasMore = results.length > parsedLimit;
    if (hasMore) {
      results.pop(); // Remove the extra result
    }

    // Get next cursor
    const nextCursor = hasMore && results.length > 0
      ? results[results.length - 1]._id.toString()
      : null;

    const data = {
      data: results,
      details: {
        pagination: {
          type: "cursor" as const,
          hasMore,
          nextCursor,
        },
      },
    };

    // Log performance metrics
    const duration = ChatPerformanceMonitor.endTimer(operationId);
    ChatPerformanceMonitor.logPerformance(operation, duration, {
      ...context,
      metadata: {
        ...context.metadata,
        messageCount: data.data.length,
        hasMore: data.details.pagination.hasMore,
      },
    });

    // Add metadata to response
    const response = {
      data: data.data,
      details: {
        ...data.details,
        conversationId,
        participantCount: conversation.participants.length,
        requestedBy: req.user.id,
        timestamp: new Date().toISOString(),
      }
    };

    chatLogger.logSuccess(operation, {
      ...context,
      duration,
      metadata: {
        ...context.metadata,
        messageCount: data.data.length,
      },
    });

    createOkResponse(res, response);
  } catch (err: any) {
    const duration = ChatPerformanceMonitor.endTimer(operationId);

    chatLogger.logError(operation, err, {
      ...context,
      duration,
      metadata: {
        ...context.metadata,
        stack: err.stack,
      },
    });

    // Determine error category and appropriate response
    let statusCode = 500;
    let errorMessage = "Failed to retrieve conversation messages";

    if (err.name === 'ValidationError') {
      statusCode = 400;
      errorMessage = "Invalid request parameters";
    } else if (err.name === 'CastError') {
      statusCode = 400;
      errorMessage = "Invalid conversation ID format";
    }

    createErrorResponse(res, {
      message: errorMessage,
      details: {
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
        conversationId: req.params.conversationId,
        timestamp: new Date().toISOString(),
      }
    }, statusCode);
  }
};

/**
 * Get conversations for a specific user
 * @param req - Express request object
 * @param res - Express response object
 * @description Retrieves paginated conversations for a user. Users can only access their own conversations unless they're admin.
 * @security Requires authentication and user profile access verification
 */
export const getUserConversations = async (req: Request, res: Response) => {
  const operation = "GET_USER_CONVERSATIONS";
  const operationId = `${operation}_${Date.now()}_${Math.random()}`;
  const context = chatLogger.createContextFromRequest(req, operation);

  // Start performance monitoring
  ChatPerformanceMonitor.startTimer(operationId);

  try {
    const { userId } = req.params;

    chatLogger.logDebug(operation, "Starting conversation retrieval", {
      ...context,
      metadata: {
        ...context.metadata,
        targetUserId: userId,
        isOwnProfile: req.user.id === userId,
      },
    });

    // Get pagination parameters (already validated by middleware)
    const {
      limit = "20",
      sortOrder = "desc",
      cursor,
    } = req.query as { limit?: string; sortOrder?: string; cursor?: string };

    const parsedLimit = Math.min(parseInt(limit, 10), PerformanceThresholds.MAX_CONVERSATIONS_PER_REQUEST);

    // Use optimized aggregation pipeline for better performance
    const pipeline = buildOptimizedConversationQuery({
      userId,
      limit: parsedLimit,
      cursor: cursor as string,
      sortOrder: sortOrder as "asc" | "desc",
    });

    const results = await Conversation.aggregate(pipeline);

    // Check if there are more results
    const hasMore = results.length > parsedLimit;
    if (hasMore) {
      results.pop(); // Remove the extra result
    }

    // Get next cursor
    const nextCursor = hasMore && results.length > 0
      ? results[results.length - 1].updatedAt.toISOString()
      : null;

    const data = {
      data: results,
      details: {
        pagination: {
          type: "cursor" as const,
          hasMore,
          nextCursor,
        },
      },
    };

    // Serialize conversation data for better client consumption
    // Note: The aggregation pipeline already formats the data optimally
    const serializedData = data.data.map((conversation) => {
      // Convert participants array to object format for easier client consumption
      const participantsObj: any = {};
      conversation.participants.forEach((participant: any) => {
        if (participant.user) {
          participantsObj[participant.user._id] = participant.user;
        }
      });

      // Add additional metadata for each conversation
      return {
        ...conversation,
        participants: participantsObj,
        unreadCount: 0, // TODO: Implement unread message count
        lastActivity: conversation.updatedAt || new Date(),
        isActive: true, // TODO: Implement conversation status
      };
    });

    // Log performance metrics
    const duration = ChatPerformanceMonitor.endTimer(operationId);
    ChatPerformanceMonitor.logPerformance(operation, duration, {
      ...context,
      metadata: {
        ...context.metadata,
        conversationCount: serializedData.length,
        hasMore: data.details.pagination.hasMore,
      },
    });

    const response = {
      ...data,
      data: serializedData,
      details: {
        ...data.details,
        userId,
        totalConversations: serializedData.length,
        requestedBy: req.user.id,
        isOwnProfile: req.user.id === userId,
        timestamp: new Date().toISOString(),
      }
    };

    chatLogger.logSuccess(operation, {
      ...context,
      duration,
      metadata: {
        ...context.metadata,
        conversationCount: serializedData.length,
      },
    });

    createOkResponse(res, response);
  } catch (err: any) {
    const duration = ChatPerformanceMonitor.endTimer(operationId);

    chatLogger.logError(operation, err, {
      ...context,
      duration,
      metadata: {
        ...context.metadata,
        stack: err.stack,
      },
    });

    // Determine error category and appropriate response
    let statusCode = 500;
    let errorMessage = "Failed to retrieve user conversations";

    if (err.name === 'ValidationError') {
      statusCode = 400;
      errorMessage = "Invalid request parameters";
    } else if (err.name === 'CastError') {
      statusCode = 400;
      errorMessage = "Invalid user ID format";
    }

    createErrorResponse(res, {
      message: errorMessage,
      details: {
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
        userId: req.params.userId,
        timestamp: new Date().toISOString(),
      }
    }, statusCode);
  }
};
