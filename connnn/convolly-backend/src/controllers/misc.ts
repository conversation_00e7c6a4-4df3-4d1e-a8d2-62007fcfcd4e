import { Request, Response } from "express";
import { createZendeskTicket } from "../utils/zendesk";
import ComplaintTicket, {
  COMPLAINT_PROIVDERS,
  DEFAULT_COMPLAINT_PROVIDER,
} from "../models/complaint-ticket";
import { createOkResponse } from "../utils/misc";
import { createErrorResponse } from "../middlewares/errorHandler";
import { sendMail } from "../utils/mailer";
import { getProfile, getProfiles } from "../utils/profile";
import {
  EMAIL_TEST_BODY,
  renderEmailTemplate,
} from "../utils/emails-templates/email-templates";

export const submitComplaint = async (req: Request, res: Response) => {
  const { subject, description, email, provider } = req.body;

  if (!subject || !description || !email) {
    createErrorResponse(res, "Missing fields");
    return;
  }

  if (provider && !COMPLAINT_PROIVDERS.includes(provider)) {
    createErrorResponse(res, {
      message: "Invalid compliant provider",
      details: {
        allowedComplaintProviders: COMPLAINT_PROIVDERS,
      },
    });
    return;
  }

  try {
    const zendeskResponse = await createZendeskTicket(
      subject,
      description,
      req.user
    );

    const zendeskTicketId = zendeskResponse.ticket.id;

    const ticket = new ComplaintTicket({
      subject,
      description,
      requesterEmail: email,
      ticketId: zendeskTicketId,
      provider: provider || DEFAULT_COMPLAINT_PROVIDER,
    });

    await ticket.save();

    createOkResponse(res, {
      message: "Ticket created successfully",
      data: {
        ticketId: zendeskTicketId,
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const sendTestMail = async (req: Request, res: Response) => {
  try {
    const { to, html, ...rest } = req.body;

    await sendMail(to, {
      subject: "Convolly Mail service",
      ...rest,
      html: renderEmailTemplate(html || EMAIL_TEST_BODY),
    });

    createOkResponse(res, "Mail sent successfully.");
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getUser = async (req: Request, res: Response) => {
  try {
    const profile = await getProfile(req.body);

    createOkResponse(res, { data: profile });

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const getUsers = async (req: Request, res: Response) => {
  try {
    // const query = {};

    // if (req.body.exclude) query._id = { $nin: req.body.exclude };

    const users = await getProfiles({
      encryptedData: { $exists: true, $ne: null },
    });

    createOkResponse(res, { data: users });
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};
