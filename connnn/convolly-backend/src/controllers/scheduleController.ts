import { Request, Response } from 'express';
import { Calendar } from '../models/calender';
import { Event } from '../models/Event';
import { RecurringEventPattern } from '../models/RecurringEventPattern';
import { getUserCalendars } from '../services/calendarService';
import { canStudentBookSession, consumeLesson } from '../services/subscriptionCheckService';
import { Types } from 'mongoose';
import { getProfile } from '../utils/profile';
import LessonModel from '../models/lesson.model';
import Subscription from '../models/subscription.model';
import { createClassroomHook } from '../hooks/classroom';
import Tutor from '../models/tutor';
import Student from '../models/student';

/**
 * Create a recurring schedule/event
 */
export const createRecurringSchedule = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const {
      calendarId,
      title,
      description,
      location,
      startDateTime,
      endDateTime,
      allDay,
      status,
      visibility,
      priority,
      recurrence
    } = req.body;

    // Validate required fields
    if (!calendarId || !title || !startDateTime || !endDateTime || !recurrence) {
      return res.status(400).json({ 
        message: 'calendarId, title, startDateTime, endDateTime, and recurrence are required.' 
      });
    }

    // Validate calendar ownership
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      return res.status(404).json({ message: 'Calendar not found' });
    }

    if (calendar.ownerUserId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'You do not own this calendar' });
    }

    // Validate dates
    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    if (startDate >= endDate) {
      return res.status(400).json({ message: 'startDateTime must be before endDateTime' });
    }

    // Create the base event
    const event = new Event({
      calendarId,
      title,
      description,
      location,
      startDateTime: startDate,
      endDateTime: endDate,
      allDay: allDay ?? false,
      status: status ?? 'confirmed',
      visibility: visibility ?? 'public',
      priority: priority ?? 3
    });

    await event.save();

    // Create the recurring pattern
    const recurringPattern = new RecurringEventPattern({
      eventId: event._id,
      frequency: recurrence.frequency,
      interval: recurrence.interval || 1,
      daysOfWeek: recurrence.daysOfWeek,
      dayOfMonth: recurrence.dayOfMonth,
      monthOfYear: recurrence.monthOfYear,
      startDate: startDate,
      endDate: recurrence.endDate ? new Date(recurrence.endDate) : undefined,
      occurrenceCount: recurrence.occurrenceCount
    });

    await recurringPattern.save();

    res.status(201).json({
      success: true,
      message: 'Recurring schedule created successfully',
      data: {
        event,
        recurringPattern
      }
    });
  } catch (error: any) {
    console.error('Create recurring schedule error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create recurring schedule',
      error: error.message
    });
  }
};

/**
 * Get user's schedule for a specific date range
 */
export const getUserSchedule = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'startDate and endDate are required' });
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    // Get user's calendars
    const userType = req.user.role === 'tutor' ? 'Tutor' : 'Student';
    const calendars = await getUserCalendars(req.user._id, userType);
    const calendarIds = calendars.map(cal => cal._id);

    // Get events in the date range
    const events = await Event.find({
      calendarId: { $in: calendarIds },
      $or: [
        {
          startDateTime: { $gte: start, $lte: end }
        },
        {
          endDateTime: { $gte: start, $lte: end }
        },
        {
          startDateTime: { $lte: start },
          endDateTime: { $gte: end }
        }
      ]
    }).populate('calendarId').sort({ startDateTime: 1 });

    res.json({
      success: true,
      data: {
        events,
        calendars
      }
    });
  } catch (error: any) {
    console.error('Get user schedule error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user schedule',
      error: error.message
    });
  }
};

/**
 * Get available time slots for a tutor
 */
export const getTutorAvailability = async (req: Request, res: Response): Promise<any> => {
  try {
    const { tutorId } = req.params;
    const { date } = req.query;

    if (!tutorId || !date) {
      return res.status(400).json({ message: 'tutorId and date are required' });
    }

    const targetDate = new Date(date as string);
    if (isNaN(targetDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    // Get tutor's calendars
    const calendars = await getUserCalendars(new Types.ObjectId(tutorId), 'Tutor');
    const calendarIds = calendars.map(cal => cal._id);

    // Get start and end of the day
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    // Get existing events for the day
    const existingEvents = await Event.find({
      calendarId: { $in: calendarIds },
      $or: [
        {
          startDateTime: { $gte: startOfDay, $lte: endOfDay }
        },
        {
          endDateTime: { $gte: startOfDay, $lte: endOfDay }
        },
        {
          startDateTime: { $lte: startOfDay },
          endDateTime: { $gte: endOfDay }
        }
      ]
    }).sort({ startDateTime: 1 });

    // Generate available slots (this is a simplified version)
    // In a real implementation, you'd consider the tutor's working hours
    const availableSlots = [];
    const workingHours = { start: 9, end: 17 }; // 9 AM to 5 PM
    const slotDuration = 60; // 60 minutes

    for (let hour = workingHours.start; hour < workingHours.end; hour++) {
      const slotStart = new Date(targetDate);
      slotStart.setHours(hour, 0, 0, 0);
      
      const slotEnd = new Date(slotStart);
      slotEnd.setMinutes(slotEnd.getMinutes() + slotDuration);

      // Check if this slot conflicts with existing events
      const hasConflict = existingEvents.some(event => {
        return (slotStart < event.endDateTime && slotEnd > event.startDateTime);
      });

      if (!hasConflict) {
        availableSlots.push({
          startTime: slotStart,
          endTime: slotEnd,
          duration: slotDuration
        });
      }
    }

    res.json({
      success: true,
      data: {
        date: targetDate,
        availableSlots,
        existingEvents
      }
    });
  } catch (error: any) {
    console.error('Get tutor availability error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tutor availability',
      error: error.message
    });
  }
};

/**
 * Book a lesson/session with a tutor
 */
export const bookSession = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can book sessions' });
    }

    const {
      tutorId,
      calendarId, // Optional - if not provided, use tutor's first calendar
      studentId, // Optional - if not provided, use authenticated user
      title,
      description,
      startDateTime,
      endDateTime,
      subject
    } = req.body;

    // Determine the actual student ID
    let actualStudentId: Types.ObjectId;
    if (studentId) {
      // If studentId is provided, validate it's a valid ObjectId
      if (!Types.ObjectId.isValid(studentId)) {
        return res.status(400).json({ message: 'Invalid studentId format' });
      }
      actualStudentId = new Types.ObjectId(studentId);
    } else {
      // Use authenticated user as student
      actualStudentId = req.user._id;
    }

    // Check if student can book sessions (subscription or free trial)
    const subscriptionCheck = await canStudentBookSession(
      actualStudentId,
      new Types.ObjectId(tutorId)
    );

    if (!subscriptionCheck.canBook) {
      return res.status(403).json({
        success: false,
        message: 'Cannot book session',
        reason: subscriptionCheck.reason,
        subscriptionStatus: subscriptionCheck.subscriptionStatus,
        freeTrialUsed: subscriptionCheck.freeTrialUsed,
        freeTrialLessonsRemaining: subscriptionCheck.freeTrialLessonsRemaining
      });
    }

    // Validate required fields
    if (!tutorId || !title || !startDateTime || !endDateTime) {
      return res.status(400).json({
        message: 'tutorId, title, startDateTime, and endDateTime are required.'
      });
    }

    // Get or validate tutor calendar
    let calendar;
    if (calendarId) {
      // If calendarId is provided, validate it belongs to tutor
      calendar = await Calendar.findById(calendarId);
      if (!calendar) {
        return res.status(404).json({ message: 'Calendar not found' });
      }

      if (calendar.ownerUserId.toString() !== tutorId || calendar.ownerType !== 'Tutor') {
        return res.status(400).json({ message: 'Invalid calendar for this tutor' });
      }
    } else {
      // If no calendarId provided, use tutor's first calendar
      const tutorCalendars = await getUserCalendars(new Types.ObjectId(tutorId), 'Tutor');
      if (tutorCalendars.length === 0) {
        return res.status(404).json({ message: 'No calendars found for tutor' });
      }
      calendar = tutorCalendars[0];
    }

    // Validate dates
    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    if (startDate >= endDate) {
      return res.status(400).json({ message: 'startDateTime must be before endDateTime' });
    }

    if (startDate <= new Date()) {
      return res.status(400).json({ message: 'Cannot book sessions in the past' });
    }

    // Check for conflicts in tutor's calendar
    const conflictingEvent = await Event.findOne({
      calendarId: calendar._id,
      $or: [
        {
          startDateTime: { $lt: endDate },
          endDateTime: { $gt: startDate }
        }
      ]
    });

    if (conflictingEvent) {
      return res.status(409).json({ message: 'Time slot is not available' });
    }

    // Consume lesson from subscription or mark as free trial
    const isFreeTrial = subscriptionCheck.isFreeTrial || false;
    const consumeResult = await consumeLesson(
      actualStudentId,
      new Types.ObjectId(tutorId),
      isFreeTrial
    );

    if (!consumeResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to process lesson booking',
        error: consumeResult.message
      });
    }

    // Get student and tutor profiles for event descriptions
    const studentProfile = await getProfile({ id: actualStudentId.toString() });
    const tutorProfile = await getProfile({ id: tutorId });

    if (!studentProfile || !tutorProfile) {
      return res.status(404).json({ message: 'Student or tutor profile not found' });
    }

    const studentName = `${studentProfile.firstname} ${studentProfile.lastname}`;
    const tutorName = `${tutorProfile.firstname} ${tutorProfile.lastname}`;

    // Create event in tutor's calendar
    const tutorEvent = new Event({
      calendarId: calendar._id,
      title: `${title} - ${studentName}`,
      description: `${description || ''}\nStudent: ${studentProfile.email}\nSubject: ${subject || 'General'}\nType: ${isFreeTrial ? 'Free Trial' : 'Paid Lesson'}`,
      startDateTime: startDate,
      endDateTime: endDate,
      allDay: false,
      status: 'confirmed',
      visibility: 'private',
      priority: 2
    });

    await tutorEvent.save();

    // Get student's calendars and create event in student's calendar
    const studentCalendars = await getUserCalendars(actualStudentId, 'Student');
    let studentEvent = null;

    if (studentCalendars.length > 0) {
      const studentCalendar = studentCalendars[0]; // Use first available student calendar

      // Check for conflicts in student's calendar
      const studentConflictingEvent = await Event.findOne({
        calendarId: studentCalendar._id,
        $or: [
          {
            startDateTime: { $lt: endDate },
            endDateTime: { $gt: startDate }
          }
        ]
      });

      if (!studentConflictingEvent) {
        // Create event in student's calendar
        studentEvent = new Event({
          calendarId: studentCalendar._id,
          title: `${title} - ${tutorName}`,
          description: `${description || ''}\nTutor: ${tutorProfile.email}\nSubject: ${subject || 'General'}\nType: ${isFreeTrial ? 'Free Trial' : 'Paid Lesson'}`,
          startDateTime: startDate,
          endDateTime: endDate,
          allDay: false,
          status: 'confirmed',
          visibility: 'private',
          priority: 2
        });

        await studentEvent.save();
      }
    }

    // Create lesson record for tracking
    let subscriptionId = null;
    if (!isFreeTrial) {
      const subscription = await Subscription.findOne({
        studentId: actualStudentId,
        tutorId: new Types.ObjectId(tutorId),
        status: 'active'
      });
      subscriptionId = subscription?._id || null;
    }

    const lesson = new LessonModel({
      tutorId: new Types.ObjectId(tutorId),
      studentId: actualStudentId,
      subscriptionId,
      scheduledTime: startDate,
      duration: Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60)), // Convert to minutes
      status: 'scheduled',
      isFreeTrial,
      notes: `Booked via schedule booking: ${title}`
    });

    await lesson.save();

    // Create classroom for the session
    let classroom = null;
    try {
      // Get full tutor and student profiles for classroom creation
      const [tutorProfile, studentProfile] = await Promise.all([
        Tutor.findById(tutorId),
        Student.findById(actualStudentId)
      ]);

      if (tutorProfile && studentProfile) {
        // Create classroom payload
        const classroomPayload = {
          chatGroup: {
            name: `${title} - ${tutorName} & ${studentName}`,
            desc: `Virtual classroom for ${title} session on ${startDate.toLocaleDateString()}`,
            maxUsers: 2
          },
          visibility: 'students-only' as const,
          expireAt: new Date(endDate.getTime() + 24 * 60 * 60 * 1000) // Expire 24 hours after session ends
        };

        // Create classroom using the hook
        classroom = await createClassroomHook(
          classroomPayload,
          tutorProfile,
          studentProfile
        );

        console.log(`Classroom created for session ${lesson._id}: ${classroom._id}`);
      }
    } catch (classroomError: any) {
      console.error('Error creating classroom:', classroomError);
      // Don't fail the booking if classroom creation fails
    }

    res.status(201).json({
      success: true,
      message: studentEvent ? 'Session booked successfully in both calendars' : 'Session booked successfully in tutor calendar',
      data: {
        tutorEvent,
        studentEvent,
        lesson,
        classroom,
        isFreeTrial,
        subscriptionInfo: {
          remainingLessons: subscriptionCheck.remainingLessons,
          freeTrialLessonsRemaining: subscriptionCheck.freeTrialLessonsRemaining
        },
        consumeMessage: consumeResult.message
      }
    });
  } catch (error: any) {
    console.error('Book session error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to book session',
      error: error.message
    });
  }
};
