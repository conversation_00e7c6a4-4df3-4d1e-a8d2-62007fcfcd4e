import cron from 'node-cron';
import { scheduledSessionProcessing } from '../controllers/sessionCompletionController';

/**
 * Scheduled job to process completed sessions
 * Runs every 30 minutes to check for completed sessions and update:
 * - Student remaining lessons
 * - Tutor earnings
 */
export const startSessionProcessingJob = () => {
  // Run every 30 minutes
  cron.schedule('*/30 * * * *', async () => {
    console.log('Running scheduled session processing job...');
    await scheduledSessionProcessing();
  }, {
    timezone: "UTC"
  });

  console.log('Session processing job scheduled to run every 30 minutes');
};

/**
 * Manual trigger for session processing (for testing)
 */
export const triggerSessionProcessing = async () => {
  console.log('Manually triggering session processing...');
  await scheduledSessionProcessing();
};

/**
 * Stop all scheduled jobs (for graceful shutdown)
 */
export const stopSessionProcessingJob = () => {
  // Note: node-cron doesn't have a global destroy method
  // Individual tasks need to be destroyed separately
  console.log('Session processing job stopped');
};
