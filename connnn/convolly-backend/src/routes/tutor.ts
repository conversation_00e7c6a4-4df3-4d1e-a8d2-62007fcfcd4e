import { Router } from "express";
import { withRequestBody } from "../middlewares/misc";
import {
  getOverviewInsights,
  getLessonsInsights,
  getSubscriptionsInsights,
  getTutors,
  // Lesson Management
  getTutorLessons,
  updateLessonStatus,
  // Availability Management
  getTutorCalendars,
  getTutorSchedule,
  createAvailabilitySlot,
  // Student Management
  getTutorStudents,
  getStudentDetails,
  // Earnings Management
  getEarningsBreakdown,
  getEarningsWithPlatformFee,
  getTutorTransactions,
  // Performance Analytics
  getPerformanceMetrics,
  // Review Management
  getTutorReviews,
  reportReview,
  // Profile Management
  updateTutorProfile,
  getTutorCompleteProfile,
} from "../controllers/tutor";
import { deleteProfileHook, getProfileById } from "../hooks/profile";
import Tutor from "../models/tutor";
import { AuthOptions, isAuthenticated } from "../middlewares/auth";

const tutorRouter = Router();

const strictConfig: AuthOptions = {
  role: "tutor",
  withUserId: true,
  strictVerification: true,
};

// ============================================================================
// INSIGHTS ROUTES (existing)
// ============================================================================
const insightRouter = Router({ mergeParams: true });

insightRouter.use(isAuthenticated({ role: "tutor" }));
insightRouter.get("/overview", getOverviewInsights);
insightRouter.get("/lessons", getLessonsInsights);
insightRouter.get("/subscriptions", getSubscriptionsInsights);

tutorRouter.use("/:id/insight", insightRouter);

// ============================================================================
// AUTHENTICATED TUTOR ROUTES (for logged-in tutors managing their own data)
// ============================================================================

// Lesson Management Routes
tutorRouter.get(
  "/my-lessons",
  isAuthenticated({ role: "tutor" }),
  getTutorLessons
);
tutorRouter.patch(
  "/lessons/:lessonId/status",
  withRequestBody(),
  isAuthenticated({ role: "tutor" }),
  updateLessonStatus
);

// Availability Management Routes
tutorRouter.get(
  "/my-calendars",
  isAuthenticated({ role: "tutor" }),
  getTutorCalendars
);
tutorRouter.get(
  "/my-schedule",
  isAuthenticated({ role: "tutor" }),
  getTutorSchedule
);
tutorRouter.post(
  "/availability",
  withRequestBody(),
  isAuthenticated({ role: "tutor" }),
  createAvailabilitySlot
);

// Student Management Routes
tutorRouter.get(
  "/my-students",
  isAuthenticated({ role: "tutor" }),
  getTutorStudents
);
tutorRouter.get(
  "/students/:studentId",
  isAuthenticated({ role: "tutor" }),
  getStudentDetails
);

// Earnings and Financial Routes
tutorRouter.get(
  "/earnings",
  isAuthenticated({ role: "tutor" }),
  getEarningsBreakdown
);
tutorRouter.get(
  "/earnings-detailed",
  isAuthenticated({ role: "tutor" }),
  getEarningsWithPlatformFee
);
tutorRouter.get(
  "/transactions",
  isAuthenticated({ role: "tutor" }),
  getTutorTransactions
);

// Performance Analytics Routes
tutorRouter.get(
  "/performance",
  isAuthenticated({ role: "tutor" }),
  getPerformanceMetrics
);

// Review Management Routes
tutorRouter.get(
  "/my-reviews",
  isAuthenticated({ role: "tutor" }),
  getTutorReviews
);
tutorRouter.get(
  "/reviews",
  isAuthenticated({ role: "tutor" }),
  getTutorReviews
);
tutorRouter.post(
  "/reviews/:reviewId/report",
  withRequestBody(),
  isAuthenticated({ role: "tutor" }),
  reportReview
);

// Profile Management Routes
tutorRouter.get(
  "/my-profile",
  isAuthenticated({ role: "tutor" }),
  getTutorCompleteProfile
);

// ============================================================================
// PUBLIC AND ADMIN ROUTES
// ============================================================================

// Public routes for browsing tutors
tutorRouter.route("/").get(getTutors);

// Admin/Public routes for specific tutor operations
tutorRouter
  .route("/:id")
  .get(getProfileById(Tutor))
  .put(withRequestBody(), isAuthenticated(strictConfig), updateTutorProfile)
  .delete(isAuthenticated(strictConfig), deleteProfileHook);

export default tutorRouter;
