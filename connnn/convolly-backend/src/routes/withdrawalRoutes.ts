import { Router } from 'express';
import {
  createWithdrawalRequest,
  getTutorWithdrawalRequests,
  cancelWithdrawalRequest,
  getWithdrawalRequestById,
  getTutorBalance,
  createRefundRequest,
  getStudentRefundRequests,
  cancelRefundRequest,
  getRefundRequestById
} from '../controllers/withdrawalController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const withdrawalRouter = Router();

// ============================================================================
// TUTOR WITHDRAWAL ROUTES
// ============================================================================

// Get tutor's available balance
withdrawalRouter.get(
  '/balance',
  isAuthenticated({ role: 'tutor' }),
  getTutorBalance
);

// Create a new withdrawal request
withdrawalRouter.post(
  '/request',
  withRequestBody(),
  isAuthenticated({ role: 'tutor' }),
  createWithdrawalRequest
);

// Get tutor's withdrawal requests
withdrawalRouter.get(
  '/my-requests',
  isAuthenticated({ role: 'tutor' }),
  getTutorWithdrawalRequests
);

// Get specific withdrawal request by ID
withdrawalRouter.get(
  '/request/:requestId',
  isAuthenticated({ role: 'tutor' }),
  getWithdrawalRequestById
);

// Cancel a withdrawal request
withdrawalRouter.patch(
  '/request/:requestId/cancel',
  isAuthenticated({ role: 'tutor' }),
  cancelWithdrawalRequest
);

// ============================================================================
// STUDENT REFUND ROUTES
// ============================================================================

// Create a new refund request
withdrawalRouter.post(
  '/refund/request',
  withRequestBody(),
  isAuthenticated({ role: 'student' }),
  createRefundRequest
);

// Get student's refund requests
withdrawalRouter.get(
  '/refund/my-requests',
  isAuthenticated({ role: 'student' }),
  getStudentRefundRequests
);

// Get specific refund request by ID
withdrawalRouter.get(
  '/refund/request/:requestId',
  isAuthenticated({ role: 'student' }),
  getRefundRequestById
);

// Cancel a refund request
withdrawalRouter.patch(
  '/refund/request/:requestId/cancel',
  isAuthenticated({ role: 'student' }),
  cancelRefundRequest
);

export default withdrawalRouter;
