import { Router } from 'express';
import { createCalendar, getTutorCalendars, updateCalendar, deleteCalendar, getUserOwnCalendars } from '../controllers/calendarController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const calender_router = Router();

// Protected routes - require authentication
calender_router.post('/', withRequestBody(), isAuthenticated(), createCalendar);
calender_router.get('/my-calendars', isAuthenticated(), getUserOwnCalendars);
calender_router.get('/tutor/:tutorId', getTutorCalendars); // Public route to view shared tutor calendars
calender_router.put('/:id', withRequestBody(), isAuthenticated(), updateCalendar);
calender_router.delete('/:id', isAuthenticated(), deleteCalendar);

export default calender_router;
