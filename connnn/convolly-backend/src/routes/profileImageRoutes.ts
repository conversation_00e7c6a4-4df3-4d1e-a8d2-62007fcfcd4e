import { Router } from 'express';
import { ProfileImageController } from '../controllers/profileImageController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const profileImageRouter = Router();

// Upload profile image
profileImageRouter.post(
  '/upload',
  withRequestBody(),
  isAuthenticated({ strictVerification: true }),
  ProfileImageController.uploadProfileImage
);

// Get profile image URL
profileImageRouter.get(
  '/',
  isAuthenticated({ strictVerification: true }),
  ProfileImageController.getProfileImage
);

// Delete profile image
profileImageRouter.delete(
  '/',
  isAuthenticated({ strictVerification: true }),
  ProfileImageController.deleteProfileImage
);

export default profileImageRouter;
