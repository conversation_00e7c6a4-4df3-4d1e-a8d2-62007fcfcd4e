import { Router } from "express";
import { isDevMode, withRequestBody } from "../middlewares/misc";
import {
  getUser,
  getUsers,
  sendTestMail,
  submitComplaint,
} from "../controllers/misc";
import { isAuthenticated } from "../middlewares/auth";

const miscRouter = Router();

const devRouter = Router({ mergeParams: true });

devRouter.use(isDevMode);

devRouter.post("/send-mail", withRequestBody(), sendTestMail);

miscRouter.post("/get-user", withRequestBody(), getUser);

miscRouter.get("/chat-users", getUsers);

miscRouter.use("/dev", devRouter);

miscRouter.post(
  "/create-ticket",
  withRequestBody(),
  isAuthenticated(),
  submitComplaint
);

export default miscRouter;
