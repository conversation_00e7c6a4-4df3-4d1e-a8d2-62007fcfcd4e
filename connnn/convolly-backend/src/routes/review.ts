import { NextFunction, Request, Response, Router } from "express";
import { validateId, withRequestBody } from "../middlewares/misc";
import {
  createReview,
  deleteReview,
  getReviewsByMe,
  getReviewsOnMe,
  updateReview,
} from "../controllers/review";
import { isAuthenticated } from "../middlewares/auth";
import { Review } from "../models/review";
import { createErrorResponse } from "../middlewares/errorHandler";

const reviewRouter = Router();

const strictConfig = { withUserId: true, strictVerification: true };

const getReview = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const review = await Review.findById(req.params.reviewId);

    if (!review) {
      createErrorResponse(res, "Review not found", 404);
      return;
    }

    req.review = review;

    next();
  } catch (err: any) {
    next(err);
  }
};

const isCreatedByMe = [
  getReview,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (req.review?.createdBy.toString() !== req.user.id) {
        createErrorResponse(res, "Forbidden access", 403);
        return;
      }

      next();
    } catch (err) {
      next(err);
    }
  },
];

reviewRouter
  .post(
    "/create/:userId",
    withRequestBody(),
    isAuthenticated(strictConfig),
    createReview
  )
  .post(
    "/by-me/:userId",
    withRequestBody(),
    isAuthenticated(strictConfig),
    getReviewsByMe
  )
  .post(
    "/on-me/:userId",
    withRequestBody(),
    isAuthenticated(strictConfig),
    getReviewsOnMe
  )
  .put(
    "/:reviewId",
    withRequestBody(),
    validateId("reviewId"),
    isAuthenticated(),
    isCreatedByMe,
    updateReview
  )
  .delete(
    "/:reviewId",
    validateId("reviewId"),
    isAuthenticated(),
    isCreatedByMe,
    deleteReview
  );

export default reviewRouter;
