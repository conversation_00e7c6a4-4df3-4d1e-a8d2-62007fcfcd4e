import { Router } from 'express';
import {
  checkBookingEligibility,
  getSubscriptionSummary,
  checkGeneralBookingEligibility,
  getBookingRequirements
} from '../controllers/subscriptionStatusController';
import { isAuthenticated } from '../middlewares/auth';

const subscriptionStatusRouter = Router();

// ============================================================================
// STUDENT SUBSCRIPTION STATUS ROUTES
// ============================================================================

// Check if student can book with a specific tutor
subscriptionStatusRouter.get(
  '/check-eligibility/:tutorId',
  isAuthenticated({ role: 'student' }),
  checkBookingEligibility
);

// Get student's complete subscription summary
subscriptionStatusRouter.get(
  '/summary',
  isAuthenticated({ role: 'student' }),
  getSubscriptionSummary
);

// Check general booking eligibility (can book with any tutor)
subscriptionStatusRouter.get(
  '/can-book',
  isAuthenticated({ role: 'student' }),
  checkGeneralBookingEligibility
);

// Get booking requirements and pricing info (public route)
subscriptionStatusRouter.get(
  '/requirements',
  getBookingRequirements
);

export default subscriptionStatusRouter;
