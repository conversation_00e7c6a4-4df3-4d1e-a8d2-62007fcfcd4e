import express from "express";
import { withRequestBody } from "../middlewares/misc";
import {
  forgotPassword,
  login,
  logout,
  register,
  resetPassword,
  verifyOTP,
} from "../controllers/auth";
import { isAuthenticated, verifyJWT } from "../middlewares/auth";

const authRoutes = express.Router();

authRoutes.post("/register", withRequestBody(), register);

authRoutes.post("/login", withRequestBody(), login);

authRoutes.post("/logout", withRequestBody(), isAuthenticated(), logout);

authRoutes.post("/forgot-password", withRequestBody(), forgotPassword);

authRoutes.post("/forgot-password/verify-otp", withRequestBody(), verifyOTP);

authRoutes.post("/reset-password", verifyJWT, withRequestBody(), resetPassword);

export default authRoutes;
