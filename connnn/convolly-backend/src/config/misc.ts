import { v2 as cloudinary } from "cloudinary";

cloudinary.config({
  cloud_name: "dozjfvgjl",
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export default cloudinary;

export const CORS_CONFIG = {
  origin: "*", // IS_PROD ? [SERVER_ORIGIN, CLIENT_ORIGIN] : "*",
  // methods: ["GET", "POST", "PUT", "DELETE"],
  // allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};
