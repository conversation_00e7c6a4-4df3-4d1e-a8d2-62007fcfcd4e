import { CONVERSATION_POPULATE } from "../../controllers/chat";
import { getErrorResponse } from "../../middlewares/errorHandler";
import Conversation, { IConversation } from "../../models/conversation";
import Message from "../../models/message";
import { PROFILE_REGISTRATION_ROLES } from "../../models/profile";
import { KeyValuePair } from "../../types/misc";
import { normalizeDatetime } from "../../utils/datetime";
import { capitalize, serializeConversation } from "../../utils/misc";
import {
  handleRegisterRoomSocketData,
  handleReJoinRoom,
  handleUnRegisterSocket,
  safelyBind,
  unBindEvents,
} from "../../utils/socket-cache";
import { isValidObjectId } from "../../utils/validation";
import { SOCKET_ERROR_KEY } from "./server-socket";

const STATUS_CODE = "PRIVATE_CHAT_ERROR";

const createChatSocket = (socket: any, io: any) => {
  const suffixRoomKey = "private-chat-message";

  const userId = socket.data.user.id;

  const userRoomKey = suffixRoomKey + userId;

  const roomKey = suffixRoomKey + socket.id;

  const populate = [
    "sender",
    {
      path: "conversation",
      populate: CONVERSATION_POPULATE,
    },
  ];

  const updateMessage = (eventName: string, updates: () => KeyValuePair) => {
    return async (messageId: string) => {
      if (!isValidObjectId(messageId)) {
        socket.emit(
          SOCKET_ERROR_KEY,
          getErrorResponse(
            {
              message: "Invalid message id",
              name: STATUS_CODE,
              details: {
                reason: eventName,
              },
            },
            400
          )
        );
        return;
      }

      const message = await Message.findByIdAndUpdate(messageId, updates(), {
        new: true,
        runValidators: true,
      });

      if (!message) {
        socket.emit(
          SOCKET_ERROR_KEY,
          getErrorResponse(
            {
              message: "Message not found",
              name: SOCKET_ERROR_KEY,
            },
            404
          )
        );
        return;
      }

      io.to(suffixRoomKey + message.sender.toString()).emit(
        eventName,
        await message.populate(populate)
      );
    };
  };

  const subHandlers = {
    sendMessage: {
      event: "send-chat-message",
      handler: async (data: any) => {
        try {
          let { sender, receiver, recipients, conversationId, tempId, files } =
            data;

          if (
            !PROFILE_REGISTRATION_ROLES.includes(sender?.role) ||
            !PROFILE_REGISTRATION_ROLES.includes(receiver?.role)
          ) {
            socket.emit(
              SOCKET_ERROR_KEY,
              getErrorResponse(
                {
                  message: `Sender or Receiver role is invalid`,
                  name: STATUS_CODE,
                  details: {
                    allowedRoles: PROFILE_REGISTRATION_ROLES,
                  },
                },
                400
              )
            );
            return;
          }

          if (!isValidObjectId(sender?.id) || !isValidObjectId(receiver?.id)) {
            socket.emit(
              SOCKET_ERROR_KEY,
              getErrorResponse({
                message: "Sender or Receiver id is invalid",
                name: STATUS_CODE,
              }),
              400
            );
            return;
          }

          if (!recipients && !files) {
            socket.emit(
              SOCKET_ERROR_KEY,
              getErrorResponse(
                {
                  name: STATUS_CODE,
                  message:
                    "Invalid body, recipients message or file uploads is required",
                },
                400
              )
            );
            return;
          }

          const participants = [
            { userModel: capitalize(sender.role), user: sender.id },
            { userModel: capitalize(receiver.role), user: receiver.id },
          ];

          if (!conversationId) {
            const conversation = await Conversation.create({
              participants,
            });

            conversationId = conversation.id;
          }

          const msg = await (
            await Message.create({
              recipients,
              files,
              conversation: conversationId,
              sender: sender.id,
              senderModel: capitalize(sender.role),
            })
          ).populate(populate);

          const conversations = await Conversation.findByIdAndUpdate(
            conversationId,
            {
              lastMessage: msg.id,
            },
            { new: true, runValidators: true }
          );

          if (!conversations) {
            socket.emit(
              SOCKET_ERROR_KEY,
              getErrorResponse(
                {
                  message: "Conversation record not found",
                  name: STATUS_CODE,
                },
                404
              )
            );

            await Message.deleteMany({ conversation: conversationId });

            return;
          }

          const newMessage = {
            ...msg.toObject(),
            recipients: Object.fromEntries(msg.recipients as any),
            conversation: serializeConversation(
              msg.conversation as IConversation
            ),
          };

          socket.emit("chat-message-saved", { tempId, message: newMessage });

          io.to(suffixRoomKey + receiver.id).emit(
            "new-chat-message",
            newMessage
          );
        } catch (err: any) {
          socket.emit(
            SOCKET_ERROR_KEY,
            getErrorResponse(
              {
                message: err.message,
                name: STATUS_CODE,
              },
              500
            )
          );
        }
      },
    },
    readMessage: {
      event: "chat-message-read",
      handler: updateMessage("chat-message-read", () => ({
        readAt: normalizeDatetime(),
      })),
    },
    messageDelivered: {
      event: "chat-message-delivered",
      handler: updateMessage("chat-message-delivered", () => ({
        deliveredAt: normalizeDatetime(),
      })),
    },
    typing: {
      event: "chat-user-typing",
      handler: (to: string, from: KeyValuePair) => {
        io.to(suffixRoomKey + to).emit("chat-user-typing", from);
      },
    },
    stoppedTyping: {
      event: "chat-user-stopped-typing",
      handler: (to: string, from: KeyValuePair) => {
        io.to(suffixRoomKey + to).emit("chat-user-stopped-typing", from);
      },
    },
  };

  const onJoinChatRoom = () => {
    const { hasErrors } = handleRegisterRoomSocketData(
      socket,
      roomKey,
      userRoomKey
    );

    if (hasErrors) return;

    safelyBind(
      socket,
      subHandlers.sendMessage.event,
      subHandlers.sendMessage.handler
    );

    safelyBind(
      socket,
      subHandlers.readMessage.event,
      subHandlers.readMessage.handler
    );

    safelyBind(
      socket,
      subHandlers.messageDelivered.event,
      subHandlers.messageDelivered.handler
    );

    safelyBind(socket, subHandlers.typing.event, subHandlers.typing.handler);

    safelyBind(
      socket,
      subHandlers.stoppedTyping.event,
      subHandlers.stoppedTyping.handler
    );
  };

  const handlers = {
    ...subHandlers,
    join: {
      event: "join-chat-room",
      handler: onJoinChatRoom,
    },
  };

  const onLeave = () => {
    handleUnRegisterSocket(socket, roomKey, userRoomKey);
    unBindEvents(socket, handlers);
  };

  handleReJoinRoom(userRoomKey, onJoinChatRoom);

  safelyBind(socket, handlers.join.event, handlers.join.handler);

  return { onLeave, handlers };
};

export default createChatSocket;
