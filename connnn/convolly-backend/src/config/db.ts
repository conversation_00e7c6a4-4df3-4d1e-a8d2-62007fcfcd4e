import mongoose from "mongoose";
import { getErrorResponse } from "../middlewares/errorHandler";
import { IS_PROD } from "./constants";

const connectDB = async () => {
  try {
    await mongoose.connect(
      (IS_PROD ? process.env.MONGO_PROD_URI : process.env.MONGO_DEV_URI)!
    );

    console.log("🟢 MongoDB connected");
  } catch (err: any) {
    console.log("🔴 DB Connection Error:");

    getErrorResponse(err, 500);

    process.exit(1);
  }
};

export default connectDB;
