{"info": {"name": "Convolly Backend - Payout Testing", "description": "Comprehensive collection for testing payout and withdrawal functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "tutor_token", "value": "", "type": "string"}, {"key": "tutor_id", "value": "", "type": "string"}, {"key": "withdrawal_request_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('admin_token', response.token);", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('tutor_token', response.token);", "    pm.collectionVariables.set('tutor_id', response.user._id);", "}"]}}]}]}, {"name": "Tu<PERSON> Requests", "item": [{"name": "Get <PERSON><PERSON> Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/withdrawals/balance", "host": ["{{base_url}}"], "path": ["withdrawals", "balance"]}}}, {"name": "Create <PERSON><PERSON>wal Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"requestedAmount\": 100.00,\n  \"bankDetails\": {\n    \"accountHolderName\": \"<PERSON>\",\n    \"bankName\": \"Chase Bank\",\n    \"accountNumber\": \"**********\",\n    \"routingNumber\": \"*********\",\n    \"accountType\": \"checking\"\n  },\n  \"requestReason\": \"Monthly withdrawal for completed lessons\"\n}"}, "url": {"raw": "{{base_url}}/withdrawals/request", "host": ["{{base_url}}"], "path": ["withdrawals", "request"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('withdrawal_request_id', response.data._id);", "}"]}}]}, {"name": "Get My Withdrawal Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/withdrawals/my-requests?page=1&limit=10", "host": ["{{base_url}}"], "path": ["withdrawals", "my-requests"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Withdrawal Request by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/withdrawals/request/{{withdrawal_request_id}}", "host": ["{{base_url}}"], "path": ["withdrawals", "request", "{{withdrawal_request_id}}"]}}}, {"name": "Cancel Withdrawal Request", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{tutor_token}}"}], "url": {"raw": "{{base_url}}/withdrawals/request/{{withdrawal_request_id}}/cancel", "host": ["{{base_url}}"], "path": ["withdrawals", "request", "{{withdrawal_request_id}}", "cancel"]}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All Withdrawal Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/withdrawals?page=1&limit=10&status=pending", "host": ["{{base_url}}"], "path": ["admin", "withdrawals"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "pending"}]}}}, {"name": "Get Withdrawal Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/withdrawals/statistics", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "statistics"]}}}, {"name": "Get Withdrawal Request by ID (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}"]}}}, {"name": "Approve Withdrawal Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"adminNotes\": \"Approved after verification of tutor balance and bank details\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}/approve", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}", "approve"]}}}, {"name": "Reject Withdrawal Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"rejectionReason\": \"Insufficient balance or invalid bank details\",\n  \"adminNotes\": \"Bank account verification failed\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}/reject", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}", "reject"]}}}, {"name": "Process Withdrawal Request (Legacy)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"stripeTransferId\": \"tr_**********\",\n  \"adminNotes\": \"Payment processed successfully via Stripe\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}/process", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}", "process"]}}}]}, {"name": "Enhanced Payout Status Management", "item": [{"name": "Update Payout Status - Pending", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"pending\",\n  \"adminNotes\": \"Processing payment through <PERSON>e\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}/status", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}", "status"]}}}, {"name": "Update Payout Status - Failed", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"failed\",\n  \"failureReason\": \"Bank account verification failed\",\n  \"stripeTransferId\": \"tr_failed_**********\",\n  \"adminNotes\": \"Payment failed due to invalid bank details\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}/status", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}", "status"]}}}, {"name": "Update Payout Status - Paid", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"paid\",\n  \"stripeTransferId\": \"tr_success_**********\",\n  \"adminNotes\": \"Payment completed successfully\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/{{withdrawal_request_id}}/status", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "{{withdrawal_request_id}}", "status"]}}}, {"name": "Bulk Update Payout Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"requestIds\": [\"{{withdrawal_request_id}}\"],\n  \"status\": \"paid\",\n  \"adminNotes\": \"Bulk payment processing completed\"\n}"}, "url": {"raw": "{{base_url}}/admin/withdrawals/bulk-status-update", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "bulk-status-update"]}}}, {"name": "Get Payout Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/withdrawals/payout-analytics?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{base_url}}"], "path": ["admin", "withdrawals", "payout-analytics"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}]}}}]}, {"name": "Financial Management", "item": [{"name": "Get Financial Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/financial/overview", "host": ["{{base_url}}"], "path": ["admin", "financial", "overview"]}}}, {"name": "Get All Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/financial/transactions?page=1&limit=20&type=lesson_payout&status=completed", "host": ["{{base_url}}"], "path": ["admin", "financial", "transactions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "type", "value": "lesson_payout"}, {"key": "status", "value": "completed"}]}}}, {"name": "Get Tutor Payout Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/financial/tutor-payouts?page=1&limit=20&tutorId={{tutor_id}}", "host": ["{{base_url}}"], "path": ["admin", "financial", "tutor-payouts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "tutorId", "value": "{{tutor_id}}"}]}}}, {"name": "Get Escrow Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/admin/financial/escrow?page=1&limit=20&status=held", "host": ["{{base_url}}"], "path": ["admin", "financial", "escrow"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "status", "value": "held"}]}}}, {"name": "Release Escrow Funds", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"<PERSON><PERSON> completed successfully, releasing funds to tutor\"\n}"}, "url": {"raw": "{{base_url}}/admin/financial/escrow/{{escrow_id}}/release", "host": ["{{base_url}}"], "path": ["admin", "financial", "escrow", "{{escrow_id}}", "release"]}}}]}]}