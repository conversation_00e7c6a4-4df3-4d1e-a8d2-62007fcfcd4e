{"info": {"name": "Calendar & Schedule API Tests", "description": "Test collection for calendar and schedule features with automatic calendar creation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000/api", "type": "string"}, {"key": "tutorToken", "value": "", "type": "string"}, {"key": "studentToken", "value": "", "type": "string"}, {"key": "tutorId", "value": "", "type": "string"}, {"key": "studentId", "value": "", "type": "string"}, {"key": "calendarId", "value": "", "type": "string"}, {"key": "eventId", "value": "", "type": "string"}], "item": [{"name": "1. Authentication", "item": [{"name": "Register Tutor (Auto-creates calendars)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('tutorId', response.data._id);", "    pm.test('<PERSON><PERSON> registered successfully', () => {", "        pm.expect(response.success).to.be.true;", "        pm.expect(response.data._id).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstname\": \"<PERSON>\",\n  \"lastname\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"phoneno\": \"+1234567890\",\n  \"role\": \"tutor\",\n  \"teachingSubjects\": [\n    {\n      \"title\": \"Mathematics\",\n      \"experienceLevel\": \"Expert\",\n      \"qualities\": [\"Problem Solving\", \"Algebra\"],\n      \"specialities\": [\"Calculus\", \"Statistics\"]\n    },\n    {\n      \"title\": \"Physics\",\n      \"experienceLevel\": \"Advanced\",\n      \"qualities\": [\"Mechanics\", \"Thermodynamics\"],\n      \"specialities\": [\"Quantum Physics\"]\n    }\n  ],\n  \"teachingExperience\": \"5 years of teaching experience\",\n  \"motivatePotentialStudent\": \"I help students achieve their academic goals\",\n  \"headline\": \"Expert Math and Physics Tutor\",\n  \"basePrice\": 50\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "Register Student (Auto-creates calendars)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('studentId', response.data._id);", "    pm.test('Student registered successfully', () => {", "        pm.expect(response.success).to.be.true;", "        pm.expect(response.data._id).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstname\": \"<PERSON>\",\n  \"lastname\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"phoneno\": \"+1234567891\",\n  \"role\": \"student\",\n  \"learningReasons\": [\"Academic improvement\", \"Career advancement\"],\n  \"skillsToImprove\": [\"Mathematics\", \"Problem solving\"],\n  \"needIndustryKnowledge\": true,\n  \"preferredLessonDuration\": \"60 minutes\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('tutorToken', response.token);", "    pm.test('<PERSON><PERSON> login successful', () => {", "        pm.expect(response.success).to.be.true;", "        pm.expect(response.token).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Login Student", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('studentToken', response.token);", "    pm.test('Student login successful', () => {", "        pm.expect(response.success).to.be.true;", "        pm.expect(response.token).to.exist;", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "2. Calendar Management", "item": [{"name": "Get My Calendars (<PERSON><PERSON>)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.test('Calendars retrieved successfully', () => {", "        pm.expect(response.success).to.be.true;", "        pm.expect(response.data).to.be.an('array');", "        pm.expect(response.data.length).to.be.greaterThan(0);", "    });", "    ", "    // Save first calendar ID for later use", "    if (response.data.length > 0) {", "        pm.collectionVariables.set('calendarId', response.data[0]._id);", "    }", "}"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON>}}"}], "url": {"raw": "{{baseUrl}}/calendars/my-calendars", "host": ["{{baseUrl}}"], "path": ["calendars", "my-calendars"]}}}, {"name": "Get My Calendars (Student)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{studentToken}}"}], "url": {"raw": "{{baseUrl}}/calendars/my-calendars", "host": ["{{baseUrl}}"], "path": ["calendars", "my-calendars"]}}}, {"name": "Create New Calendar", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Advanced Mathematics\",\n  \"description\": \"Calendar for advanced mathematics courses\",\n  \"color\": \"#FF5722\",\n  \"isShared\": true\n}"}, "url": {"raw": "{{baseUrl}}/calendars", "host": ["{{baseUrl}}"], "path": ["calendars"]}}}, {"name": "Get Tutor Shared Calendars", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/calendars/tutor/{{tutorId}}", "host": ["{{baseUrl}}"], "path": ["calendars", "tutor", "{{tutorId}}"]}}}, {"name": "Update Calendar", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{<PERSON><PERSON><PERSON>}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Teaching Schedule\",\n  \"description\": \"Updated description for teaching schedule\",\n  \"color\": \"#9C27B0\",\n  \"isShared\": true\n}"}, "url": {"raw": "{{baseUrl}}/calendars/{{calendarId}}", "host": ["{{baseUrl}}"], "path": ["calendars", "{{calendarId}}"]}}}]}]}